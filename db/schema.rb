# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 20200818174107) do

  create_table "an_cool_gaston_ge3ls_files", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "title"
    t.text     "description",          limit: 65535
    t.string   "content_file_name"
    t.string   "content_content_type"
    t.integer  "content_file_size"
    t.datetime "content_updated_at"
    t.integer  "user_id"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.index ["user_id"], name: "index_an_cool_gaston_ge3ls_files_on_user_id", using: :btree
  end

  create_table "announcements", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "message",                   null: false
    t.boolean  "display",    default: true, null: false
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "barcodes", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "barcodable_type"
    t.integer  "barcodable_id"
    t.string   "content",         null: false
    t.integer  "check_digit",     null: false
    t.datetime "created_at",      null: false
    t.datetime "updated_at",      null: false
    t.index ["barcodable_type", "barcodable_id"], name: "index_barcodes_on_barcodable_type_and_barcodable_id", using: :btree
    t.index ["content", "check_digit"], name: "index_barcodes_on_content_and_check_digit", unique: true, using: :btree
    t.index ["content"], name: "index_barcodes_on_content", unique: true, using: :btree
  end

  create_table "bulk_imports", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string "name"
    t.string "description"
    t.string "category"
  end

  create_table "calendar_events", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name",       null: false
    t.date     "start_time", null: false
    t.date     "end_time"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "characteristics", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "tree_id",                                     null: false
    t.float    "height",                        limit: 24
    t.float    "dbh",                           limit: 24
    t.integer  "age_recorded",                                null: false
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
    t.string   "dbh_position"
    t.float    "height_breeding_value",         limit: 53
    t.float    "dbh_breeding_value",            limit: 53
    t.text     "notes",                         limit: 65535
    t.integer  "status_code",                   limit: 1
    t.string   "condition_code"
    t.integer  "western_gall_rust_six_code",    limit: 1
    t.string   "mountain_pine_beetle_presence", limit: 1
    t.integer  "white_pine_weevil_score_sum"
    t.integer  "white_pine_weevil_presence",    limit: 1
    t.integer  "western_gall_rust_two_code"
    t.float    "height_breeding_value_gxe",     limit: 24
    t.float    "dbh_breeding_value_gxe",        limit: 24
    t.integer  "last_user_updated_id"
    t.index ["last_user_updated_id"], name: "fk_rails_21a8949bf7", using: :btree
    t.index ["tree_id", "age_recorded"], name: "index_characteristics_on_tree_id_and_age_recorded", using: :btree
  end

  create_table "cohorts", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "study_id"
    t.string   "label"
    t.string   "control"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["study_id"], name: "index_cohorts_on_study_id", using: :btree
  end

  create_table "cohorts_trees", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "cohort_id"
    t.integer  "tree_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cohort_id", "tree_id"], name: "index_cohorts_trees_on_cohort_id_and_tree_id", unique: true, using: :btree
    t.index ["cohort_id"], name: "index_cohorts_trees_on_cohort_id", using: :btree
    t.index ["tree_id"], name: "index_cohorts_trees_on_tree_id", using: :btree
  end

  create_table "data_file_types", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.string   "description"
    t.datetime "created_at",  null: false
    t.datetime "updated_at",  null: false
  end

  create_table "data_files", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "measurement_id"
    t.string   "data_file_name"
    t.string   "data_content_type"
    t.integer  "data_file_size"
    t.datetime "data_updated_at"
    t.text     "description",             limit: 65535
    t.boolean  "has_concentrations"
    t.string   "has_concentration_units"
    t.string   "mapping_errors"
    t.integer  "data_file_type_id"
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.index ["data_file_type_id"], name: "index_data_files_on_data_file_type_id", using: :btree
    t.index ["measurement_id"], name: "index_data_files_on_measurement_id", using: :btree
  end

  create_table "davidson_blue_ge3ls_files", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "title"
    t.text     "description",          limit: 65535
    t.string   "content_file_name"
    t.string   "content_content_type"
    t.integer  "content_file_size"
    t.datetime "content_updated_at"
    t.integer  "user_id"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.index ["user_id"], name: "index_davidson_blue_ge3ls_files_on_user_id", using: :btree
  end

  create_table "general_files", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "title"
    t.string   "content_file_name"
    t.string   "content_content_type"
    t.integer  "content_file_size"
    t.datetime "content_updated_at"
    t.integer  "user_id"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.text     "description",          limit: 65535
    t.index ["user_id"], name: "index_general_files_on_user_id", using: :btree
  end

  create_table "grouping_assignments", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "type"
    t.string   "label"
    t.integer  "grouping_id"
    t.string   "assignable_type"
    t.integer  "assignable_id"
    t.datetime "created_at",      null: false
    t.datetime "updated_at",      null: false
    t.index ["assignable_type", "assignable_id"], name: "index_grouping_assignments_on_assignable_type_and_assignable_id", using: :btree
    t.index ["grouping_id"], name: "index_grouping_assignments_on_grouping_id", using: :btree
  end

  create_table "groupings", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "type"
    t.string   "name"
    t.string   "description"
    t.datetime "created_at",  null: false
    t.datetime "updated_at",  null: false
  end

  create_table "measurement_types", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.string   "description"
    t.datetime "created_at",  null: false
    t.datetime "updated_at",  null: false
    t.string   "code"
    t.index ["code"], name: "index_measurement_types_on_code", using: :btree
  end

  create_table "measurement_values", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "measurement_id"
    t.string   "name",           null: false
    t.string   "value",          null: false
    t.datetime "created_at",     null: false
    t.datetime "updated_at",     null: false
    t.index ["measurement_id", "name"], name: "unique_on_all", unique: true, using: :btree
    t.index ["measurement_id"], name: "index_measurement_values_on_measurement_id", using: :btree
    t.index ["name"], name: "index_measurement_values_on_name", using: :btree
  end

  create_table "measurements", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.text     "description",          limit: 65535
    t.date     "perform_on"
    t.integer  "assigned_to_id"
    t.date     "performed_on"
    t.integer  "performed_by_id"
    t.float    "amount_used",          limit: 24
    t.string   "amount_used_unit"
    t.integer  "sample_id"
    t.integer  "protocol_id"
    t.integer  "measurement_type_id"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.integer  "last_user_updated_id"
    t.date     "performed_on_end"
    t.index ["assigned_to_id"], name: "index_measurements_on_assigned_to_id", using: :btree
    t.index ["last_user_updated_id"], name: "fk_rails_756b349621", using: :btree
    t.index ["measurement_type_id"], name: "index_measurements_on_measurement_type_id", using: :btree
    t.index ["performed_by_id"], name: "index_measurements_on_performed_by_id", using: :btree
    t.index ["protocol_id"], name: "index_measurements_on_protocol_id", using: :btree
    t.index ["sample_id", "measurement_type_id"], name: "index_measurements_on_sample_id_and_measurement_type_id", unique: true, using: :btree
    t.index ["sample_id"], name: "index_measurements_on_sample_id", using: :btree
  end

  create_table "parent_trees", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "accession_number"
    t.date     "cone_collected"
    t.string   "location"
    t.float    "latitude",         limit: 24
    t.float    "longitude",        limit: 24
    t.integer  "elevation"
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
    t.integer  "suc_code"
    t.integer  "species_id"
    t.index ["species_id"], name: "index_parent_trees_on_species_id", using: :btree
    t.index ["suc_code"], name: "index_parent_trees_on_suc_code", unique: true, using: :btree
  end

  create_table "pictures", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "content_file_name"
    t.string   "content_content_type"
    t.integer  "content_file_size"
    t.datetime "content_updated_at"
    t.integer  "user_id"
    t.datetime "created_at",           null: false
    t.datetime "updated_at",           null: false
    t.index ["user_id"], name: "index_pictures_on_user_id", using: :btree
  end

  create_table "protocols", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.string   "version"
    t.string   "comments"
    t.string   "storage_file_name"
    t.string   "storage_content_type"
    t.integer  "storage_file_size"
    t.datetime "data_updated_at"
    t.datetime "created_at",           null: false
    t.datetime "updated_at",           null: false
  end

  create_table "samples", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "tree_id",                                                                 null: false
    t.integer  "sample_id"
    t.integer  "collected_by_id"
    t.integer  "site_id"
    t.integer  "status",                                                      default: 0
    t.string   "sample_type",                                                             null: false
    t.decimal  "original_amount",                    precision: 12, scale: 6
    t.string   "original_unit"
    t.decimal  "actual_amount",                      precision: 12, scale: 6
    t.string   "actual_unit"
    t.string   "code",                                                                    null: false
    t.string   "building"
    t.string   "room"
    t.string   "freezer"
    t.string   "shelf"
    t.string   "box"
    t.string   "box_position"
    t.date     "collected_on"
    t.text     "description",          limit: 65535
    t.datetime "created_at",                                                              null: false
    t.datetime "updated_at",                                                              null: false
    t.string   "suffix",                                                                  null: false
    t.integer  "last_user_updated_id"
    t.date     "collected_on_end"
    t.index ["collected_by_id"], name: "fk_rails_8dc5df5928", using: :btree
    t.index ["last_user_updated_id"], name: "fk_rails_53526beaaa", using: :btree
    t.index ["sample_id"], name: "index_samples_on_sample_id", using: :btree
    t.index ["site_id"], name: "index_samples_on_site_id", using: :btree
    t.index ["tree_id", "suffix"], name: "index_samples_on_tree_id_and_suffix", unique: true, using: :btree
    t.index ["tree_id"], name: "index_samples_on_tree_id", using: :btree
  end

  create_table "sites", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.string   "website"
    t.string   "map_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "sources", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name",                              null: false
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.string   "code",       limit: 4, default: "", null: false
    t.index ["code"], name: "index_sources_on_code", using: :btree
    t.index ["name"], name: "index_sources_on_name", unique: true, using: :btree
  end

  create_table "species", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name",                 null: false
    t.datetime "created_at",           null: false
    t.datetime "updated_at",           null: false
    t.string   "code",       limit: 2
    t.index ["name"], name: "index_species_on_name", unique: true, using: :btree
  end

  create_table "stored_files", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "type"
    t.integer  "attachable_id"
    t.string   "attachable_type"
    t.string   "attachment_file_name"
    t.string   "attachment_content_type"
    t.integer  "attachment_file_size"
    t.datetime "attachment_updated_at"
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
    t.index ["attachable_id"], name: "index_stored_files_on_attachable_id", using: :btree
  end

  create_table "studies", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.text     "description", limit: 65535
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "taggings", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "tag_id"
    t.string   "taggable_type"
    t.integer  "taggable_id"
    t.string   "tagger_type"
    t.integer  "tagger_id"
    t.string   "context",       limit: 128
    t.datetime "created_at"
    t.index ["context"], name: "index_taggings_on_context", using: :btree
    t.index ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "taggings_idx", unique: true, using: :btree
    t.index ["tag_id"], name: "index_taggings_on_tag_id", using: :btree
    t.index ["taggable_id", "taggable_type", "context"], name: "index_taggings_on_taggable_id_and_taggable_type_and_context", using: :btree
    t.index ["taggable_id", "taggable_type", "tagger_id", "context"], name: "taggings_idy", using: :btree
    t.index ["taggable_id"], name: "index_taggings_on_taggable_id", using: :btree
    t.index ["taggable_type"], name: "index_taggings_on_taggable_type", using: :btree
    t.index ["tagger_id", "tagger_type"], name: "index_taggings_on_tagger_id_and_tagger_type", using: :btree
    t.index ["tagger_id"], name: "index_taggings_on_tagger_id", using: :btree
  end

  create_table "tags", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string  "name",                       collation: "utf8_bin"
    t.integer "taggings_count", default: 0
    t.index ["name"], name: "index_tags_on_name", unique: true, using: :btree
  end

  create_table "task_categories", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.text     "description", limit: 65535
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "task_priorities", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.text     "description", limit: 65535
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "task_statuses", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tasks", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "subject"
    t.string   "description"
    t.integer  "status_id"
    t.integer  "priority_id"
    t.integer  "assigned_to_id"
    t.integer  "category_id"
    t.date     "start_date"
    t.date     "due_date"
    t.float    "estimated_hours", limit: 24
    t.integer  "done_ratio"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.index ["assigned_to_id"], name: "fk_rails_781b907909", using: :btree
    t.index ["category_id"], name: "fk_rails_acbc5a096e", using: :btree
    t.index ["priority_id"], name: "fk_rails_25bc089f8e", using: :btree
    t.index ["status_id"], name: "fk_rails_dce14077b1", using: :btree
  end

  create_table "trees", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.integer  "source_id",                                          null: false
    t.integer  "code",                                               null: false
    t.text     "notes",                 limit: 65535
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.integer  "species_id",                                         null: false
    t.string   "forward_selected"
    t.integer  "breeding_value_rank",   limit: 1
    t.string   "label_present",         limit: 1
    t.integer  "rep"
    t.integer  "row"
    t.integer  "tier"
    t.integer  "set"
    t.integer  "stake"
    t.integer  "tree"
    t.string   "trial",                 limit: 10
    t.integer  "last_user_updated_id"
    t.boolean  "res_for_tree",                        default: true
    t.integer  "x_coordinate"
    t.integer  "y_coordinate"
    t.integer  "mother_parent_tree_id"
    t.string   "family",                              default: "0"
    t.integer  "father_parent_tree_id"
    t.integer  "suc_code"
    t.string   "treatment"
    t.integer  "block"
    t.index ["code"], name: "index_trees_on_code", using: :btree
    t.index ["father_parent_tree_id"], name: "fk_rails_ea35b9918a", using: :btree
    t.index ["last_user_updated_id"], name: "fk_rails_9bc3ff3181", using: :btree
    t.index ["mother_parent_tree_id"], name: "index_trees_on_mother_parent_tree_id", using: :btree
    t.index ["source_id"], name: "index_trees_on_source_id", using: :btree
    t.index ["species_id"], name: "index_trees_on_species_id", using: :btree
  end

  create_table "users", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "email",                  default: "", null: false
    t.string   "encrypted_password",     default: "", null: false
    t.string   "password_salt"
    t.string   "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer  "failed_attempts",        default: 0
    t.string   "unlock_token"
    t.datetime "locked_at"
    t.integer  "sign_in_count",          default: 0
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string   "current_sign_in_ip"
    t.string   "last_sign_in_ip"
    t.string   "name",                                null: false
    t.integer  "site_id",                             null: false
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
    t.string   "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer  "invitation_limit"
    t.string   "invited_by_type"
    t.integer  "invited_by_id"
    t.integer  "invitations_count",      default: 0
    t.integer  "role",                   default: 0,  null: false
    t.integer  "status",                 default: 1,  null: false
    t.index ["email"], name: "index_users_on_email", unique: true, using: :btree
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true, using: :btree
    t.index ["invitations_count"], name: "index_users_on_invitations_count", using: :btree
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id", using: :btree
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true, using: :btree
    t.index ["site_id"], name: "index_users_on_site_id", using: :btree
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true, using: :btree
  end

  create_table "versions", force: :cascade, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8" do |t|
    t.string   "item_type",      limit: 191,        null: false
    t.integer  "item_id",                           null: false
    t.string   "event",                             null: false
    t.string   "whodunnit"
    t.text     "object",         limit: 4294967295
    t.datetime "created_at"
    t.text     "object_changes", limit: 4294967295
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id", using: :btree
  end

  add_foreign_key "an_cool_gaston_ge3ls_files", "users", on_delete: :nullify
  add_foreign_key "characteristics", "trees", on_delete: :cascade
  add_foreign_key "characteristics", "users", column: "last_user_updated_id", on_delete: :nullify
  add_foreign_key "cohorts", "studies", on_delete: :cascade
  add_foreign_key "davidson_blue_ge3ls_files", "users", on_delete: :nullify
  add_foreign_key "general_files", "users", on_delete: :nullify
  add_foreign_key "measurement_values", "measurements", on_delete: :cascade
  add_foreign_key "measurements", "users", column: "last_user_updated_id", on_delete: :nullify
  add_foreign_key "parent_trees", "species"
  add_foreign_key "pictures", "users", on_delete: :nullify
  add_foreign_key "samples", "samples"
  add_foreign_key "samples", "sites"
  add_foreign_key "samples", "trees"
  add_foreign_key "samples", "users", column: "collected_by_id", on_delete: :nullify
  add_foreign_key "samples", "users", column: "last_user_updated_id", on_delete: :nullify
  add_foreign_key "tasks", "task_categories", column: "category_id"
  add_foreign_key "tasks", "task_priorities", column: "priority_id"
  add_foreign_key "tasks", "task_statuses", column: "status_id"
  add_foreign_key "tasks", "users", column: "assigned_to_id"
  add_foreign_key "trees", "parent_trees", column: "father_parent_tree_id"
  add_foreign_key "trees", "parent_trees", column: "mother_parent_tree_id"
  add_foreign_key "trees", "sources", on_delete: :cascade
  add_foreign_key "trees", "species"
  add_foreign_key "trees", "users", column: "last_user_updated_id", on_delete: :nullify
  add_foreign_key "users", "sites"
end
