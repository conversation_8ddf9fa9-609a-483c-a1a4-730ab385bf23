class CreateParentTree < ActiveRecord::Migration[5.0]
  def change
    create_table :parent_trees do |t|
      t.string :code
      t.date :cone_collected
      t.string :location
      t.float :latitude
      t.float :longitude
      t.integer :elevation
      t.timestamps
    end

    add_index :parent_trees, :code, unique: true

    add_reference :trees, :parent_tree, index: true
    add_foreign_key :trees, :parent_trees
  end
end
