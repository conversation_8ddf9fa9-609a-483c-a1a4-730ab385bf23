class ChangeEvaluations < ActiveRecord::Migration[5.0]
  def change
    remove_column :evaluations, :diagnosis, :string
    remove_column :evaluations, :symptoms, :string
    rename_column :evaluations, :evaluated_on, :age_recorded
    rename_column :evaluations, :weight, :dbh

    change_column :evaluations, :age_recorded, :integer

    add_column :evaluations, :dbh_position, :string
    add_column :evaluations, :height_breeding_value, :double
    add_column :evaluations, :dbh_breeding_value, :double
    add_column :evaluations, :notes, :text
    add_column :evaluations, :status_code, :integer, limit: 1
    add_column :evaluations, :condition_code, :string
    add_column :evaluations, :western_gall_rust_code, :integer, limit: 1
    add_column :evaluations, :mountain_pine_beetle_presence, :string, limit: 1
    add_column :evaluations, :white_pine_weevil_score_sum, :integer
    add_column :evaluations, :white_pine_weevil_presence, :integer, limit: 1
    add_column :evaluations, :last_user_updated, :string

    rename_table :evaluations, :characteristics
  end
end
