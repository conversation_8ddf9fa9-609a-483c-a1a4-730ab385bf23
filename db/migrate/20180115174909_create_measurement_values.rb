class CreateMeasurementValues < ActiveRecord::Migration[5.0]
  def change
    create_table :measurement_values do |t|
      t.belongs_to :measurement, foreign_key: { on_delete: :cascade  }
      t.string :name, null: false
      t.string :value, null: false

      t.timestamps
    end

    add_index :measurement_values, [:measurement_id, :name],
      name: 'unique_on_all', unique: true
    add_index :measurement_values, :name
  end
end
