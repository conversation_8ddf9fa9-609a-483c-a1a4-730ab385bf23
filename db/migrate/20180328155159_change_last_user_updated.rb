class ChangeLastUserUpdated < ActiveRecord::Migration[5.0]
  def change
    remove_column :trees, :last_user_updated, :string
    add_column :trees, :last_user_updated_id, :integer
    add_foreign_key :trees, :users, column: :last_user_updated_id, on_delete: :nullify

    remove_column :characteristics, :last_user_updated, :string
    add_column :characteristics, :last_user_updated_id, :integer
    add_foreign_key :characteristics, :users, column: :last_user_updated_id, on_delete: :nullify

    remove_column :samples, :last_user_updated, :string
    add_column :samples, :last_user_updated_id, :integer
    add_foreign_key :samples, :users, column: :last_user_updated_id, on_delete: :nullify

    remove_column :measurements, :last_user_updated, :string
    add_column :measurements, :last_user_updated_id, :integer
    add_foreign_key :measurements, :users, column: :last_user_updated_id, on_delete: :nullify
  end
end
