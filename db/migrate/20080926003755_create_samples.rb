class CreateSamples < ActiveRecord::Migration[5.0]
  def change
    create_table :samples do |t|
      t.belongs_to :test_subject, foreign_key: { on_delete: :restrict }, null: false
      t.belongs_to :sample, foreign_key: { on_delete: :restrict }
      t.integer :collected_by_id
      t.belongs_to :site, foreign_key: { on_delete: :restrict } # Storage site
      t.integer :status, default: 0
      t.string :sample_type, null: false
      t.decimal :original_amount, precision: 12, scale: 6
      t.string :original_unit
      t.decimal :actual_amount, precision: 12, scale: 6
      t.string :actual_unit
      t.string :barcode
      t.string :building
      t.string :room
      t.string :freezer
      t.string :shelf
      t.string :box
      t.string :box_position
      t.date :collected_on
      t.text :description

      t.timestamps
    end

    add_foreign_key :samples, :users, column: :collected_by_id, on_delete: :nullify
  end
end
