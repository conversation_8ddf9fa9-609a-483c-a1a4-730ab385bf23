class CorrectedPedigrees < ActiveRecord::Migration[5.0]
  def change
    # Trees
    add_column :trees, :father_parent_tree_id, :integer, :references => "parent_tree"
    add_column :trees, :suc_code, :integer
    change_column :trees, :family, :string, :default => 0
    remove_foreign_key :trees, column: :parent_tree_id
    rename_column :trees, :parent_tree_id, :mother_parent_tree_id
    add_foreign_key :trees, :parent_trees, column: :mother_parent_tree_id, primary_key: :id
    add_foreign_key :trees, :parent_trees, column: :father_parent_tree_id, primary_key: :id
    
    # Parent Trees
    rename_column :parent_trees, :code, :accession_number
    change_column :parent_trees, :accession_number, :integer
    add_column :parent_trees, :suc_code, :integer
    add_index :parent_trees, :suc_code, unique: true
    remove_index :parent_trees, :accession_number
  end
end
