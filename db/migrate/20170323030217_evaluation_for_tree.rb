class EvaluationForTree < ActiveRecord::Migration[5.0]
  def change
    remove_column(:test_subject_evaluations, :education)
    remove_column(:test_subject_evaluations, :marital_status)
    remove_column(:test_subject_evaluations, :address)
    remove_column(:test_subject_evaluations, :address_2)
    remove_column(:test_subject_evaluations, :city)
    remove_column(:test_subject_evaluations, :province_state)
    remove_column(:test_subject_evaluations, :country)
    remove_column(:test_subject_evaluations, :postal_zip_code)
    remove_column(:test_subject_evaluations, :current_smoker)
    remove_column(:test_subject_evaluations, :past_smoker)
    remove_column(:test_subject_evaluations, :drinking_weekly)
    remove_column(:test_subject_evaluations, :exercise_weekly)
    remove_column(:test_subject_evaluations, :hours_sleep)
    remove_column(:test_subject_evaluations, :enough_sleep)
    remove_column(:test_subject_evaluations, :father_age)
    remove_column(:test_subject_evaluations, :father_health)
    remove_column(:test_subject_evaluations, :father_death_age)
    remove_column(:test_subject_evaluations, :father_death_cause)
    remove_column(:test_subject_evaluations, :mother_age)
    remove_column(:test_subject_evaluations, :mother_health)
    remove_column(:test_subject_evaluations, :mother_death_age)
    remove_column(:test_subject_evaluations, :mother_death_cause)
    remove_column(:test_subject_evaluations, :num_children)
    remove_column(:test_subject_evaluations, :relative_condition)
    remove_column(:test_subject_evaluations, :relative_relationship)
    remove_column(:test_subject_evaluations, :past_medical)
  end
end
