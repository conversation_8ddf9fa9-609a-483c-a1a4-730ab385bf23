class ChangeTestSubjects < ActiveRecord::Migration[5.0]
  def change
    remove_column :test_subjects, :age, :integer
    remove_column :test_subjects, :species, :string
    rename_column :samples, :test_subject_id, :tree_id
    rename_column :evaluations, :test_subject_id, :tree_id
    rename_column :cohorts_test_subjects, :test_subject_id, :tree_id
    remove_foreign_key :test_subjects, :sites

    change_column :test_subjects, :code, :string, null: false
    change_column :test_subjects, :collection_site_id, :integer, null: false
    change_column :test_subjects, :species_id, :integer, null: false

    add_column :test_subjects, :forward_selected, :string, null: false, limit: 1
    add_column :test_subjects, :family, :string, limit: 4, null: false
    add_column :test_subjects, :breeding_value_code, :integer, limit: 1
    add_column :test_subjects, :label_present, :string, limit: 1
    add_column :test_subjects, :rep, :integer
    add_column :test_subjects, :row, :integer
    add_column :test_subjects, :tier, :integer
    add_column :test_subjects, :set, :integer
    add_column :test_subjects, :stake, :integer
    add_column :test_subjects, :tree, :integer
    add_column :test_subjects, :last_user_updated, :string

    add_foreign_key :test_subjects, :collection_sites, null: false, on_delete: :cascade

    rename_table :test_subjects, :trees
    rename_table :cohorts_test_subjects, :cohorts_trees
  end
end
