# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the rake db:seed (or created alongside the db with db:setup).
#
# Examples:
#
#   cities = City.create([{ :name => 'Chicago' }, { :name => 'Copenhagen' }])
#   Mayor.create(:name => 'Daley', :city => cities.first)

require 'active_record/fixtures'

# Load default data file types
if DataFileType.none?
  ActiveRecord::FixtureSet.
    create_fixtures(File.join(File.dirname(__FILE__), "migrate/defaults" ),
                    :data_file_types)
end

# Load default measurement types
#if MeasurementType.none?
#  ActiveRecord::FixtureSet.
#    create_fixtures(File.join(File.dirname(__FILE__), "migrate/defaults" ),
#                    :measurement_types)
#end

# Create user + defaults
['University of Alberta', 'University of British Columbia', 'University of Calgary'].each do |name|
  Site.find_or_create_by!(name: name)
end

User.create_with(site: Site.first, name: 'SiteAdmin', role: :administrator, password: 'marblerye').
  find_or_create_by!(email: '<EMAIL>')


# Measurement Types Table
measurement_types_list = [
  ['Gas Exchange', 'Photosynthesis, Stomatal Conductance, WUE (Water Use Efficiency)', 'GE'],
  ['Increment Core Analysis', 'Wood Density, Microfibril Angle, C13 - Integrated WUE (Water Use Efficiency)', 'IC'],
  ['Monoterpene Analysis', 'Defenses Study', 'MT'],
  ['Polyphenolic Analysis', 'Defenses Study', 'PP'],
  ['Resin Duct Analysis', 'Defenses Study', 'RD']
]

measurement_types_list.each do |name, description, code|
  MeasurementType.create(name: name, description: description, code: code)
end

# Species Table
sources_list = [
  ['Lodgepole Pine', 'P'],
  ['White Spruce', 'S']
]

sources_list.each do |name, code|
  Species.create(name: name, code: code)
end

# Sources Table
sources_list = [
  ['Calling Lake', 'CALL'],
  ['Carson Lake', 'CARS'],
  ['Judy Creek', 'JUDY'],
  ['Red Earth', 'REDE'],
  ['Swan Hills', 'SWAN'],
  ['Timeau/Swan Hills', 'TIME'],
  ['Virginia Hills', 'VIRG']
]

sources_list.each do |name, code|
  Source.create(name: name, code: code)
end

# Seeds Groupings Table
groupings_list = [
  ['TreeGrouping', 'Field', 'Groups all RES-FOR field trees'],
  ['TreeGrouping', 'Greenhouse', 'Groups all RES-FOR greenhouse trees']
]

groupings_list.each do |type, name, description|
  Grouping.create(type: type, name: name, description: description)
end

# Seeds Bulk Import Table
bulk_import_list = [
  ['trees', 'tree identification data', 'Tree'],
  ['characteristics', 'tree growth data measured at different ages', 'Characteristic'],
  ['measurements/gas_exchange', 'gas exchange measurements recorded by Xiaojing Wei', 'Measurement'],
  ['measurements/monoterpene_analysis', 'monoterpene analysis measurements recorded by Jennifer Klutsch', 'Measurement'],
  ['measurements/polyphenolic_analysis', 'polyphenolic analysis measurements recorded by Jennifer Klutsch', 'Measurement']
]

bulk_import_list.each do |name, description, category|
  BulkImport.create(name: name, description: description, category: category)
end

# Create default task models
if TaskStatus.none?
  ['not started', 'in progress', 'stalled', 'complete'].each do |status|
    TaskStatus.create!(name: status)
  end
end
if TaskPriority.none?
  ['high', 'low'].each do |priority|
    TaskPriority.create!(name: priority)
  end
end
if TaskCategory.none?
  ['equipment', 'meeting', 'grant', 'accounting'].each do |category|
    TaskCategory.create!(name: category)
  end
end
