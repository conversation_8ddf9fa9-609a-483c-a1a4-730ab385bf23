jQuery ->
  $('form').on 'click', '.remove_fields', (event) ->
    $(this).closest('.field').remove()
    event.preventDefault()

  $('form').on 'click', '.add_fields', (event) ->
    time = new Date().getTime()
    regexp = new RegExp($(this).data('id'), 'g')
    $(this).before($(this).data('fields').replace(regexp, time))
    event.preventDefault()

  $('input[id^="selectAll"]').click ->
    selector = @id.substring(10, @id.length)
    if @checked
      $("input[class$=" + selector + "]").each ->
        @checked = true
        return
    else
      $("input[class$=" + selector + "]").each ->
        @checked = false
        return
    return
