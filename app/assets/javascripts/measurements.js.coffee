jQuery ->
  measurement_type = $('.measurement_type_select option:selected').text()

  $('.measurement_data').hide()
  for k, v of gon.measurement_types_hash
    if measurement_type == k
      $('.measurement_data').show()
      $("##{v}").show().siblings().hide()

  $('.measurement_type_select').change ->
    $('.measurement_data').show()
    measurement_type = $('.measurement_type_select option:selected').text()

    for k, v of gon.measurement_types_hash
      if measurement_type == k
        $("##{v}").show().siblings().hide()

  # Measurement types with phases (couldn't do dynamic variables so wrote out each one)
  # Metabolomics
  MB_measurement_phase = $(".MB_measurement_phase_select option:selected").text()
  $('.MB_measurements_show_container').hide()
  if (MB_measurement_phase != "Select phase") && (MB_measurement_phase != "Select field or a greenhouse phase")
    $('.MB_measurements_show_container').show()
    $("##{MB_measurement_phase}_MB_measurements").show().siblings().hide()

  $('.MB_measurement_phase_select').change ->
    $('.MB_measurements_show_container').show()
    MB_measurement_phase = $('.MB_measurement_phase_select option:selected').text()

    if (MB_measurement_phase != "Select phase") && (MB_measurement_phase != "Select field or a greenhouse phase")
      $("##{MB_measurement_phase}_MB_measurements").show().siblings().hide()
    else
      $('.MB_measurements_show_container').hide()

  # Metabolomics (Primary)
  MP_measurement_phase = $(".MP_measurement_phase_select option:selected").text()
  $('.MP_measurements_show_container').hide()
  if (MP_measurement_phase != "Select phase") && (MP_measurement_phase != "Select field or a greenhouse phase")
    $('.MP_measurements_show_container').show()
    $("##{MP_measurement_phase}_MP_measurements").show().siblings().hide()

  $('.MP_measurement_phase_select').change ->
    $('.MP_measurements_show_container').show()
    MP_measurement_phase = $('.MP_measurement_phase_select option:selected').text()

    if (MP_measurement_phase != "Select phase") && (MP_measurement_phase != "Select field or a greenhouse phase")
      $("##{MP_measurement_phase}_MP_measurements").show().siblings().hide()
    else
      $('.MP_measurements_show_container').hide()

  # Volumetric Water Content (GE)
  VG_measurement_phase = $(".VG_measurement_phase_select option:selected").text()
  $('.VG_measurements_show_container').hide()
  if VG_measurement_phase != "Select phase"
    $('.VG_measurements_show_container').show()
    $("##{VG_measurement_phase}_VG_measurements").show().siblings().hide()

  $('.VG_measurement_phase_select').change ->
    $('.VG_measurements_show_container').show()
    VG_measurement_phase = $('.VG_measurement_phase_select option:selected').text()

    if VG_measurement_phase != "Select phase"
      $("##{VG_measurement_phase}_VG_measurements").show().siblings().hide()
    else
      $('.VG_measurements_show_container').hide()
