# Enable select2 on all elements with class select2-basic
$ ->
  $('.select2-basic').select2(theme: "bootstrap")

$ ->
  $('form')
    .bind 'cocoon:after-insert', (e, added) ->
      added.find(".select2-basic").select2(theme: "bootstrap")
    .bind 'cocoon:before-insert', (e, to_be_added) ->
      to_be_added.fadeIn('slow')
    .bind 'cocoon:before-remove', (e, to_be_removed) ->
      to_be_removed.fadeOut('slow')
