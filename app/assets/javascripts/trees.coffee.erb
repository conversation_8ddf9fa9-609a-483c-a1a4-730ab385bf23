$ ->
  if $("#subject-tree").length
    tree = $("#subject-tree")

    tree.on 'ready.jstree', (event, data) ->
      tree.jstree('open_all')
      tree.jstree('select_node', tree.data('selected-node'))

    # jsTree doesn't follow click throughs by default, so here we initiate
    # a click event if the node contains a link that is a real URL
    tree.on 'activate_node.jstree', (e, data) ->
      selected = data.node

      if selected && selected.a_attr['href'] != '#'
        link = document.createElementNS("http://www.w3.org/1999/xhtml", "a")
        link.href = selected.a_attr['href']
        event = new MouseEvent('click', {
            'view': window,
            'bubbles': false,
            'cancelable': true
        })
        link.dispatchEvent(event)

    tree.jstree
      plugins: ['types']
      core:
        multiple: false
        data:
          url: tree.data('source')
      types:
        default:
          icon: '<%= image_path("tree/folder.png") %>'
        subject:
          icon: '<%= image_path("tree/tree.png") %>'
        medications:
          icon: '<%= image_path("tree/medicine.png") %>'
        tests:
          icon: '<%= image_path("tree/test.png") %>'
        characteristic:
          icon: '<%= image_path("tree/characteristic.png") %>'
        measurement:
          icon: '<%= image_path("tree/measurement.png") %>'
        sample:
          icon: '<%= image_path("tree/sample.png") %>'
        sub_sample:
          icon: '<%= image_path("tree/sub.png") %>'
        aliquot:
          icon: '<%= image_path("tree/aliquot.png") %>'
        group:
          icon: '<%= image_path("tree/grouping.png") %>'
