<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="168.999" height="104.747" viewBox="0 0 168.999 104.747">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_3757" data-name="Rectangle 3757" width="168.999" height="104.747" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_21184" data-name="Group 21184" transform="translate(-0.001)">
    <g id="Group_21182" data-name="Group 21182" transform="translate(0.001)" clip-path="url(#clip-path)">
      <path id="Path_19321" data-name="Path 19321" d="M31.571,4.75A5.212,5.212,0,0,0,29.9.452c-.354-.354-.563-.5-.668-.438-.125.062-.146.229-.041.48a3.508,3.508,0,0,1,.062,2.963c-.459.918-1.982,1.711-4.548,2.378-3.234.688-5.592,1.189-7.115,1.5-5.4,1.147-9.3,3.234-11.725,6.217a10.773,10.773,0,0,0-2.336,6.968q0,7.292,8.721,10.536c1.273-.4,3.63-.939,7.031-1.648.167-.063.25-.125.271-.188a.2.2,0,0,0-.167-.25,10.6,10.6,0,0,1-3.651-1.544c-2.733-1.794-4.048-4.089-3.943-6.906a7.378,7.378,0,0,1,2.379-5.216,13.147,13.147,0,0,1,5.32-2.984c1.231-.376,2.941-.834,5.132-1.4A14.688,14.688,0,0,0,28.587,9.34a5.83,5.83,0,0,0,2.984-4.59" transform="translate(-1.666 0)" fill="#8b254f"/>
      <path id="Path_19322" data-name="Path 19322" d="M36.144,74.049c-.272-.272-.438-.376-.543-.334-.146.042-.167.188-.062.459a3.569,3.569,0,0,1-.083,2.984Q34.172,79.41,26.567,80.6C20,81.393,15.01,82.1,11.65,82.749c-4.006.772-7.01,2.107-9.034,3.964A7.427,7.427,0,0,0,.009,92.722a7.834,7.834,0,0,0,3.088,5.8,14.966,14.966,0,0,0,7.448,3.108,23.4,23.4,0,0,0,6.822.355c.272-.042.4-.146.4-.292-.021-.1-.1-.188-.272-.25-1.356-.438-2.4-.792-3.088-1.085Q7.864,97.7,7.958,93.6q.124-4.632,8.3-6.238c1.94-.376,5.216-.939,9.868-1.69a35.909,35.909,0,0,0,8.742-2.5,4.913,4.913,0,0,0,3.171-4.4,5.516,5.516,0,0,0-1.9-4.715" transform="translate(-0.001 -34.77)" fill="#8b254f"/>
      <path id="Path_19323" data-name="Path 19323" d="M263.57,176.876a4.388,4.388,0,0,0-3.735-1.732,4.843,4.843,0,0,0-3.922,1.711,6.535,6.535,0,0,0-1.4,4.382,6.208,6.208,0,0,0,1.46,4.3,5.351,5.351,0,0,0,4.214,1.731,7.735,7.735,0,0,0,3.881-1.106l.125-.229.083-1.294-.188-.1a5.82,5.82,0,0,1-3.8,1.481,3.967,3.967,0,0,1-3.86-2.462,6.061,6.061,0,0,1-.5-2.5h8.575l.125-.125a6.705,6.705,0,0,0-1.064-4.048m-3.8-.625a3.121,3.121,0,0,1,2.8,1.46,4.642,4.642,0,0,1,.585,2.191h-7.115a4.853,4.853,0,0,1,1-2.462,3.449,3.449,0,0,1,2.733-1.189" transform="translate(-120.066 -82.623)" fill="#20285c"/>
      <path id="Path_19324" data-name="Path 19324" d="M241.4,179.181a5.94,5.94,0,0,0,2-4.674,5.5,5.5,0,0,0-1.9-4.486,4.635,4.635,0,0,0-3-1.085,7.2,7.2,0,0,0-2.5.5,5.353,5.353,0,0,0-1.043.584c-.021-2.295-.021-4.673.041-7.114l-.125-.125-1.439.25-.126.167c.084,1.063.167,3.15.23,6.279l.021,11.2.125.125,2.753-.042a7.852,7.852,0,0,0,4.966-1.585m.542-4.611a4.483,4.483,0,0,1-2.065,4.11,6.941,6.941,0,0,1-3.755.876l-1.168-.021v-8.178a10.533,10.533,0,0,1,1.085-.688,5.06,5.06,0,0,1,2.087-.48,3.548,3.548,0,0,1,2.649,1.126,4.539,4.539,0,0,1,1.169,3.255" transform="translate(-110.061 -76.791)" fill="#20285c"/>
      <path id="Path_19325" data-name="Path 19325" d="M291.886,170.007l-.166.125.1,2.8-1.794-.041-.125.124-.042.939.1.146,1.836-.042-.021,6.906a6.5,6.5,0,0,0,.334,2.566,2.272,2.272,0,0,0,2.316,1.148,6.8,6.8,0,0,0,1.21-.125l.167-.209.062-1.022-.187-.1a2.96,2.96,0,0,1-1.043.229c-.9,0-1.377-.563-1.419-1.669l-.021-7.719,2.525.042.167-.1.042-.98-.1-.146-2.608.062.1-3.108-.125-.125Z" transform="translate(-136.74 -80.052)" fill="#20285c"/>
      <path id="Path_19326" data-name="Path 19326" d="M305.1,176.053l-.1.146-.083,1.064.188.1a7.1,7.1,0,0,1,3.213-.877c1.147,0,1.9.354,2.211,1.043a4.768,4.768,0,0,1,.209,1.5v.292l-1.982.292a8.249,8.249,0,0,0-3.9,1.377,3.855,3.855,0,0,0-.083,5.445,3.786,3.786,0,0,0,2.753,1,5.318,5.318,0,0,0,3.213-1.147l.021.792.167.146,1.189-.083.146-.146c-.125-1.816-.167-3.735-.146-5.8v-1.4a16.7,16.7,0,0,0-.083-2.274,2.186,2.186,0,0,0-.564-1.169,3.891,3.891,0,0,0-3.025-1.064,8.689,8.689,0,0,0-3.338.751m0,7.615a2.252,2.252,0,0,1,1.064-1.982,9.618,9.618,0,0,1,3.338-1.043l1.231-.188V185a4.557,4.557,0,0,1-2.962,1.231,2.526,2.526,0,0,1-2.024-.835,2.48,2.48,0,0,1-.646-1.732" transform="translate(-143.262 -82.697)" fill="#20285c"/>
      <path id="Path_19327" data-name="Path 19327" d="M277.489,175.715l-.146.146c.146,1.5.209,3.964.209,7.386,0,1.043-.021,2.316-.063,3.839l.125.146,1.356-.021.146-.146c-.1-2.566-.146-4.4-.146-5.529,0-2.149.293-3.526.9-4.131a1.957,1.957,0,0,1,1.418-.605,1.915,1.915,0,0,1,.939.313l.188-.146.188-1.189-.167-.167a2.853,2.853,0,0,0-1.022-.229,2.343,2.343,0,0,0-2.044,1.189,3.454,3.454,0,0,0-.438.876l.021-1.793-.125-.126Z" transform="translate(-130.835 -82.734)" fill="#20285c"/>
      <path id="Path_19328" data-name="Path 19328" d="M205.476,166.813l-.146.146-3.484,10.536q-.845,2.6-1.753,5.133l.1.208h1.335l.188-.146c.293-1.189.71-2.65,1.231-4.339l.272-.814,6.259-.021.188.542c.4,1.273.855,2.8,1.335,4.611l.146.166,1.523-.042.125-.166c-.668-1.815-1.46-4.152-2.357-6.968l-2.9-8.763-.146-.125Zm-1.878,9.472,2.712-8.659,2.754,8.659Z" transform="translate(-94.393 -78.673)" fill="#20285c"/>
      <path id="Path_19329" data-name="Path 19329" d="M184.953,173.536a5.461,5.461,0,0,0-1.606-.209,5.644,5.644,0,0,0-4.277,1.648,6.322,6.322,0,0,0-1.606,4.569,7.372,7.372,0,0,0,.542,2.963c.939,2.19,2.942,3.3,5.988,3.3a10.457,10.457,0,0,0,3.881-.813l.125-.167.167-2.546-.167-.063a7.663,7.663,0,0,1-3.5.96,3.149,3.149,0,0,1-2.817-1.252,3.532,3.532,0,0,1-.5-1.356l7.281-.146.125-.146a14.134,14.134,0,0,0-.354-3.359,4.349,4.349,0,0,0-3.276-3.38m-3.776,4.632a2.72,2.72,0,0,1,1.085-2.191,1.73,1.73,0,0,1,.9-.229,1.513,1.513,0,0,1,1.439.772,2.894,2.894,0,0,1,.354,1.565Z" transform="translate(-83.717 -81.765)" fill="#20285c"/>
      <path id="Path_19330" data-name="Path 19330" d="M228.4,163.264l-.125-.125-1.418.229-.146.125c.146,2.128.229,5.653.229,10.557V181l.126.146,1.314-.021.146-.146q-.188-4.35-.188-11.642,0-2.315.062-6.071" transform="translate(-106.95 -76.959)" fill="#20285c"/>
      <path id="Path_19331" data-name="Path 19331" d="M31.3,19.831,30.172,19.5v4.819l1.419,1.419A6.934,6.934,0,0,1,33.01,29.95,9.118,9.118,0,0,1,30.986,36.1c-1.773,2.108-4.819,3.631-9.117,4.528C14.964,42.071,10.52,43.2,8.58,44.011A9.586,9.586,0,0,0,4.929,46.6,6.109,6.109,0,0,0,3.4,49.873c-.333,2.838.939,4.987,3.839,6.468a53.621,53.621,0,0,1,8.846-2l1.565-.271c.146-.042.25-.1.271-.209.042-.188-.083-.292-.354-.354-.647-.126-1.147-.23-1.461-.313a10.961,10.961,0,0,1-4.464-1.857,2.725,2.725,0,0,1,.4-4.4,13.8,13.8,0,0,1,4.84-1.69c1.524-.313,3.965-.772,7.364-1.356a40.459,40.459,0,0,0,7.386-2q7.948-3.223,7.948-11.12,0-6.1-5.675-9.66A13.128,13.128,0,0,0,31.3,19.831" transform="translate(-1.582 -9.198)" fill="#20285c"/>
      <path id="Path_19332" data-name="Path 19332" d="M57.537,104.014a9.257,9.257,0,0,0-1.94-5.946,9.5,9.5,0,0,0-2.42-2.149,35.983,35.983,0,0,1-7.782,2.336c-.564.1-1.21.23-1.94.355-.292.062-.4.188-.334.375.042.083.167.146.4.209,1.231.293,1.9.459,1.982.48a8.729,8.729,0,0,1,4.173,2.358,4.629,4.629,0,0,1,1.127,3.15,5.575,5.575,0,0,1-1.357,3.756,13.851,13.851,0,0,1-6.175,3.338,27.072,27.072,0,0,1-5.612,1.023v3.838c6.363-.376,11.2-1.627,14.479-3.755q5.414-3.536,5.4-9.367" transform="translate(-17.763 -45.249)" fill="#20285c"/>
      <path id="Path_19333" data-name="Path 19333" d="M38.232,168.087a9.7,9.7,0,0,0-1.21,4.986,8.61,8.61,0,0,0,1.648,5.466,7.352,7.352,0,0,0,4.048,2.607,12.182,12.182,0,0,0,3.15.417,18.3,18.3,0,0,0,5.571-1.043l.125-.188c-.062-1.44-.083-2.545-.083-3.275v-.543q0-1.752.062-3.567l-.125-.146-4.006.083-.125.146c.146,1.732.209,3.422.188,5.027a7.941,7.941,0,0,1-1.773.251,3.759,3.759,0,0,1-3.525-1.961,6.576,6.576,0,0,1-.773-3.3,6.218,6.218,0,0,1,1.377-4.3,4.565,4.565,0,0,1,3.588-1.482,7.164,7.164,0,0,1,3.964,1.461l.188-.085c.1-1.084.209-2.169.355-3.254l-.125-.146a12.531,12.531,0,0,0-5.007-1.085c-3.484,0-5.988,1.314-7.51,3.922" transform="translate(-17.465 -77.443)" fill="#20285c"/>
      <path id="Path_19334" data-name="Path 19334" d="M140.8,173.856l-.146.166a71.179,71.179,0,0,1,.334,7.49l-.042,3.881.125.146c1.273-.062,2.545-.083,3.8-.083l.147-.125c-.126-2.545-.189-5.362-.209-8.429a2.7,2.7,0,0,1,1.544-.626,1.258,1.258,0,0,1,1.252,1.043,10.735,10.735,0,0,1,.188,2.482c0,1.92-.021,3.777-.062,5.592l.125.167c1.147-.062,2.378-.1,3.734-.1l.146-.125q-.156-2.41-.188-5.508l-.021-3.109a2.874,2.874,0,0,1,1.565-.563c.98,0,1.482,1.043,1.482,3.151l-.021,1.294c-.021.521-.041,2.107-.084,4.8l.146.146c1.127-.062,2.379-.083,3.756-.083l.125-.125c-.083-1.69-.125-3.422-.125-5.237V178.4c.021-1.941-.167-3.213-.605-3.859a3.145,3.145,0,0,0-2.8-1.274,6.963,6.963,0,0,0-3.984,1.461,2.9,2.9,0,0,0-2.671-1.461,6.388,6.388,0,0,0-3.484,1.4v-1.251l-.208-.209c-1.314.251-2.587.459-3.818.647" transform="translate(-66.353 -81.71)" fill="#20285c"/>
      <path id="Path_19335" data-name="Path 19335" d="M124.106,173.805a7.343,7.343,0,0,0-2.734-.438c-4.068,0-6.112,2.086-6.112,6.239a7.2,7.2,0,0,0,1.043,4.13c.939,1.4,2.566,2.087,4.923,2.087,4.027,0,6.03-2.087,6.03-6.259,0-3.025-1.043-4.944-3.15-5.757m-4.465,2.9a1.767,1.767,0,0,1,1.648-1.022,1.688,1.688,0,0,1,1.44.709,5.229,5.229,0,0,1,.647,3.15c0,2.609-.709,3.9-2.107,3.9a1.783,1.783,0,0,1-1.273-.522c-.542-.542-.814-1.648-.814-3.338a6.152,6.152,0,0,1,.459-2.879" transform="translate(-54.373 -81.784)" fill="#20285c"/>
      <path id="Path_19336" data-name="Path 19336" d="M71.067,178.075a2.954,2.954,0,0,1,.354-1.878,4.747,4.747,0,0,1,3.568-2.5,3.6,3.6,0,0,1,1.085-.063c.125,0,.125-.041.021-.1a.591.591,0,0,0-.229-.083,8.726,8.726,0,0,0-2.65-.313,5.578,5.578,0,0,0-4.256,1.69,6.548,6.548,0,0,0-1.606,4.631,7.176,7.176,0,0,0,.522,2.963c.959,2.19,2.962,3.3,5.987,3.3a10.453,10.453,0,0,0,3.881-.813l.125-.167.167-2.546-.167-.063a7.663,7.663,0,0,1-3.5.96,3.148,3.148,0,0,1-2.817-1.252,3.824,3.824,0,0,1-.48-1.356l7.26-.146.125-.146a10.952,10.952,0,0,0-.334-3.172,2.593,2.593,0,0,0-1.21-1.335,3.872,3.872,0,0,0-3.234.021.591.591,0,0,1-.1.083c-.021.021-.021.062.021.1a2.083,2.083,0,0,1,1.231,2.107Z" transform="translate(-31.774 -81.673)" fill="#20285c"/>
      <path id="Path_19337" data-name="Path 19337" d="M101.486,174.628a3.014,3.014,0,0,0-2.8-1.357,7.871,7.871,0,0,0-3.61,1.335l.063-1.251-.23-.146c-1.231.25-2.5.459-3.8.647l-.146.166c.209,2.191.313,4.528.313,7.052l-.042,4.318.146.146c1.064-.063,2.316-.083,3.755-.083l.125-.125q-.157-4.663-.188-8.387a3.506,3.506,0,0,1,1.732-.689c.835,0,1.314.709,1.419,2.128.062,1,.083,2.566.083,4.694l-.042,2.316.146.146c1.315-.062,2.546-.083,3.672-.083l.146-.125c-.062-1.085-.1-2.691-.146-4.819v-1.774c-.021-2.107-.209-3.484-.605-4.109" transform="translate(-42.915 -81.71)" fill="#20285c"/>
    </g>
  </g>
</svg>
