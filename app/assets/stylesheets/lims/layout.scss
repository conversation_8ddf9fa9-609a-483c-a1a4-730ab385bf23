main {
  @extend .container-fluid;
  margin-top: 80px; // accommodate the navbar
}

#main-content {
  margin-top: .5rem;
  &.with-sidebar {
    @extend .col-9;
  }
  &.without-sidebar {
    @extend .col-12;
  }
}

#sidebar-content {
  @extend .col-3;
  #sidebar {
    overflow: hidden;
    white-space: nowrap;
    background: #FCFFF0;
    min-height: 800px;

    h2 {
      @extend .rounded;
      text-shadow: 0px 0px 0.05px white;
      border: 2px solid #CDCDC1;
      background: white;
      font-weight: normal;
      font-size: 15px;
      margin: .5rem;
      margin-bottom: 1rem;
      padding: 0.5em 0.4em;
    }
  }
}

dl.standard {
  @extend .row;
  dt {
    @extend .col-3;
  }
  dd {
    @extend .col-9;
  }
}

.error_notification {
  @extend .alert;
  @extend .alert-danger;
}

table.list {
  @extend .table;
  @extend .table-striped;
  @extend .table-sm;

  thead tr { @extend .table-info; }
  tr.table-search {
    th {
      input, select {
        @extend .form-control;
        @extend .form-control-sm;
      }
    }
    .table-search-actions.btn-group {
      float: right;
    }
  }
}

.user-photo {
  width: 200px;
}

footer {
  margin: 2rem 0;
}

.nested-fields {
  margin: 1rem 0;
}
