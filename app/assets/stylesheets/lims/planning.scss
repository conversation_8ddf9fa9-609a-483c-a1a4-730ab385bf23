#gantt-chart {
  position: relative;
  width: 100%;
}

table#gantt {
  border:0;
  border-collapse: collapse;
  width: 100%;
}

/***** Gantt chart *****/
.gantt_hdr {
  position:absolute;
  top:0;
  height:16px;
  border-top: 1px solid #c0c0c0;
  border-bottom: 1px solid #c0c0c0;
  border-right: 1px solid #c0c0c0;
  text-align: center;
  overflow: hidden;
  font-size: 12px;
  &.gantt_day {
    font-size: 10px;
  }
}

.task {
  position: absolute;
  height:8px;
  font-size:0.8em;
  color:#888;
  padding:0;
  margin:0;
  line-height:0.8em;
}

.task_late { background:#f66 image_url('icons/task_late.png'); border: 1px solid #f66; }
.task_done { background:#66f image_url('icons/task_done.png'); border: 1px solid #66f; }
.task_todo { background:#aaa image_url('icons/task_todo.png'); border: 1px solid #aaa; }
.milestone { background-image:image_url('icons/milestone.png'); background-repeat: no-repeat; border: 0; }


/***** Gantt Chart Tooltips ******/
.task-tooltip {position:relative;z-index:24;}
.task-tooltip:hover {z-index:25;color:#000;}
.task-tooltip span.tip {display: none; text-align:left;}

div.task-tooltip:hover span.tip {
  display:block;
  position:absolute;
  top:12px; left:24px; width:270px;
  border:1px solid #555;
  background-color:#fff;
  padding: 4px;
  font-size: 0.8em;
  color:#505050;
}


/***** Calendar *****/
table.cal { border-collapse: collapse; width: 100%; margin: 0px 0 6px 0;border: 1px solid #d7d7d7; }
table.cal thead th {width: 14%;}
table.cal tbody tr {height: 100px;}
table.cal th { background-color:#EEEEEE; padding: 4px; }
table.cal td {border: 1px solid #d7d7d7; vertical-align: top; font-size: 0.9em;}
table.cal td p.day-num {font-size: 1.1em; text-align:right;}
table.cal td.odd p.day-num {color: #bbb;}
table.cal td.today {background:#ffffdd;}
table.cal td.today p.day-num {font-weight: bold;}
