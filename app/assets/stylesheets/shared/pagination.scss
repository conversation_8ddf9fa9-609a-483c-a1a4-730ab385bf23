/* Pagination style */
.pagination {
  background: #f1f1f1;
  border: 1px solid #e5e5e5;
  text-align: center;
  padding: 1em;
  display: inline-block;
  margin-bottom: 1em;
  cursor: default; }
  .pagination a, .pagination span {
    padding: 0.2em 0.3em; }
  .pagination .disabled {
    color: #aaaaaa; }
  .pagination .current {
    font-style: normal;
    font-weight: bold;
    background-color: #bebebe;
    display: inline-block;
    width: 1.4em;
    height: 1.4em;
    line-height: 1.5;
    -moz-border-radius: 1em;
    -webkit-border-radius: 1em;
    border-radius: 1em;
    text-shadow: rgba(255, 255, 255, 0.8) 1px 1px 1px; }
  .pagination a {
    text-decoration: none;
    color: black; }
    .pagination a:hover, .pagination a:focus {
      text-decoration: underline; }

/* Old Pagination Style */

/*      .pagination {
*    background: white;
*
*      margin-top: 1em; */
      /* self-clearing method:  } */
/*      .pagination a, .pagination span {
*        padding: .2em .5em;
*        display: block;
*        float: left;
*        margin-right: 1px; }
*      .pagination span.disabled {
*        color: #999;
*        border: 1px solid #DDD; }
*      .pagination span.current {
*        font-weight: bold;
*        background: #2E6AB1;
*        color: white;
*        border: 1px solid #2E6AB1; }
*      .pagination a {
*        text-decoration: none;
*       color: #105CB6;
*        border: 1px solid #9AAFE5; }
*        .pagination a:hover, .pagination a:focus {
*          color: #003;
*          border-color: #003; }
*      .pagination .page_info {
*        background: #DCDCDC;
*        color: green;
*        padding: .4em .6em;
*        width: 32em;
*        margin-bottom: .3em;
*        text-align: left; }
*        .pagination .page_info b {
*          color: #003;
*          padding: .1em .25em; }
*      .pagination:after {
*        content: ".";
*        display: block;
*        height: 0;
*        clear: both;
*        visibility: hidden; }
*       html .pagination {
*        height: 1%; }
      *:first-child+html .pagination {
*        overflow: hidden; }
*/
