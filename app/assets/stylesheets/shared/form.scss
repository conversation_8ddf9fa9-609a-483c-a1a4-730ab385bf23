div.error-explanation {
  @extend .alert;
  @extend .alert-danger;
  h2 { font-size: 17px }
  ul { margin: 0.8em; }
}

form.standard {
  .form-actions {
    padding-top: .8em;
    margin-bottom: 1em;
    margin-top: .8em;
    border-top: 1px solid white;
    a, input, button {
      @extend .btn;
      @extend .btn-primary;
      margin-right: 0.5em;
    }
  }
  fieldset {
    @extend .form-group;
    @extend .rounded;
    padding: 1rem;
    background: #E0EBEA;
    margin-top: 1em;
    margin-bottom: 1em;
  }
  legend {
    font-size: 18px;
    background: white;
    display: inline-block;
    border: 1px solid silver;
    padding: 0 0.5em;
    border-radius: 3px;
    max-width: 600px;
    margin-left: .5em;
    line-height: 30px;
    margin-bottom: 10px;
  }
}

.inline-checkbox-group {
  .checkbox {
    display: inline-block;
    input {
      vertical-align: middle;
    }
    label {
      margin-right: 1.5em;
    }
  }
}

.nested-fields {
  .form-control {
    @extend .form-control-sm;
  }
  &.inline {
    @extend .form-inline;
    background: white;
    border-radius: 2px;
    position:relative;
    padding: 2px 3px;
    margin-top: 1px;
    label {
      margin-left: 1.5em;
      margin-right: 0.5em;
    }
    .btn-rmv, .notice-rmv {
      top: 14%; // Bleh...
      position: absolute;
      right: 5px;
      vertical-align: middle;
    }
    &:hover {
      background: $app-light-yellow;
    }
  }
  &.panelled {
    @extend .card;
    .btn-rmv {
      float: right;
    }
  }
}
.btn-add {
  @extend .btn;
  @extend .btn-sm;
  @extend .btn-secondary;
  margin-top: 1em;
}
.btn-rmv {
  @extend .btn;
  @extend .btn-sm;
  @extend .btn-secondary;
  color: #9D1309;
}
.btn-new {
  @extend .btn;
  @extend .btn-sm;
  @extend .btn-primary;
  margin-top: 1em;
  margin-left: 2em;
  float: right;
}

input[type="radio"] { 
  margin-left: 1em;
  margin-right: 0.3em;
}

input[type="checkbox"] { 
  margin-left: 1em;
  margin-right: 10px;
}

.result {
  display: none
}
