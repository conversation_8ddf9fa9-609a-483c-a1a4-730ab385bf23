class MeasurementsController < ApplicationController
  before_action :set_sample, :set_tree, :set_paper_trail_whodunnit
  before_action :set_measurement, only: [:show, :edit, :update, :destroy, :history]
  before_action :get_measurement_types_hash, only: [:new, :edit, :create, :update]
  before_action :set_measurement_and_version, only: [:rollback]

  def index
    @search = Measurement.ransack(params[:q])
    @search.sorts = 'sample_code asc' if @search.sorts.empty?

    @measurements =
      if @sample.present?
        @sample.measurements.paginate(:page => params[:page], :per_page => 15)
      elsif @tree.present?
        @tree.measurements.paginate(:page => params[:page], :per_page => 15)
      else
        @search.result.includes(:sample).paginate(:page => params[:page], :per_page => 15)
        #Measurement.page(params[:page])
      end
  end

  def show
  end

  def new
    @measurement = Measurement.new(sample: @sample)
  end

  def edit
  end

  def create
    @measurement = Measurement.new(measurement_params)

    if @measurement.save
      if @measurement.update(measurement_params)
        if !@measurement.measurement_value_errors.blank?
          flash[:error] = @measurement.measurement_value_errors
          redirect_to [@sample, @measurement]
        else
          redirect_to [@sample, @measurement], notice: 'Measurement was successfully updated.'
        end
      end
    else
      render action: "new"
    end
  end

  def update
    if @measurement.update(measurement_params)
      if !@measurement.measurement_value_errors.blank?
        flash[:error] = @measurement.measurement_value_errors
        redirect_to [@sample, @measurement]
      else
        redirect_to [@sample, @measurement], notice: 'Measurement was successfully updated.'
      end
    else
      render action: "edit"
    end
  end

  def destroy
    @measurement.destroy
    flash[:notice] = 'Measurement was successfully deleted.'

    redirect_to :back
  end

  def history
    @history = @measurement.versions.union(@measurement.measurement_value_versions).order('created_at DESC')
    @versions = @history.paginate(:page => params[:page], :per_page => 15)
    @page = params[:page].to_i
    @per_page = 15
  end

  def rollback
    # change the current item (measurement or measurement value) to the specified version
    # reify gives you the object of this version
    @item = @version.reify
    @item.save!
    redirect_to sample_measurement_path(@sample, @measurement)
  end

  private

  def set_measurement_and_version
    @version = PaperTrail::Version.find_by(id: params[:version_id])
    @measurement = Measurement.find(params[:id])

    if @version.item_type == 'Measurement'
      @item = @measurement
    else
      @item = MeasurementValue.find_by(id: @version.item_id)
    end
  end

  def set_sample
    @sample = Sample.find(params[:sample_id]) if params[:sample_id].present?
  end

  def set_tree
    if params[:tree_id].present?
      @tree = Tree.find(params[:tree_id])
    elsif @sample.present?
      @tree = @sample.tree
    elsif @measurement.present? && @measurement.tree.present?
      @tree = @measurement.tree
    end
  end

  def set_measurement
    @measurement = Measurement.find(params[:id])
  end

  def measurement_params
    params.require(:measurement).permit!
  end

  def get_measurement_types_hash
    gon.measurement_types_hash = MeasurementType.pluck(:name).map { |name| [name, name.parameterize.underscore] }.to_h
  end
  
end
