class CowProductionsController < ApplicationController
  VIEW_PATH = "cow_productions/subjects/#{SUBJECT_CONFIG[:name]}".freeze

  before_action :find_tree, only: [:tree, :show, :edit, :update, :destroy]

  # GET /cowproductions
  def index
    # Filter cows
    view = "index"
    @search = CowProduction.ransack(params[:q])

    @search.sorts = 'code asc' if @search.sorts.empty?
    @cow_productions = @search.result.paginate(:page => params[:page], :per_page => 15)

    render template: "#{VIEW_PATH}/#{view}"
  end

  # GET /trees/1
  def show
    render template: "#{VIEW_PATH}/show"
  end

  # GET /trees/new
  def new
    @tree = Tree.new

    respond_to do |format|
      format.html do
        if request.xhr?
          render partial: "#{VIEW_PATH}/modal_form", layout: false
        else
          render template: "#{VIEW_PATH}/new"
        end
      end
    end
  end

  # GET /trees/1/edit
  def edit
    if template_exists? "#{VIEW_PATH}/edit"
      render template: "#{VIEW_PATH}/edit"
    else
      render 'edit'
    end
  end

  # POST /trees
  def create
    @tree = Tree.new(tree_params)

    respond_to do |format|
      if @tree.save
        format.html { redirect_to @tree, notice: "Plot was successfully created." }
        format.json { render json: @tree.as_json(methods: :to_label), status: :created, location: @tree }
      else
        format.html { render "#{VIEW_PATH}/new" }
        format.json { render json: @tree.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /trees/1
  def update
    if @tree.update_attributes(tree_params)
      flash[:notice] = "Plot was successfully updated."
      redirect_to(@tree)
    else
      render action: "edit"
    end
  end

  # DELETE /trees/1
  def destroy
    @tree.destroy

    redirect_to(trees_url)
  end

  # GET /trees/1/tree
  def tree
    render 'tree', layout: false
  end
  

  private

  def find_tree(param_name = :id)
    super
  end

  def tree_params
    params.require(:tree).permit!
  end
end
