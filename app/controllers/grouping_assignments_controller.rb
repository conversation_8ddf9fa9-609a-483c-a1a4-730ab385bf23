class GroupingAssignmentsController < ApplicationController
  before_action :find_grouping, :find_assignable

  # GET /grouping_assignments
  def index
    @grouping_assignments = @entity.grouping_assignments.all
  end

  # GET /grouping_assignments/1
  def show
    @grouping_assignment = @entity.grouping_assignments.find(params[:id])
  end

  # GET /grouping_assignments/new
  def new
    @grouping_assignment = GroupingAssignment.new
  end

  # POST /grouping_assignments
  def create
    @successful = true
    @error_messages = []

    if params[:grouping_id]
      @grouping = Grouping.find(params[:grouping_id])
      @assignments = @grouping.grouping_assignments.order(:assignable_id).page(params[:page])
    end

    # Grouping menu only
    if params[:grouping_assignment][:assignable_codes]
      assignable_codes = grouping_assignment_params.delete(:assignable_codes).split
      assignable_codes.each do |assignable_code|
        begin
          assignable_item = grouping_assignment_params[:assignable_type].constantize.find_by(code: assignable_code)

          if !assignable_item.blank?
            @grouping_assignment = GroupingAssignment.new(grouping_assignment_params.merge(assignable_id: assignable_item.id))
            merge_with_grouping_assignment
            @grouping_assignment.save!
          else
            @successful = false
            @error_messages.push("#{@grouping.assignable_type} #{assignable_code} does not exist")
          end
        rescue Exception => e
          @successful = false
          @error_messages.push(e.to_s.gsub('Grouping has already been taken', "#{@grouping.assignable_type} #{assignable_code} has already been added"))
        end
      end

      respond_to do |format|
        if @successful
          format.js # Render create.js.erb
        else
          format.js # Render create.js.erb
        end
      end

    # Individual menu (and individual measurement select) only
    else
      @grouping_assignment = GroupingAssignment.new(grouping_assignment_params)
      merge_with_grouping_assignment

      respond_to do |format|
        if @grouping_assignment.save
          format.html {
            flash[:notice] = "#{@grouping_assignment.assignable.class} was successfully added to the group."
            redirect_to(controller: @assignable_type.tableize, action: 'show', id: @assignable)
          }
          format.js { @successful = true } # Render create.js.erb
        else
          format.html { render action: "new" }
          format.js { @successful = false } # Render create.js.erb
        end
      end
    end
  end

  # DELETE /grouping_assignments/1
  def destroy
    @grouping_assignment = @entity.grouping_assignments.find(params[:id])
    @grouping_assignment.destroy

    # Grouping menu only
    if params[:grouping_id]
      @grouping = Grouping.find(params[:grouping_id])
      @assignments = @grouping.grouping_assignments.order(:assignable_id).page(params[:page])
    end

    respond_to do |format|
      format.html {
        flash[:notice] = "#{@grouping_assignment.assignable.class} was successfully removed from the group."
        redirect_to(controller: @assignable_type.tableize, action: 'show', id: @assignable)
      }
      format.js { @successful = true } # Render destroy.js.erb
    end
  end

  private

  # Individual menu
  def find_grouping
    @entity = @grouping = Grouping.find(params[:grouping_id]) if params[:grouping_id]
  end

  # Grouping menu
  def find_assignable
    Grouping.valid_types.each do |assignable_type|
      if !params["#{assignable_type.tableize.singularize}_id"].blank?
        @assignable_type = assignable_type
        @entity = @assignable = assignable_type.constantize.find(params["#{assignable_type.tableize.singularize}_id"])
        @tree = @entity if @entity.class == Tree
      end
    end
  end

  def merge_with_grouping_assignment
    if @entity.is_a?(Grouping)
      @grouping_assignment.grouping = @entity
    else
      @grouping_assignment.assignable = @entity
    end
  end

  def grouping_assignment_params
    params.require(:grouping_assignment).permit!
  end
end
