class AnCoolGastonGe3lsFilesController < ApplicationController
  http_basic_authenticate_with :name => Rails.application.secrets.an_cool_gaston_username, :password => Rails.application.secrets.an_cool_gaston_password
  
  before_action :set_an_cool_gaston_ge3ls_file, only: [:edit, :update, :destroy]

  def index
    @search = AnCoolGastonGe3lsFile.ransack(params[:q])
    @search.sorts = 'created_at desc' if @search.sorts.empty?
    @an_cool_gaston_ge3ls_files = @search.result.includes(:user).paginate(:page => params[:page], :per_page => 15)
  end

  def new
    @an_cool_gaston_ge3ls_file = AnCoolGastonGe3lsFile.new
  end

  def create
    @an_cool_gaston_ge3ls_file = AnCoolGastonGe3lsFile.new(an_cool_gaston_ge3ls_file_params)
    @an_cool_gaston_ge3ls_file.user = current_user

    respond_to do |format|
      if @an_cool_gaston_ge3ls_file.save
        format.html { redirect_to an_cool_gaston_ge3ls_files_path, notice: 'File was successfully created.' }
        format.json { render :show, status: :created, location: @an_cool_gaston_ge3ls_file }
      else
        format.html { render :new }
        format.json { render json: @an_cool_gaston_ge3ls_file.errors, status: :unprocessable_entity }
      end
    end
  end

  def edit
  end

  def update
    respond_to do |format|
      if @an_cool_gaston_ge3ls_file.update(an_cool_gaston_ge3ls_file_params)
        format.html { redirect_to an_cool_gaston_ge3ls_files_path, notice: 'File was successfully updated.' }
        format.json { render :show, status: :ok, location: @an_cool_gaston_ge3ls_file }
      else
        format.html { render :edit }
        format.json { render json: @an_cool_gaston_ge3ls_file.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @an_cool_gaston_ge3ls_file.destroy
    respond_to do |format|
      format.html { redirect_to an_cool_gaston_ge3ls_files_url, notice: 'File was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  def set_an_cool_gaston_ge3ls_file
    @an_cool_gaston_ge3ls_file = AnCoolGastonGe3lsFile.find(params[:id])
  end

  def an_cool_gaston_ge3ls_file_params
    params.require(:an_cool_gaston_ge3ls_file).permit!
  end
end
