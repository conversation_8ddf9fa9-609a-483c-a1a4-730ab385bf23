class DataFilesController < ApplicationController
  before_action :find_measurement, except: [ :master ]

  def master
    @search = DataFile.ransack(params[:q])
    @search.sorts = 'created_at desc' if @search.sorts.empty?
    @data_files = @search.result.paginate(:page => params[:page], :per_page => 15)
  end

  # GET /data_files
  # GET /data_files.xml
  def index
    @data_files = @measurement.present? ? @measurement.data_files : DataFile.all

    respond_to do |format|
      format.html # index.html.erb
      format.xml  { render :xml => @data_files }
    end
  end

  # GET /data_files/1
  # GET /data_files/1.xml
  def show
    @data_file = @measurement.data_files.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.xml  { render :xml => @data_file }
    end
  end

  # GET /data_files/new
  # GET /data_files/new.xml
  def new
    @data_file = DataFile.new
    @data_file.measurement = @measurement

    respond_to do |format|
      format.html # new.html.erb
      format.xml  { render :xml => @data_file }
    end
  end

  # POST /data_files
  # POST /data_files.xml
  def create
    @data_file = DataFile.new(data_file_params)
    @data_file.measurement = @measurement

    respond_to do |format|
      if @data_file.save
        flash[:notice] = 'Data File was successfully uploaded.'
        format.html { redirect_to(measurement_data_file_path(@measurement, @data_file)) }
        format.xml  { render :xml => @data_file, :status => :created, :location => @data_file }
      else
        format.html { render :action => "new" }
        format.xml  { render :xml => @data_file.errors, :status => :unprocessable_entity }
      end
    end
  end

  # DELETE /data_files/1
  # DELETE /data_files/1.xml
  def destroy
    @data_file = @measurement.data_files.find(params[:id])
    @data_file.destroy

    respond_to do |format|
      format.html { redirect_to(measurement_data_files_path(@measurement)) }
      format.xml  { head :ok }
    end
  end

  private

  def find_measurement
    if params[:measurement_id]
      @measurement = Measurement.find(params[:measurement_id])
      @sample = @measurement.sample
      @tree = @sample.root
    end
  end

  def data_file_params
    params.require(:data_file).permit!
  end
end
