class ProtocolsController < ApplicationController
  # GET /protocols
  # GET /protocols.xml
  def index
    @search = Protocol.ransack(params[:q])
    @search.sorts = ['name asc', 'version asc'] if @search.sorts.empty?
    @protocols = @search.result.paginate(:page => params[:page], :per_page => 15)

    respond_to do |format|
      format.html # index.html.erb
      format.xml  { render xml: @protocols }
    end
  end

  # GET /protocols/1
  # GET /protocols/1.xml
  def show
    @protocol = Protocol.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.xml  { render xml: @protocol }
    end
  end

  # GET /protocols/new
  # GET /protocols/new.xml
  def new
    @protocol = Protocol.new

    respond_to do |format|
      format.html # new.html.erb
      format.xml  { render xml: @protocol }
    end
  end

  # GET /protocols/1/edit
  def edit
    @protocol = Protocol.find(params[:id])
  end

  # POST /protocols
  # POST /protocols.xml
  def create
    @protocol = Protocol.new(protocol_params)

    respond_to do |format|
      if @protocol.save
        flash[:notice] = 'Protocol was successfully created.'
        format.html { redirect_to(@protocol) }
        format.xml  { render xml: @protocol, status: :created, location: @protocol }
      else
        format.html { render action: "new" }
        format.xml  { render xml: @protocol.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /protocols/1
  # PUT /protocols/1.xml
  def update
    @protocol = Protocol.find(params[:id])

    respond_to do |format|
      if @protocol.update(protocol_params)
        flash[:notice] = 'Protocol was successfully updated.'
        format.html { redirect_to(@protocol) }
        format.xml  { head :ok }
      else
        format.html { render action: "edit" }
        format.xml  { render xml: @protocol.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /protocols/1
  # DELETE /protocols/1.xml
  def destroy
    @protocol = Protocol.find(params[:id])
    @protocol.destroy

    respond_to do |format|
      format.html { redirect_to(protocols_url) }
      format.xml  { head :ok }
    end
  end

  private

  def protocol_params
    params.require(:protocol).permit!
  end
end
