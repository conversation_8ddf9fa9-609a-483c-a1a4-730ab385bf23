class TasksController < ApplicationController
  # GET /tasks
  def index
    if params[:user_id]
      @user = User.find(params[:user_id])
      @tasks = @user.tasks
    else
      @tasks = Task.all
    end
  end

  # GET /tasks/1
  def show
    @task = Task.find(params[:id])
  end

  # GET /tasks/new
  def new
    @task = Task.new(done_ratio: 0)
  end

  # GET /tasks/1/edit
  def edit
    @task = Task.find(params[:id])
  end

  # POST /tasks
  def create
    @task = Task.new(task_params)

    if @task.save
      redirect_to @task, notice: 'Task was successfully created.'
    else
      render action: "new"
    end
  end

  # PUT /tasks/1
  def update
    @task = Task.find(params[:id])

    if @task.update(task_params)
      redirect_to @task, notice: 'Task was successfully updated.'
    else
      render action: "edit"
    end
  end

  # DELETE /tasks/1
  def destroy
    @task = Task.find(params[:id])
    @task.destroy

    redirect_to tasks_url, notice: 'Task was successfully deleted.'
  end

  # GET /tasks/gantt
  def gantt
    @gantt = Gantt.new(params)

    # Tasks that have start and due dates
    conditions = "(((start_date >= :from AND start_date <= :to) OR (due_date >= :from AND due_date <= :to) OR (start_date < :from AND due_date > :to)) "
    conditions << "AND start_date IS NOT NULL AND due_date IS NOT NULL)"

    @gantt.events = Task.order('start_date, due_date').
      where(conditions, from: @gantt.date_from, to: @gantt.date_to)

    respond_to do |format|
      format.html
      format.js { render template: 'tasks/gantt', layout: false }
    end
  end

  # GET /tasks/calendar
  def calendar
    if params[:year] and params[:year].to_i > 1900
      @year = params[:year].to_i
      if params[:month] and params[:month].to_i > 0 and params[:month].to_i < 13
        @month = params[:month].to_i
      end
    end

    @year ||= Date.today.year
    @month ||= Date.today.month

    @calendar = Calendar.new(Date.civil(@year, @month, 1), :month)

    @calendar.events =
      Task.where("(start_date BETWEEN :from AND :to) OR (due_date BETWEEN :from AND :to)",
                 from: @calendar.startdt, to: @calendar.enddt)

    respond_to do |format|
      format.html
      format.js { render template: 'tasks/calendar', layout: false }
    end
  end

  # PUT /tasks/1/complete
  def complete
    @task = Task.find(params[:id])
    @task.complete!

    redirect_to @task, notice: 'Task has been completed.'
  end

  private

  def task_params
    params.require(:task).permit!
  end
end
