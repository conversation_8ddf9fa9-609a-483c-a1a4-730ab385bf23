class SamplesController < ApplicationController
  before_action :find_tree, :set_paper_trail_whodunnit
  before_action :find_parent_sample
  before_action :find_sample, only: [ :show, :edit, :update, :destroy, :history]
  before_action :set_sample_and_version, only: [:rollback]
  #before_action :no_parent?, only: [ :new, :create ]

  # GET /samples
  # GET /samples.xml
  def index
    @search = Sample.ransack(params[:q])
    @search.sorts = 'code asc' if @search.sorts.empty?

    @samples =
      if @parent.blank?
        @search.result.includes(:sample, :tree, measurements: [:measurement_type]).paginate(:page => params[:page], :per_page => 15)

      else
        @parent.samples.paginate(:page => params[:page], :per_page => 15)
      end

    respond_to do |format|
      format.html # index.html.erb
      format.xml  { render xml: @samples }
    end
  end

  # GET /samples/1
  # GET /samples/1.xml
  def show
    respond_to do |format|
      format.html # show.html.erb
      format.xml  { render xml: @sample }
    end
  end

  # GET /samples/new
  # GET /samples/new.xml
  def new
    @sample = Sample.new

    respond_to do |format|
      format.html # new.html.erb
      format.xml  { render xml: @sample }
    end
  end

  # GET /samples/1/edit
  def edit
  end

  # POST /samples
  # POST /samples.xml
  def create
    @sample = Sample.new(sample_params)
    if @parent.kind_of?(Tree)
      @sample.tree = @parent
    else
      @sample.sample = @parent
    end

    respond_to do |format|
      if @sample.save
        flash[:notice] = 'Sample was successfully created.'
        format.html { redirect_to([@parent, @sample]) }
        format.xml  { render xml: @sample, status: :created, location: @sample }
      else
        format.html { render action: "new" }
        format.xml  { render xml: @sample.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /samples/1
  # PUT /samples/1.xml
  def update
    respond_to do |format|
      if @sample.update(sample_params)
        flash[:notice] = 'Sample was successfully updated.'
        format.html { redirect_to([@parent, @sample]) }
        format.xml  { head :ok }
      else
        format.html { render action: "edit" }
        format.xml  { render xml: @sample.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /samples/1
  # DELETE /samples/1.xml
  def destroy
    @sample.destroy

    flash[:notice] = 'Sample was successfully deleted.'
    respond_to do |format|
      format.html { redirect_to(@parent.kind_of?(Sample) ? sample_samples_url(@parent) : tree_samples_url(@tree)) }
      format.xml  { head :ok }
    end
  end

  def history
    @history = PaperTrail::Version.where(item_type: 'Sample', item_id: @sample.id).order('created_at DESC')
    @versions = @history.paginate(:page => params[:page], :per_page => 15)
    @page = params[:page].to_i
    @per_page = 15
  end

  def rollback
    # change the current item (measurement or measurement value) to the specified version
    # reify gives you the object of this version
    @sample = @version.reify
    @sample.save!
    redirect_to sample_path(@sample)
  end

  private

  def set_sample_and_version
    @version = PaperTrail::Version.find_by(id: params[:version_id])
    @sample = Sample.find(params[:id])
  end

  def find_tree
    unless params[:tree_id].blank?
      super
      @parent = @tree if @parent.blank?
    end
  end

  def find_parent_sample
    unless params[:sample_id].blank?
      @parent = @parent_sample = Sample.find(params[:sample_id])
      params[:tree_id] = @parent.root.id
      find_tree
    end
  end

  def find_sample
    @sample = @parent.blank? ? Sample.find(params[:id]) : @parent.samples.find(params[:id])
    params[:tree_id] = @sample.root.id
    find_tree
  end

  #def no_parent?
  #  redirect_to samples_url if @parent.blank?
  #end

  def sample_params
    params.require(:sample).permit!
  end
end
