class DavidsonBlueGe3lsFilesController < ApplicationController
  http_basic_authenticate_with :name => Rails.application.secrets.davidson_blue_username, :password => Rails.application.secrets.davidson_blue_password

  before_action :set_davidson_blue_ge3ls_file, only: [:edit, :update, :destroy]

  def index
    @search = DavidsonBlueGe3lsFile.ransack(params[:q])
    @search.sorts = 'created_at desc' if @search.sorts.empty?
    @davidson_blue_ge3ls_files = @search.result.includes(:user).paginate(:page => params[:page], :per_page => 15)
  end

  def new
    @davidson_blue_ge3ls_file = DavidsonBlueGe3lsFile.new
  end

  def create
    @davidson_blue_ge3ls_file = DavidsonBlueGe3lsFile.new(davidson_blue_ge3ls_file_params)
    @davidson_blue_ge3ls_file.user = current_user

    respond_to do |format|
      if @davidson_blue_ge3ls_file.save
        format.html { redirect_to davidson_blue_ge3ls_files_path, notice: 'File was successfully created.' }
        format.json { render :show, status: :created, location: @davidson_blue_ge3ls_file }
      else
        format.html { render :new }
        format.json { render json: @davidson_blue_ge3ls_file.errors, status: :unprocessable_entity }
      end
    end
  end

  def edit
  end

  def update
    respond_to do |format|
      if @davidson_blue_ge3ls_file.update(davidson_blue_ge3ls_file_params)
        format.html { redirect_to davidson_blue_ge3ls_files_path, notice: 'File was successfully updated.' }
        format.json { render :show, status: :ok, location: @davidson_blue_ge3ls_file }
      else
        format.html { render :edit }
        format.json { render json: @davidson_blue_ge3ls_file.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @davidson_blue_ge3ls_file.destroy
    respond_to do |format|
      format.html { redirect_to davidson_blue_ge3ls_files_url, notice: 'File was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  def set_davidson_blue_ge3ls_file
    @davidson_blue_ge3ls_file = DavidsonBlueGe3lsFile.find(params[:id])
  end

  def davidson_blue_ge3ls_file_params
    params.require(:davidson_blue_ge3ls_file).permit!
  end
end
