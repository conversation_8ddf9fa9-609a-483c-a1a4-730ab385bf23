class ParentTreesController < ApplicationController
  
  def index
    @search = ParentTree.ransack(params[:q])
    
    if @search.sorts.empty?
      @parent_trees = @search.result.order("-suc_code desc, accession_number asc").paginate(:page => params[:page], :per_page => 15)
    else
      attribute = @search.sorts.first.name
      order_by = @search.sorts.first.dir

      if order_by == "asc"
        sort_query = "-#{attribute} desc" # NULLS LAST
      elsif order_by == "desc"
        sort_query = "#{attribute} desc"
      end

      @parent_trees = @search.result.except(:order).order(sort_query).paginate(:page => params[:page], :per_page => 15)
    end
  end

  def show
    @parent_tree = ParentTree.find(params[:id])
  end

end
