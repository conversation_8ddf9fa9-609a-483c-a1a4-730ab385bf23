class GeneralFilesController < ApplicationController
  before_action :set_general_file, only: [:edit, :update, :destroy]

  def all
    @search = GeneralFile.ransack(params[:q])
    @search.sorts = 'created_at desc' if @search.sorts.empty?
    @general_files = @search.result.includes(:user)
  end

  def index
    @search = GeneralFile.ransack(params[:q])
    @search.sorts = 'created_at desc' if @search.sorts.empty?
    @general_files = @search.result.includes(:user).paginate(:page => params[:page], :per_page => 15)
  end

  def new
    @general_file = GeneralFile.new
  end

  def create
    @general_file = GeneralFile.new(general_file_params)
    @general_file.user = current_user

    respond_to do |format|
      if @general_file.save
        format.html { redirect_to general_files_path, notice: 'File was successfully created.' }
        format.json { render :show, status: :created, location: @general_file }
      else
        format.html { render :new }
        format.json { render json: @general_file.errors, status: :unprocessable_entity }
      end
    end
  end

  def edit
  end

  def update
    respond_to do |format|
      if @general_file.update(general_file_params)
        format.html { redirect_to general_files_path, notice: 'File was successfully updated.' }
        format.json { render :show, status: :ok, location: @general_file }
      else
        format.html { render :edit }
        format.json { render json: @general_file.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @general_file.destroy
    respond_to do |format|
      format.html { redirect_to general_files_url, notice: 'File was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  def set_general_file
    @general_file = GeneralFile.find(params[:id])
  end

  def general_file_params
    params.require(:general_file).permit!
  end
end
