class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception
  before_action :authenticate_user!
  before_action :set_current_user

  private

  def find_tree(id_param = :tree_id)
    @tree = Tree.find(params[id_param])
  end

  def set_current_user
    User.current_user = current_user
  end

  def after_sign_out_path_for(resource_or_scope)
    user_session_path
  end

end
