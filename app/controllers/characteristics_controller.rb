class CharacteristicsController < ApplicationController
  before_action :find_tree, only: [:index, :show, :new, :edit, :create, :update, :destroy]

  # GET /characteristics
  # GET /characteristics.xml
  def index
    @characteristics = @tree.characteristics.order('age_recorded ASC')

    respond_to do |format|
      format.html # index.html.erb
      format.xml  { render xml: @characteristics }
    end
  end

  # GET /characteristics/1
  # GET /characteristics/1.xml
  def show
    @characteristic = @tree.characteristics.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.xml  { render xml: @characteristic }
    end
  end

  # GET /characteristics/new
  # GET /characteristics/new.xml
  def new
    @characteristic = Characteristic.new

    respond_to do |format|
      format.html # new.html.erb
      format.xml  { render xml: @characteristic }
    end
  end

  # GET /characteristics/1/edit
=begin
  def edit
    @characteristic = @tree.characteristics.find(params[:id])
  end
=end

  # POST /characteristics
  # POST /characteristics.xml
  def create
    @characteristic = Characteristic.new(characteristic_params)

    @characteristic.tree = @tree

    respond_to do |format|
      if @characteristic.save
        flash[:notice] = "#{Tree.title} #{Characteristic.title} was successfully created."
        format.html { redirect_to(tree_characteristic_url(@tree, @characteristic)) }
        format.xml  { render xml: @characteristic, status: :created, location: @characteristic }
      else
        format.html { render action: "new" }
        format.xml  { render xml: @characteristic.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /characteristics/1
  # PUT /characteristics/1.xml
  def update
    @characteristic = @tree.characteristics.find(params[:id])

    respond_to do |format|
      if @characteristic.update(characteristic_params)
        flash[:notice] = "#{Tree.title} #{Characteristic.title} was successfully updated."
        format.html { redirect_to(tree_characteristic_url(@tree, @characteristic)) }
        format.xml  { head :ok }
      else
        format.html { render action: "edit" }
        format.xml  { render xml: @characteristic.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /characteristics/1
  # DELETE /characteristics/1.xml
  def destroy
    @characteristic = @tree.characteristics.find(params[:id])
    @characteristic.destroy

    respond_to do |format|
      format.html { redirect_to(tree_characteristics_url(@tree)) }
      format.xml  { head :ok }
    end
  end

  private

  def characteristic_params
    params.require(:characteristic).permit!
  end
end
