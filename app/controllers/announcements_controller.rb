class AnnouncementsController < ApplicationController
  before_action :set_announcement, only: [:edit, :update]

  # GET /announcements
  # GET /announcements.json
  def index
    @announcements = Announcement.all.order('created_at DESC').paginate(:page => params[:page], :per_page => 15)
  end

  # GET /announcements/new
  def new
    @announcement = Announcement.new
  end

  # GET /announcements/1/edit
  def edit
  end

  # POST /announcements
  # POST /announcements.json
  def create
    @announcement = Announcement.new(announcement_params)

    if @announcement.save
      redirect_to announcements_path, notice: 'Announcement was successfully created.'
    else
      render :new
    end
  
  end

  # PATCH/PUT /announcements/1
  # PATCH/PUT /announcements/1.json
  def update
    if @announcement.update(announcement_params)
      redirect_to announcements_path, notice: 'Announcement was successfully updated.'
    else
      render :edit
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_announcement
      @announcement = Announcement.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def announcement_params
      params.require(:announcement).permit(:message, :display)
    end
end
