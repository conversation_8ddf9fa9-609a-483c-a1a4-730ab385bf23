class StudiesController < ApplicationController
  def index
    @studies = Study.all
  end

  def new
    @study = Study.new
  end

  def show
    @study = Study.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
    end
  end

  def edit
    @study = Study.find(params[:id])
  end

  def create
    @study = Study.new(study_params)

    if @study.save
      flash[:notice] = 'Study was successfully created.'
      redirect_to(@study)
    else
      render action: "new"
    end
  end

  def update
    @study = Study.find(params[:id])

    if @study.update(study_params)
      flash[:notice] = 'Study was successfully updated.'
      redirect_to(action: 'show', id: @study)
    else
      render action: "edit"
    end
  end

  def destroy
    @study = Study.find(params[:id])
    @study.destroy

    flash[:notice] = 'Study was successfully deleted.'
    redirect_to(action: 'index')
  end

  private

  def study_params
    params.require(:study).permit!
  end
end
