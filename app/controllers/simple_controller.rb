class SimpleController < ApplicationController
  include <PERSON><PERSON>elper

  def home

  end

  def advanced_search
    fetch_query
  end

  def result
    fetch_query

    @query_results = @search.result(distinct: :true)
    @ordered_results = @query_results.order(:code)
    @trees = @ordered_results.paginate(:page => params[:page], :per_page => 15)
    @columns = params[:columns]
    @ages = @query_results.joins(:characteristics).distinct.pluck(:age_recorded).sort!

    respond_to do |format|
      if !@columns
        @columns = default_columns
      end
      format.html
      # From https://medium.com/table-xi/stream-csv-files-in-rails-because-you-can-46c212159ab7
      format.csv do 
        headers["X-Accel-Buffering"] = "no" # Don't buffer when going through proxy servers
        headers["Cache-Control"] = "no-cache" # Don't cache anything from this generated endpoint
        headers["Content-Type"] = "text/csv; charset=utf-8"  # Tell the browser this is a CSV file
        headers["Content-Disposition"] = %(attachment; filename="#{csv_filename}")  # Make the file download with a specific filename
        self.response_body = CSVExport.generate(@ordered_results, @columns, @ages) # Fed as enumerator for streaming
      end
    end
  end

  def references

  end

  def fetch_query
    # Map measurement names to their actual names in the db for search purposes
    if params.has_key?(:q)
      query_ids = params[:q][:c]
      query_ids.each do |query_id|
        query_value = params[:q][:c][query_id.to_sym][:v]['0'.to_sym][:value]
        params[:q][:c][query_id.to_sym][:v]['0'.to_sym][:value] = MeasurementValue.map_to_db(query_value)
      end
    end
    
    # Apply filter group if present
    if (!params.has_key?(:grouping)) || (params[:grouping][:id].blank?)
      @search = Tree.ransack(params[:q])
    else
      @filter_ids = GroupingAssignment.where(grouping_id: params[:grouping][:id], assignable_type: "Tree").pluck(:assignable_id)
      @search = Tree.where(:id => @filter_ids).ransack(params[:q])
    end

    @search.build_condition if @search.conditions.empty?
  end


  private

  def csv_filename
    "search-results-#{Date.today}.csv"
  end
end
