class UsersController < ApplicationController
  before_action :set_user, only: [:show, :edit, :update]
  before_action :authorize

  def index
    @search = User.ransack(params[:q])
    @search.sorts = 'name asc' if @search.sorts.empty?
    @users = @search.result.paginate(:page => params[:page], :per_page => 15)
  end

  def show
  end

  def new
    @user = User.new
  end

  def edit
  end

  def create
    @user = User.new(user_params)
    @user.skip_password_validation!

    if @user.save
      @user.invite!(current_user)
      redirect_to @user, notice: 'User was successfully invited.'
    else
      render action: "new"
    end
  end

  def update
    if @user == current_user
      if @user.update_with_password(user_params)
        redirect_to @user, notice: 'Your account was successfully updated.'
      else
        render action: "edit"
      end
    else
      if @user.update(user_params)
        redirect_to @user, notice: 'User was successfully updated.'
      else
        render action: "edit"
      end
    end
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def authorize
    case self.action_name
    when 'new', 'create'
      unless current_user.administrator?
        redirect_to users_path, alert: 'You are not authorized to perform this action.'
      end
    when 'edit', 'update'
      unless current_user.administrator? || current_user == @user
        redirect_to users_path, alert: 'You are not authorized to perform this action.'
      end
    end
  end

  def user_params
    params.require(:user).permit!
  end
end
