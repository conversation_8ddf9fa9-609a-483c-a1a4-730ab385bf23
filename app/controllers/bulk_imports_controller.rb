require 'csv'
class BulkImportsController < ApplicationController
  before_action :find_category, :find_name
  before_action :get_measurement_types_hash, only: [:import]

  def import
    if request.post?
      if params[:file][:id].present?
        if @category.singularize.capitalize == 'Measurement'
          results = MeasurementBulkImport.import(params[:file][:id], params[:protocol][:id], params[:sample_type], params[:measurement_type_id])
        elsif @category.singularize.capitalize == "Cowproduction"
          results = CowProduction.import(params[:file][:id])
        else
          results = @category.singularize.capitalize.constantize.import(params[:file][:id])
        end
        is_success = results[0]
        message = results[1]

        if is_success
          flash[:notice] = message

          # Characteristics don't have their own page -> redirect to trees 
          if @category == 'characteristics'
            redirect_to(trees_url)
          elsif @category == 'cowproductions'
            redirect_to(cow_productions_url)
          else
            redirect_to("/#{@category.pluralize}")
          end
        else
          flash[:error] = message
          redirect_back(fallback_location: "bulk_import/#{@category.pluralize.downcase}")
        end
      else
        redirect_back(fallback_location: "bulk_import/#{@category.pluralize.downcase}")
      end
    else
      render template: "bulk_imports/import_#{@category.pluralize.downcase}"
    end
  end

  def download_template
    filename = @name.gsub('/', '_')
    send_file "#{Rails.root}/public/templates/#{filename}_import_template.csv", type: "application/csv", x_sendfile: true
  end

  private

  def find_category
    @category = params[:category]
  end

  def find_name
    @name = params[:name]
  end

  def get_measurement_types_hash
    gon.measurement_types_hash = MeasurementType.pluck(:name).map { |name| [name, name.parameterize.underscore] }.to_h
  end

end
