doctype html
html
  head
    meta name="viewport" content="width=device-width, initial-scale=1.0"
    meta content="text/html; charset=UTF-8" http-equiv="Content-Type"
    == content_for(:meta_tags)
    title = content_for?(:title) ? yield(:title) : 'PeaCE-LIMS'
    == stylesheet_link_tag 'lims', media: 'all',
        "data-turbolinks-track" => true
    == javascript_include_tag "application", media: 'all',
        "data-turbolinks-track" => true
    == yield :css
    == yield :javascript

    == csrf_meta_tags

  body[class="#{params[:controller].parameterize.dasherize}-c \
              #{params[:action].parameterize}-a"
       data-c=params[:controller].parameterize
       data-a=params[:action].parameterize]
    header
      - if user_signed_in?
        == render 'layouts/lims/navigation'
        = Gon::Base.render_data
      - else
        == render 'layouts/public/navigation'
    main role="main"
      - if user_signed_in?
        - if defined?(@tree) && @tree.present? && @tree.persisted?
          = content_for :sidebar do
            h2 Plot Summary
            #subject-tree[data-source="#{tree_tree_path(@tree)}"
                          data-selected-node=selected_tree_node]
        .row
          - if content_for?(:sidebar)
            #sidebar-content
              #sidebar == yield :sidebar
            #main-content.with-sidebar
              == render '/layouts/flash'
              == yield
          - else
            #main-content.without-sidebar
              == render '/layouts/flash'
              == yield
      - else
        #main-content.without-sidebar
          == render '/layouts/flash'
          == yield

    footer
