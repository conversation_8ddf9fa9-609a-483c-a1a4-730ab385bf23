/ li.nav-item.dropdown
/   a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Ranches
/   div.dropdown-menu
/     = link_to "Kinsella", trees_path, class: 'dropdown-item'
/     = link_to "<PERSON><PERSON><PERSON>", trees_path(category: "non_res_for"), class: 'dropdown-item'
/     = link_to "LivingLabs", parent_trees_path, class: 'dropdown-item'
li.nav-item.dropdown
  a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Import Data
  div.dropdown-menu
    = link_to 'Bulk Import', bulk_import_cowproductions_path, class: 'dropdown-item'
    = link_to 'Header Reference', references_path, class: 'dropdown-item'
    /= link_to 'Measurements', measurements_path, class: 'dropdown-item'
    /= link_to 'Samples', samples_path, class: 'dropdown-item'
li.nav-item.dropdown 
  a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Browse Data
  div.dropdown-menu
    = link_to 'Cow Production Data', cow_productions_path, class: 'dropdown-item'
li.nav-item= link_to 'Advanced Search (Export)', advanced_search_path, class: 'nav-link'
/li.nav-item.dropdown
  a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Groups
  div.dropdown-menu
    /= link_to 'Studies', studies_path, class: 'dropdown-item'
    /= link_to 'Create Study', new_study_path, class: 'dropdown-item'
    = link_to "All Groups", all_groupings_path, class: 'dropdown-item'
    / = link_to "Sample Groups", sample_groupings_path, class: 'dropdown-item'
    / = link_to "Measurement Groups", measurement_groupings_path, class: 'dropdown-item'
li.nav-item.dropdown
  a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Planning
  div.dropdown-menu
    = link_to 'Tasks', tasks_path, class: 'dropdown-item'
    = link_to 'My Tasks', user_tasks_path(current_user), class: 'dropdown-item'
    = link_to 'Gantt Chart', gantt_tasks_path, class: 'dropdown-item'
    = link_to 'Calendar', calendar_tasks_path, class: 'dropdown-item'
li.nav-item.dropdown
  a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Files
  div.dropdown-menu
    = link_to 'Data Files', general_files_path, class: 'dropdown-item'
    /= link_to 'General Files', general_files_path, class: 'dropdown-item'
    = link_to 'Pictures', pictures_path, class: 'dropdown-item'
    = link_to 'SOPs', protocols_path, class: 'dropdown-item'
li.nav-item.dropdown
  a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Admin
  div.dropdown-menu
    = link_to 'Announcements', announcements_path, class: 'dropdown-item'
    = link_to 'Calendar Events', calendar_events_path, class: 'dropdown-item'
    = link_to 'Data File Types', data_file_types_path, class: 'dropdown-item'
    = link_to 'PIs', sites_path, class: 'dropdown-item'
    = link_to 'Users', users_path, class: 'dropdown-item'
li.nav-item.dropdown
  a.nav-link.dropdown-toggle href="#" data-toggle="dropdown" Account
  div.dropdown-menu
    = link_to 'Logout', destroy_user_session_path, method: 'delete', class: 'dropdown-item'
