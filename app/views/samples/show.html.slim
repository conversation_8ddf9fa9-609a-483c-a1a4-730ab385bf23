h1 
  | Showing #{@sample.aliquot? ? 'Sub-Sample' : 'Sample'} 
  /font[color="blue"]
    | #{@sample.code} 
  /| from #{@sample.aliquot? ? 'Sample ' : "Plot "} #{@sample.parent}
  | #{@sample.code} 
  = link_to_history 'history', history_sample_path(@sample)
br

.card-deck
  .card
    .card-header Sample Details
    .card-block
      dl
        dt Sample Type:
        dd = @sample.sample_type.capitalize
        dt Sample Code:
        dd = @sample.code
        /dt = Tree.title
        /#dd = link_to(@sample.root.to_s, tree_path(@sample.root))
        /- if @sample.aliquot?
          dt Parent Sample:
          dd = link_to(@sample.sample, sample_path(@sample.sample))
        dt Description: 
        dd = nah @sample.description
        dt Tags:
        dd = nah @sample.tag_list
    .card-footer
      .btn-group
        - if !@sample.aliquot? 
          = link_to_new 'add sub-sample', new_sample_sample_path(@sample)
        - if measurable? @sample
          = link_to_new 'add measurement', new_sample_measurement_path(@sample)
  .card
    .card-header Collection Details
    .card-block
      dl
        - if @sample.suffix == 'MT'
          dt Amount (Wet Weight):
        - else
          dt Original Amount:
        dd = nah "#{@sample.original_amount} #{@sample.original_unit}"

        - if @sample.suffix == 'MT'
          dt Amount (Dry Weight):
        - else
          dt Remaining Amount:
        dd = nah "#{@sample.actual_amount} #{@sample.actual_unit}"

        /dt Theoretical Amount:
        /dd #{@sample.theoretical_amount} #{@sample.original_unit}
        dt Collected On:
        dd = nah @sample.collected_on_range
        dt Collected By:
        dd 
          - if @sample.collected_by.nil?
            = nah
          - else
            = @sample.collected_by.name
     
  .card
    .card-header Storage Location
    .card-block
      dl
        dt = Site.model_name.human
        dd = nah @sample.site&.name
        dt Building:
        dd = nah @sample.building
        dt Room:
        dd = nah @sample.room
        dt Freezer:
        dd = nah @sample.freezer
        dt Box/Container:
        dd = nah @sample.box
br
dt Last Updated: #{@sample.updated_at.in_time_zone('Mountain Time (US & Canada)').strftime("%Y-%m-%d %H:%M")} MST by #{@sample.last_user_updated.name}
br
= link_to_edit @sample.aliquot? ? edit_sample_sample_path(@sample.sample, @sample) : edit_tree_sample_path(@tree, @sample)

