h1
  /- if !defined? @parent
    ' Samples
  /- elsif @parent.kind_of?(<PERSON><PERSON>)
    ' Sub-Samples of Sample #{@parent.id}
  /- elsif @parent.kind_of?(Tree)
    ' Showing Samples for Plot #{@parent}

  /- if defined? @parent
    - if @parent.kind_of?(<PERSON><PERSON>)
      = link_to_new_right_aligned "create sub-sample of sample #{@parent.id}", new_sample_sample_path(@parent)
    - elsif @parent.kind_of?(Tree)
      = link_to_new_right_aligned "add sample for Tree #{@parent.code}",
          new_tree_sample_path(@parent)

  - if !@tree.present?
    | Samples
    = link_to_new_right_aligned 'add sample', new_sample_path
  - else 
    | Showing Samples for Pasture #{@tree.code}
    = link_to_new_right_aligned "add sample for Tree #{@tree.code}", new_tree_sample_path(@tree)

= search_form_for @search, url: samples_path, html: { method: :get } do |f|
  table.list
    thead
      tr
        th Sample Code
        th Type
        th Original Amount
        /th= Tree.title unless @tree
        - if @parent.blank?
          th = 'Parent Sample' unless @parent
        th Collected On
        th Collected By
        /th Lab
        th Measurements
        th Tags
        th colspan="3" Actions
      - if @parent.blank?
        tr.table-search
          th= f.text_field :code_cont
          th= f.select :sample_type_cont, Sample.sample_types, include_blank: true
          th= f.text_field :original_amount_gteq
          /th= f.text_field :tree_code_cont
          th= f.text_field :sample_code_cont
          th= f.text_field :collected_on_cont
          th= f.select :collected_by_id_eq, User.order(:name).map { |s| [s.name, s.id] }, include_blank: true
          /th= f.text_field :site_name_cont
          th= f.select :measurements_measurement_type_name_eq, MeasurementType.order(:name).map { |s| [s.name, s.name] }, include_blank: true
          th= f.select :tags_name_cont, all_tags('Sample'), include_blank: true
          th colspan="3" = table_search_actions
    tbody
      - @samples.each do |sample|
        tr
          td= sample.code
          td= sample.sample_type
          td
            - if !sample.original_amount.nil? 
              | #{sample.original_amount} #{sample.original_unit}
            - if !sample.actual_amount.nil?
              | <br>(#{sample.actual_amount} #{sample.original_unit} remaining)
          /td= link_to(sample.root.code, sample.root)
          - if !defined? @parent
            td = link_to(sample.sample.code, sample.sample) unless @parent_sample || sample.sample.nil?
          td= sample.collected_on_range
          td= sample.collected_by.present? ? link_to(sample.collected_by.name, sample.collected_by) : ''
          /td= sample.site&.name
          td
            - if sample.measurements.length != 0
              - sample.measurements.each do |measurement|
                = link_to measurement.measurement_type.name, [sample, measurement]
                br
            - else
              = nah
          td= tag_list(sample.tags)
          td
            - if sample.aliquot?
              = link_to_show sample_sample_path(sample.sample, sample)
            - else
              = link_to_show tree_sample_path(sample.tree, sample)
          td
            - if sample.aliquot?
              = link_to_edit edit_sample_sample_path(sample.sample, sample)
            - else
              = link_to_edit edit_tree_sample_path(sample.tree, sample)
          td
            - if sample.aliquot?
              = link_to_destroy sample_sample_path(sample.sample, sample)
            - else
              = link_to_destroy tree_sample_path(sample.tree, sample)
br

= will_paginate@samples

