/- if @sample.new_record?
  = f.hidden_field(:tree_id) if @sample.tree
  = f.hidden_field(:parent_sample_id) if @sample.sample

fieldset
  legend Sample Identification
  .row: .col-4
    - if @parent_sample
      = f.input :sample_type, collection: Sample.sample_types, label: 'Sample Type', disabled: true, prompt: @parent_sample.sample_type
    - else
      = f.input :sample_type, collection: Sample.sample_types, label: 'Sample Type'
  .row 
    .col-4 Sample Code
  .row
    - if defined?(@tree)
      - source_code = @tree.source.code
      .col-4 = f.input :tree_code, label: false, :input_html => { :value => @tree.code }, disabled: true
    - else
      - source_code = nil
      .col-4 = f.input :tree_code, label: false, :collection => Tree.all, input_html: { class: 'select2-basic', style: 'width: 100%;' }, include_blank: 'Unique Tree Code'
    | -
    - if @sample.new_record?
      - if @parent.kind_of?(Sample)
        .col-4 = f.input :suffix, label: false, :collection => suffix_collection(source_code, 'child'), include_blank: 'Measurement Type', input_html: { class: 'select2-basic', style: 'width: 100%;' }
      - else
        .col-4 = f.input :suffix, label: false, :collection => suffix_collection(source_code, 'parent'), include_blank: 'Measurement Type', input_html: { class: 'select2-basic', style: 'width: 100%;' }
    - else
      .col-4 = f.input :suffix, label: false, disabled: true
  = f.input :description
  = f.input :tag_list, collection: all_tags('Sample'), label: "Tags",
      input_html: { class: 'taggable', multiple: true }

fieldset
  legend Collection Details
  .row
    - if @sample.suffix == 'MT'
      .col-4 = f.input :original_amount, label: 'Amount (Wet Weight)'
    - else
      .col-4 = f.input :original_amount
    .col-2 = f.input :original_unit, collection: weight_unit_options, include_blank: true, label: 'Unit'
  
  .row
    - if @sample.suffix == 'MT'
      .col-4 = f.input :original_amount, label: 'Amount (Dry Weight)'
    - else
      .col-4 = f.input :actual_amount
    .col-2 = f.input :actual_unit, collection: weight_unit_options, include_blank: true, label: 'Unit'

  .row
    .col-4 = f.association :collected_by, include_blank: true, label: 'Collected By'
    .col-4
      = f.input :collected_on, as: :string,
          input_html: { type: 'date' }, placeholder: 'YYYY-MM-DD', label: 'Collected On (Start Date)'
    .col-4
      = f.input :collected_on_end, as: :string,
          input_html: { type: 'date' }, placeholder: 'YYYY-MM-DD', label: 'Collected On (End Date)', hint: "*optional"

fieldset
  legend Storage Details
  = f.association :site, include_blank: true
  = f.input :building
  = f.input :room
  = f.input :freezer
  = f.input :box

.form-actions
  = f.submit

