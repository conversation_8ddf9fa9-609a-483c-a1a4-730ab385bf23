<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PeaCE Project - Climate-Smart Pea Development</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* CLIMATE SCIENCE PALETTE - Harmonized with PeaCE Logo */
            --primary-plum: #8E44AD;         /* Rich purple - matches logo "CE" */
            --secondary-teal: #16A085;        /* Vibrant teal - matches logo "ea" */
            --accent-spring: #27AE60;         /* Fresh spring green - matches pea pod */
            --deep-pine: #1B4332;            /* Deep forest green - matches leaves */
            --light-sage: #F8FFF8;           /* Subtle green tint backgrounds */
            --surface-white: #ffffff;
            --gradient-primary: linear-gradient(135deg, #8E44AD 0%, #16A085 100%);
            --gradient-secondary: linear-gradient(135deg, #27AE60 0%, #1B4332 100%);
            --gradient-hero: linear-gradient(135deg, #8E44AD 0%, #16A085 60%, #27AE60 100%);
            
            /* ENHANCED NEUTRALS */
            --dark-text: #1B4332;            /* Deep pine for text */
            --medium-text: #2E7D32;          /* Medium green for secondary text */
            --light-text: #4A90A4;           /* Light teal for tertiary text */
            --border-light: #E8F5E8;         /* Light sage border */
            --shadow-light: rgba(139, 69, 19, 0.05);
            --shadow-medium: rgba(139, 69, 19, 0.1);
            --shadow-heavy: rgba(139, 69, 19, 0.25);
            
            /* STATUS COLORS */
            --success: #27AE60;              /* Spring green */
            --warning: #F39C12;              /* Warm orange */
            --danger: #E74C3C;               /* Red */
            --info: #16A085;                 /* Teal */
            
            /* GLASS MORPHISM */
            --glass-bg: rgba(248, 255, 248, 0.1);
            --glass-border: rgba(248, 255, 248, 0.2);
            
            /* BACKGROUND IMAGE SETTINGS */
            --background-opacity: 0.08;
            
            /* LEGACY SUPPORT - Mapped to new palette */
            --primary-blue: #8E44AD;         /* Now plum */
            --secondary-blue: #16A085;        /* Now teal */
            --accent-emerald: #27AE60;        /* Now spring green */
            --light-background: #F8FFF8;      /* Now light sage */
        }

        body {
            font-family: 'Arial', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--dark-text);
            overflow-x: hidden;
            scroll-behavior: smooth;
            position: relative;
        }

        /* TRANSLUCENT BACKGROUND IMAGE */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('images/background/pea-field-background.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
            opacity: var(--background-opacity);
            z-index: -2;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        /* Optional: Different opacity for different sections */
        .hero::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(30, 58, 138, 0.1);
            z-index: 1;
            pointer-events: none;
        }

        /* Ensure content stays above background */
        .section {
            position: relative;
            z-index: 1;
        }

        /* Alternative background patterns for different sections */
        .overview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(5, 150, 105, 0.03) 0%, transparent 50%),
                url('images/background/pea-plants-pattern.png');
            background-size: cover, cover, 200px 200px;
            background-position: center, center, top left;
            background-repeat: no-repeat, no-repeat, repeat;
            opacity: 0.4;
            z-index: 0;
            pointer-events: none;
        }

        /* ENHANCED NAVIGATION */
        .navbar {
            background: var(--gradient-primary);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar.scrolled {
            padding: 0.7rem 0;
            background: rgba(142, 68, 173, 0.95);
            box-shadow: 0 8px 32px rgba(142, 68, 173, 0.3);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--surface-white);
            text-decoration: none;
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2.5rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.2rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: 0.5rem 0;
        }

        .nav-link:hover {
            color: #ffffff;
            transform: translateY(-2px);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background: linear-gradient(90deg, #059669, #10b981);
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 2px;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .mobile-menu {
            display: none;
            color: var(--surface-white);
            font-size: 1.5rem;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .mobile-menu:hover {
            transform: scale(1.1);
        }

        /* ENHANCED HERO SECTION */
        .hero {
            background: var(--gradient-hero);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: var(--surface-white);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(5, 150, 105, 0.1) 0%, transparent 50%);
            animation: heroFloat 20s ease-in-out infinite;
        }

        @keyframes heroFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        .hero-content {
            max-width: 900px;
            z-index: 2;
            position: relative;
            animation: heroFadeIn 1.2s ease-out;
        }

        @keyframes heroFadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .hero h1 {
            font-size: clamp(2.5rem, 6vw, 5rem);
            margin-bottom: 1.5rem;
            font-weight: 800;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShimmer 3s ease-in-out infinite;
        }

        @keyframes textShimmer {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.2); }
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 2.5vw, 1.4rem);
            margin-bottom: 3rem;
            opacity: 0.95;
            font-weight: 400;
            line-height: 1.7;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-buttons {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn {
            padding: 1.2rem 2.5rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            font-size: 1.1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.7rem;
            position: relative;
            overflow: hidden;
            font-family: inherit;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-secondary);
            color: var(--surface-white);
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(5, 150, 105, 0.4);
        }

        .btn-secondary {
            background: var(--glass-bg);
            color: var(--surface-white);
            border: 2px solid var(--glass-border);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(255, 255, 255, 0.1);
        }

        /* ENHANCED IMPACT STATS */
        .impact-stats {
            background: var(--surface-white);
            padding: 6rem 0 4rem;
            margin-top: -3rem;
            position: relative;
            z-index: 3;
        }

        .stats-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .stat-card {
            background: var(--surface-white);
            padding: 2.5rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 40px var(--shadow-light);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-secondary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 25px 50px var(--shadow-medium);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-icon {
            font-size: 3.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8rem;
            line-height: 1;
        }

        .stat-label {
            color: var(--medium-text);
            font-weight: 500;
            font-size: 1rem;
            line-height: 1.4;
        }

        /* ENHANCED SECTIONS */
        .section {
            padding: 6rem 0;
            position: relative;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 5rem;
            position: relative;
        }

        .section-title {
            font-size: clamp(2.5rem, 4vw, 3.5rem);
            margin-bottom: 1.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            line-height: 1.2;
        }

        .section-subtitle {
            font-size: 1.3rem;
            color: var(--medium-text);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* PROJECT OVERVIEW */
        .overview {
            background: linear-gradient(135deg, var(--light-sage) 0%, rgba(248, 255, 248, 0.5) 100%);
            position: relative;
        }

        .overview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 10% 20%, rgba(142, 68, 173, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(39, 174, 96, 0.05) 0%, transparent 50%);
        }

        .overview-content {
            display: grid;
            grid-template-columns: 1.2fr 1fr;
            gap: 5rem;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .overview-text h3 {
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .overview-text p {
            margin-bottom: 1.8rem;
            color: var(--dark-text);
            font-size: 1.1rem;
            line-height: 1.7;
        }

        .process-flow {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .process-step {
            background: var(--surface-white);
            padding: 2rem;
            border-radius: 16px;
            border-left: 4px solid var(--accent-spring);
            box-shadow: 0 8px 30px var(--shadow-light);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .process-step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.02) 0%, rgba(142, 68, 173, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .process-step:hover {
            transform: translateX(15px) translateY(-5px);
            box-shadow: 0 15px 40px var(--shadow-medium);
            border-left-color: var(--secondary-teal);
        }

        .process-step:hover::before {
            opacity: 1;
        }

        .step-title {
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8rem;
            font-size: 1.1rem;
        }

        /* ENHANCED ACTIVITY CARDS */
        .activities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 2.5rem;
            margin-top: 4rem;
        }

        .activity-card {
            background: var(--surface-white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 40px var(--shadow-light);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border: 1px solid var(--border-light);
        }

        .activity-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 60px var(--shadow-medium);
        }

        .activity-header {
            padding: 2rem;
            background: var(--gradient-primary);
            color: var(--surface-white);
            position: relative;
            overflow: hidden;
        }

        .activity-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: cardShine 4s ease-in-out infinite;
        }

        @keyframes cardShine {
            0%, 100% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1); opacity: 1; }
        }

        .activity-number {
            font-size: 1.1rem;
            font-weight: 600;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .activity-title {
            font-size: 1.4rem;
            margin-top: 0.8rem;
            font-weight: 700;
            line-height: 1.3;
        }

        .activity-content {
            padding: 2rem;
        }

        .activity-description {
            color: var(--dark-text);
            margin-bottom: 1.5rem;
            line-height: 1.6;
            font-size: 1rem;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.8rem;
            font-size: 0.95rem;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #f3f4f6;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-secondary);
            border-radius: 10px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progressShine 2s ease-in-out infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .activity-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.8rem;
        }

        .tag {
            background: var(--light-sage);
            color: var(--primary-plum);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid var(--border-light);
        }

        .tag:hover {
            background: var(--gradient-primary);
            color: var(--surface-white);
            transform: translateY(-2px);
        }

        /* ENHANCED FUNDING SECTION */
        .funding-category {
            margin-bottom: 4rem;
        }

        .funding-category-title {
            font-size: 1.8rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .funding-category-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--gradient-secondary);
            border-radius: 2px;
        }

        .funding-grid {
            display: grid;
            gap: 2rem;
            margin-top: 2rem;
        }

        .major-funders {
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        }

        .government-partners {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        .industry-partners {
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        }

        .funding-card {
            background: var(--surface-white);
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 10px 40px var(--shadow-light);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
        }

        .funding-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-secondary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .funding-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 25px 50px var(--shadow-medium);
        }

        .funding-card:hover::before {
            transform: scaleX(1);
        }

        .primary-funder {
            border-top: 4px solid var(--secondary-teal);
        }

        .primary-funder::before {
            background: var(--gradient-primary);
        }

        .funding-logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 1.5rem;
            background: var(--light-background);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 2px solid var(--border-light);
        }

        .funding-card:hover .funding-logo {
            transform: scale(1.05);
            border-color: var(--accent-spring);
        }

        .partner-logo {
            max-width: 90px;
            max-height: 90px;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .funding-card:hover .partner-logo {
            transform: scale(1.1);
        }

        .funding-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.8rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .funding-role {
            color: var(--medium-text);
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 1rem;
        }

        .funding-description {
            color: var(--dark-text);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Funding Summary */
        .funding-summary {
            margin-top: 4rem;
            text-align: center;
        }

        .summary-card {
            background: var(--gradient-primary);
            color: var(--surface-white);
            padding: 3rem;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2rem;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 15px 40px rgba(30, 58, 138, 0.3);
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: summaryShine 6s ease-in-out infinite;
        }

        @keyframes summaryShine {
            0%, 100% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1); opacity: 1; }
        }

        .summary-icon {
            font-size: 3rem;
            color: #27AE60;
            margin-bottom: 1rem;
        }

        .summary-content h4 {
            font-size: 1.4rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .summary-amount {
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: #27AE60;
        }

        .summary-content p {
            opacity: 0.9;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .summary-card {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }

            .major-funders,
            .government-partners,
            .industry-partners {
                grid-template-columns: 1fr;
            }

            .funding-logo {
                width: 100px;
                height: 100px;
            }

            .partner-logo {
                max-width: 75px;
                max-height: 75px;
            }
        }
        .team {
            background: linear-gradient(135deg, var(--light-sage) 0%, rgba(248, 255, 248, 0.5) 100%);
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2.5rem;
            margin-top: 4rem;
        }

        .team-card {
            background: var(--surface-white);
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 10px 40px var(--shadow-light);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
        }

        .team-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 25px 50px var(--shadow-medium);
        }

        .team-card:hover::before {
            transform: scaleX(1);
        }

        .team-avatar, .team-photo {
            width: 110px;
            height: 110px;
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(142, 68, 173, 0.2);
            border: 4px solid var(--accent-spring);
            transition: all 0.4s ease;
            position: relative;
        }

        .team-avatar {
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            color: var(--surface-white);
            font-weight: 700;
        }

        .team-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .team-card:hover .team-image,
        .team-card:hover .team-avatar {
            transform: scale(1.1);
        }

        .team-name {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.8rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .team-role {
            color: var(--medium-text);
            margin-bottom: 1rem;
            font-weight: 500;
            font-size: 1rem;
        }

        .team-institution {
            font-size: 0.9rem;
            color: var(--secondary-teal);
            font-weight: 600;
        }

        /* ENHANCED BENEFIT CARDS */
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2.5rem;
            margin-top: 4rem;
        }

        .benefit-card {
            background: var(--surface-white);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px var(--shadow-light);
            border-top: 4px solid var(--secondary-teal);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .benefit-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(142, 68, 173, 0.02) 0%, rgba(39, 174, 96, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .benefit-card:hover {
            transform: translateY(-10px);
            border-top-color: var(--accent-spring);
            box-shadow: 0 25px 50px var(--shadow-medium);
        }

        .benefit-card:hover::before {
            opacity: 1;
        }

        .benefit-icon {
            font-size: 3rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }

        .benefit-card:hover .benefit-icon {
            transform: scale(1.1);
        }

        .benefit-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* ENHANCED RESOURCES */
        .resources {
            background: linear-gradient(135deg, var(--light-sage) 0%, rgba(248, 255, 248, 0.5) 100%);
        }

        .resource-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 3rem;
            background: var(--surface-white);
            border-radius: 50px;
            padding: 0.5rem;
            box-shadow: 0 8px 30px var(--shadow-light);
            max-width: fit-content;
            margin-left: auto;
            margin-right: auto;
        }

        .tab-button {
            padding: 1rem 2rem;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            color: var(--medium-text);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
        }

        .tab-button.active {
            background: var(--gradient-primary);
            color: var(--surface-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(30, 58, 138, 0.3);
        }

        .tab-content {
            display: none;
            animation: fadeInTab 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInTab {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .publication-item {
            background: var(--surface-white);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 30px var(--shadow-light);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-light);
        }

        .publication-item:hover {
            transform: translateX(15px);
            box-shadow: 0 15px 40px var(--shadow-medium);
        }

        .publication-title {
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8rem;
            font-size: 1.1rem;
            line-height: 1.4;
        }

        .publication-status {
            display: inline-block;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .status-published {
            background: var(--gradient-secondary);
            color: var(--surface-white);
        }

        .status-submitted {
            background: linear-gradient(135deg, #f59e0b, #f97316);
            color: var(--surface-white);
        }

        .status-progress {
            background: var(--light-sage);
            color: var(--primary-plum);
            border: 1px solid var(--border-light);
        }

        /* ENHANCED DASHBOARD */
        .timeline-date {
            background: var(--gradient-primary);
            color: var(--surface-white);
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-right: 1.5rem;
            white-space: nowrap;
            box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3);
        }

        .timeline-title {
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        /* ENHANCED CONTACT */
        .contact {
            background: var(--gradient-primary);
            color: var(--surface-white);
            position: relative;
            overflow: hidden;
        }

        .contact::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.1) 0%, transparent 50%);
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
            position: relative;
            z-index: 1;
        }

        .contact-info h3 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
            font-weight: 700;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }

        .contact-item:hover {
            transform: translateX(10px);
        }

        .contact-item i {
            font-size: 1.3rem;
            margin-right: 1rem;
            color: #27AE60;
            transition: transform 0.3s ease;
        }

        .contact-item:hover i {
            transform: scale(1.2);
        }

        .contact-form {
            background: var(--glass-bg);
            padding: 2.5rem;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--surface-white);
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.95);
            color: var(--dark-text);
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            background: var(--surface-white);
            box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.2);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        /* ENHANCED FOOTER */
        .footer {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: var(--surface-white);
            padding: 4rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(5, 150, 105, 0.1) 0%, transparent 50%);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
            position: relative;
            z-index: 1;
        }

        .footer-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .footer-section:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .footer-section h4 {
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #27AE60, #1B4332);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 1.2rem;
            letter-spacing: 0.5px;
        }

        .footer-section p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .footer-section ul {
            list-style: none;
            padding: 0;
        }

        .footer-section ul li {
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section ul li:hover {
            transform: translateX(5px);
        }

        .footer-section a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            display: inline-block;
        }

        .footer-section a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background: linear-gradient(90deg, #27AE60, #1B4332);
            transition: width 0.3s ease;
        }

        .footer-section a:hover {
            color: #27AE60;
            transform: translateX(3px);
        }

        .footer-section a:hover::after {
            width: 100%;
        }

        /* Social Media Links */
        .footer-section div[style*="margin-top"] {
            display: flex;
            gap: 1rem;
            margin-top: 2rem !important;
        }

        .footer-section div[style*="margin-top"] a {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transform: none;
        }

        .footer-section div[style*="margin-top"] a:hover {
            background: linear-gradient(135deg, #27AE60, #1B4332);
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
        }

        .footer-section div[style*="margin-top"] a::after {
            display: none;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
            position: relative;
            z-index: 1;
            background: rgba(0, 0, 0, 0.2);
            margin: 0 -2rem -2rem;
            padding-left: 2rem;
            padding-right: 2rem;
            backdrop-filter: blur(10px);
        }

        .footer-bottom p {
            font-size: 0.9rem;
            margin: 0;
            padding: 1.5rem 0;
        }

        /* Dashboard specific styles */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-top: 3rem;
        }

        .chart-container {
            background: var(--surface-white);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px var(--shadow-light);
            border: 1px solid var(--border-light);
        }

        .chart-container h3 {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
        }

        .timeline {
            background: var(--surface-white);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px var(--shadow-light);
            border: 1px solid var(--border-light);
        }

        .timeline h3 {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
        }

        .timeline-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--border-light);
        }

        .timeline-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-description {
            color: var(--medium-text);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* ENHANCED RESPONSIVE */
        @media (max-width: 1024px) {
            .overview-content {
                grid-template-columns: 1fr;
                gap: 3rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .contact-grid {
                grid-template-columns: 1fr;
                gap: 3rem;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .mobile-menu {
                display: block;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .stats-container,
            .activities-grid,
            .team-grid,
            .benefits-grid {
                grid-template-columns: 1fr;
            }

            .resource-tabs {
                flex-direction: column;
                border-radius: 15px;
            }

            .section {
                padding: 4rem 0;
            }

            .contact-grid {
                grid-template-columns: 1fr;
            }

            .timeline-item {
                flex-direction: column;
                gap: 1rem;
            }

            .timeline-date {
                margin-right: 0;
                margin-bottom: 0.5rem;
                align-self: flex-start;
            }
        }

        /* SCROLL ANIMATIONS */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* ENHANCED SCROLLBAR */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-sage);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-teal);
        }

        /* LOADING ANIMATION */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: var(--surface-white);
            animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="#home" class="logo">
                <img src="images/logo/peace-logo-main.svg" alt="PeaCE Project" style="height: 96px; width: auto;">
            </a>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#overview" class="nav-link">Overview</a></li>
                <li><a href="#activities" class="nav-link">Activities</a></li>
                <li><a href="#team" class="nav-link">Team</a></li>
                <li><a href="#impact" class="nav-link">Impact</a></li>
                <li><a href="#resources" class="nav-link">Resources</a></li>
                <li><a href="#dashboard" class="nav-link">Dashboard</a></li>
                <li><a href="#funding" class="nav-link">Funding</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
                <li><a href="/users/sign_in" class="nav-link">LIMS Login</a></li>
            </ul>
            <div class="mobile-menu">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>Pea, Climate Efficient</h1>
            <p class="hero-subtitle">
                Developing Climate-Resilient, Low Carbon Footprint Field Pea as a Preferred Rotation Crop Through Genomic Technologies
            </p>
            <div class="cta-buttons">
                <a href="#overview" class="btn btn-primary">
                    <i class="fas fa-rocket"></i> Explore Project
                </a>
                <a href="#resources" class="btn btn-secondary">
                    <i class="fas fa-download"></i> Access Publications & Resources
                </a>
            </div>
        </div>
    </section>

    <!-- Impact Stats -->
    <section class="impact-stats">
        <div class="stats-container">
            <div class="stat-card fade-in">
                <div class="stat-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <div class="stat-number"> Towards 30% goal</div>
                <div class="stat-label">Contribution to GHG Emission Reduction Target</div>
            </div>
            <div class="stat-card fade-in">
                <div class="stat-icon">
                    <i class="fas fa-dna"></i>
                </div>
                <div class="stat-number">4,500+</div>
                <div class="stat-label">Diverse Pea Accessions</div>
            </div>
            <div class="stat-card fade-in">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-number">$7.1M</div>
                <div class="stat-label">Total Project Investment</div>
            </div>
            <div class="stat-card fade-in">
                <div class="stat-icon">
                    <i class="fas fa-university"></i>
                </div>
                <div class="stat-number">25+</div>
                <div class="stat-label">Research Institutions</div>
            </div>
        </div>
    </section>

    <!-- Project Overview -->
    <section id="overview" class="section overview">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Project Overview</h2>
                <p class="section-subtitle">
                    Transforming Canadian agriculture through climate-smart pea development
                </p>
            </div>
            <div class="overview-content">
                <div class="overview-text fade-in">
                    <h3>Climate Solution Through Innovation</h3>
                    <p>
                        Field pea represents a major climate solution for Canadian agriculture, capable of fixing large amounts of atmospheric nitrogen and producing protein with a significantly lower carbon footprint than most other crops or livestock.
                    </p>
                    <p>
                        The PeaCE project addresses critical challenges including root rot resistance, drought tolerance, and enhanced nutritional quality through cutting-edge genomic technologies and interdisciplinary collaboration.
                    </p>
                    <p>
                        By providing breeders with elite germplasm and innovative tools, this project will enable producers to enhance crop rotation with carbon-efficient pea instead of carbon-intensive crops, directly contributing to Canada's 2030 GHG reduction targets.
                    </p>
                </div>
                <div class="overview-visual fade-in">
                    <div class="process-flow">
                        <div class="process-step">
                            <div class="step-title">1. Genomic Resource Development</div>
                            <div>Assemble and characterize 4,500+ diverse pea accessions</div>
                        </div>
                        <div class="process-step">
                            <div class="step-title">2. Phenotypic Screening</div>
                            <div>Evaluate for draught resistance, root rot resistance, and climate resilience</div>
                        </div>
                        <div class="process-step">
                            <div class="step-title">3. Gene Editing & Validation</div>
                            <div>Develop enhanced varieties through precision breeding</div>
                        </div>
                        <div class="process-step">
                            <div class="step-title">4. Industry Integration</div>
                            <div>Transfer elite germplasm to breeding programs</div>
                        </div>
                        <div class="process-step">
                            <div class="step-title">5. Climate Impact</div>
                            <div>Achieve measurable GHG emission reductions</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Activities -->
    <section id="activities" class="section">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Research Activities</h2>
                <p class="section-subtitle">
                    Multiple integrated activities driving climate-smart pea development
                </p>
            </div>
            <div class="activities-grid">
                <div class="activity-card fade-in">
                    <div class="activity-header">
                        <div class="activity-number">Activity 1</div>
                        <div class="activity-title">Genomic Resource Development</div>
                    </div>
                    <div class="activity-content">
                        <p class="activity-description">
                            Harness genetic diversity through comprehensive genomic characterization of 4,500 pea accessions, including modern cultivars, landraces, and wild relatives.
                        </p>
                        <div class="activity-progress">
                            <div class="progress-label">
                                <span>Progress</span>
                                <span>80%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 80%"></div>
                            </div>
                        </div>
                        <div class="activity-tags">
                            <span class="tag">Genomics</span>
                            <span class="tag">Sequencing</span>
                            <span class="tag">Diversity</span>
                        </div>
                    </div>
                </div>

                <div class="activity-card fade-in">
                    <div class="activity-header">
                        <div class="activity-number">Activity 2</div>
                        <div class="activity-title">Health & GHG Assessment</div>
                    </div>
                    <div class="activity-content">
                        <p class="activity-description">
                            Improve pea health through root rot resistance screening and estimate GHG mitigation potential through enhanced crop rotation strategies.
                        </p>
                        <div class="activity-progress">
                            <div class="progress-label">
                                <span>Progress</span>
                                <span>65%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 65%"></div>
                            </div>
                        </div>
                        <div class="activity-tags">
                            <span class="tag">Disease Resistance</span>
                            <span class="tag">GHG Analysis</span>
                            <span class="tag">Phenotyping</span>
                        </div>
                    </div>
                </div>

                <div class="activity-card fade-in">
                    <div class="activity-header">
                        <div class="activity-number">Activity 3</div>
                        <div class="activity-title">Adaptive Breeding</div>
                    </div>
                    <div class="activity-content">
                        <p class="activity-description">
                            Develop advanced breeding strategies including genomic selection and MAGIC population development for rapid trait introgression.
                        </p>
                        <div class="activity-progress">
                            <div class="progress-label">
                                <span>Progress</span>
                                <span>40%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 40%"></div>
                            </div>
                        </div>
                        <div class="activity-tags">
                            <span class="tag">Breeding</span>
                            <span class="tag">MAGIC</span>
                            <span class="tag">Selection</span>
                        </div>
                    </div>
                </div>

                <div class="activity-card fade-in">
                    <div class="activity-header">
                        <div class="activity-number">Activity 4</div>
                        <div class="activity-title">Gene Editing & Validation</div>
                    </div>
                    <div class="activity-content">
                        <p class="activity-description">
                            Validate candidate genes and develop novel traits through CRISPR/Cas9 gene editing and mutagenesis approaches.
                        </p>
                        <div class="activity-progress">
                            <div class="progress-label">
                                <span>Progress</span>
                                <span>55%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 55%"></div>
                            </div>
                        </div>
                        <div class="activity-tags">
                            <span class="tag">CRISPR</span>
                            <span class="tag">Gene Editing</span>
                            <span class="tag">Validation</span>
                        </div>
                    </div>
                </div>

                <div class="activity-card fade-in">
                    <div class="activity-header">
                        <div class="activity-number">Activity 5</div>
                        <div class="activity-title">GE³LS Research</div>
                    </div>
                    <div class="activity-content">
                        <p class="activity-description">
                            Investigate legal, social, and economic implications of genomic technologies in pea development and adoption.
                        </p>
                        <div class="activity-progress">
                            <div class="progress-label">
                                <span>Progress</span>
                                <span>70%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 70%"></div>
                            </div>
                        </div>
                        <div class="activity-tags">
                            <span class="tag">Policy</span>
                            <span class="tag">Economics</span>
                            <span class="tag">Adoption</span>
                        </div>
                    </div>
                </div>

                <div class="activity-card fade-in">
                    <div class="activity-header">
                        <div class="activity-number">Activity 6</div>
                        <div class="activity-title">Data and Project Management</div>
                    </div>
                    <div class="activity-content">
                        <p class="activity-description">
                            Develop comprehensive data management systems including PeaCE-DB database and knowledge mobilization platforms. Establish and maintain essential research infrastructure including phenotyping equipment and computational resources.
                        </p>
                        <div class="activity-progress">
                            <div class="progress-label">
                                <span>Progress</span>
                                <span>85%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="activity-tags">
                            <span class="tag">Infrastructure</span>
                            <span class="tag">Equipment</span>
                            <span class="tag">Technology</span>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team -->
    <section id="team" class="section team">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Research Team</h2>
                <p class="section-subtitle">
                    World-class expertise across genomics, breeding, and climate science
                </p>
            </div>
            <div class="team-grid">
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/marcus-samuel.jpg" alt="Dr. Marcus Samuel" class="team-image">
                    </div>
                    <div class="team-name">Dr. Marcus Samuel</div>
                    <div class="team-role">Project Co-Lead</div>
                    <div class="team-institution">University of Calgary, Alberta</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/sateesh-kagale.jpg" alt="Dr. Sateesh Kagale" class="team-image">
                    </div>
                    <div class="team-name">Dr. Sateesh Kagale</div>
                    <div class="team-role">Project Co-Lead</div>
                    <div class="team-institution">National Research Council Canada, Saskatoon</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/tom-warkentin.jpg" alt="Dr. Tom Warkentin" class="team-image">
                    </div>
                    <div class="team-name">Dr. Tom Warkentin</div>
                    <div class="team-role">Pea Breeding Lead</div>
                    <div class="team-institution">University of Saskatchewan, Saskatoon</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Syama-Chatterton.jpg" alt="Dr. Syama Chatterton" class="team-image">
                    </div>
                    <div class="team-name">Dr. Syama Chatterton</div>
                    <div class="team-role">Plant Pathology Lead</div>
                    <div class="team-institution">Agriculture & Agri-Food Canada, Lethbridge</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Raju-Soolanayakanahally.jpg" alt="Dr. Raju Soolanayakanahally" class="team-image">
                    </div>
                    <div class="team-name">Dr. Raju Soolanayakanahally</div>
                    <div class="team-role">Stress Physiology Lead</div>
                    <div class="team-institution">Agriculture & Agri-Food Canada, Saskatoon</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Reynald-Lemke.jpg" alt="Dr. Raynald Lemke" class="team-image">
                    </div>
                    <div class="team-name">Dr. Raynald Lemke</div>
                    <div class="team-role">GHG Specialist</div>
                    <div class="team-institution">Agriculture & Agri-Food Canada, Saskatoon</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Nick-Tyack.jpg" alt="Dr. Nik Tyack" class="team-image">
                    </div>
                    <div class="team-name">Dr. Nik Tyack</div>
                    <div class="team-role">GE³LS Research</div>
                    <div class="team-institution">University of Saskatchewan</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Yang-Yang.jpg" alt="Dr. Yang Yang" class="team-image">
                    </div>
                    <div class="team-name">Dr. Yang Yang</div>
                    <div class="team-role">GE³LS Research</div>
                    <div class="team-institution">University of Saskatchewan</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Kim-Sanderson.jpg" alt="Dr. Kim Sanderson" class="team-image">
                    </div>
                    <div class="team-name">Dr. Kim Sanderson</div>
                    <div class="team-role">GE³LS Research</div>
                    <div class="team-institution">University of Saskatchewan</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Scott-MacKay.jpg" alt="Dr. Scott MacKay" class="team-image">
                    </div>
                    <div class="team-name">Dr. Scott MacKay</div>
                    <div class="team-role">Informatics Lead</div>
                    <div class="team-institution">University of Alberta</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/David-Wishart.jpg" alt="Dr. David Wishart" class="team-image">
                    </div>
                    <div class="team-name">Dr. David Wishart</div>
                    <div class="team-role">Informatics Lead</div>
                    <div class="team-institution">University of Alberta</div>
                </div>
                <div class="team-card fade-in">
                    <div class="team-photo">
                        <img src="images/team/Abhinandan-kumar.jpg" alt="Dr. Abhinandan Kumar" class="team-image">
                    </div>
                    <div class="team-name">Dr. Abhinandan Kumar</div>
                    <div class="team-role">Scientific Project Manager</div>
                    <div class="team-institution">University of Calgary, Alberta</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Funding & Partners -->
    <section id="funding" class="section">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Funding & Partners</h2>
                <p class="section-subtitle">
                    Proudly supported by leading organizations driving agricultural innovation in Canada
                </p>
            </div>
            
            <!-- Major Funding Agencies -->
            <div class="funding-category fade-in">
                <h3 class="funding-category-title">Major Funding Agencies</h3>
                <div class="funding-grid major-funders">
                    <div class="funding-card primary-funder fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/genome-canada-logo.png" alt="Genome Canada" class="partner-logo">
                        </div>
                        <div class="funding-name">Genome Canada</div>
                        <div class="funding-role">Lead Funding Agency</div>
                        <div class="funding-description">Supporting large-scale genomics research initiatives across Canada</div>
                    </div>
                    <div class="funding-card primary-funder fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/genome-alberta-logo.png" alt="Genome Alberta" class="partner-logo">
                        </div>
                        <div class="funding-name">Genome Alberta</div>
                        <div class="funding-role">Provincial Funding Partner</div>
                        <div class="funding-description">Advancing genomics research and innovation in Alberta</div>
                    </div>
                    <div class="funding-card primary-funder fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/genome-prairie-logo.png" alt="Genome Prairie" class="partner-logo">
                        </div>
                        <div class="funding-name">Genome Prairie</div>
                        <div class="funding-role">Regional Funding Partner</div>
                        <div class="funding-description">Supporting prairie-focused genomics research initiatives</div>
                    </div>
                </div>
            </div>

            <!-- Government Partners -->
            <div class="funding-category fade-in">
                <h3 class="funding-category-title">Government Partners</h3>
                <div class="funding-grid government-partners">
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/aafc-logo.png" alt="Agriculture and Agri-Food Canada" class="partner-logo">
                        </div>
                        <div class="funding-name">Agriculture & Agri-Food Canada</div>
                        <div class="funding-role">Federal Research Partner</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/nrc-logo.png" alt="National Research Council Canada" class="partner-logo">
                        </div>
                        <div class="funding-name">National Research Council</div>
                        <div class="funding-role">Federal Research Institution</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/alberta-innovates-logo.png" alt="Alberta Innovates" class="partner-logo">
                        </div>
                        <div class="funding-name">Alberta Innovates</div>
                        <div class="funding-role">Provincial Innovation Agency</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/saskatchewan-logo.png" alt="Government of Saskatchewan" class="partner-logo">
                        </div>
                        <div class="funding-name">Government of Saskatchewan</div>
                        <div class="funding-role">Provincial Government Partner</div>
                    </div>
                </div>
            </div>

            <!-- Industry Partners -->
            <div class="funding-category fade-in">
                <h3 class="funding-category-title">Industry & Research Partners</h3>
                <div class="funding-grid industry-partners">
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/corteva-logo.png" alt="Corteva Agriscience" class="partner-logo">
                        </div>
                        <div class="funding-name">Corteva Agriscience</div>
                        <div class="funding-role">Industry Research Partner</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/saskatchewan-pulse-growers-logo.png" alt="Saskatchewan Pulse Growers" class="partner-logo">
                        </div>
                        <div class="funding-name">Saskatchewan Pulse Growers</div>
                        <div class="funding-role">Producer Organization</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/alberta-pulse-growers-logo.png" alt="Alberta Pulse Growers" class="partner-logo">
                        </div>
                        <div class="funding-name">Alberta Pulse Growers</div>
                        <div class="funding-role">Producer Organization</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/wgrf-logo.png" alt="Western Grains Research Foundation" class="partner-logo">
                        </div>
                        <div class="funding-name">Western Grains Research Foundation</div>
                        <div class="funding-role">Research Funding Organization</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/pacbio-logo.png" alt="PacBio" class="partner-logo">
                        </div>
                        <div class="funding-name">PacBio</div>
                        <div class="funding-role">Technology Partner</div>
                    </div>
                    <div class="funding-card fade-in">
                        <div class="funding-logo">
                            <img src="images/partners/rdar-logo.png" alt="Results Driven Agriculture Research" class="partner-logo">
                        </div>
                        <div class="funding-name">RDAR</div>
                        <div class="funding-role">Agriculture Research Funder</div>
                    </div>
                </div>
            </div>

            <!-- Funding Summary -->
            <div class="funding-summary fade-in">
                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-hand-holding-usd"></i>
                    </div>
                    <div class="summary-content">
                        <h4>Total Project Investment</h4>
                        <div class="summary-amount">$7.1 Million CAD</div>
                        <p>Multi-partner funding supporting world-class genomics research for climate-smart agriculture</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact & Benefits -->
    <section id="impact" class="section">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Impact & Benefits</h2>
                <p class="section-subtitle">
                    Transformative outcomes for Canadian agriculture and climate goals
                </p>
            </div>
            <div class="benefits-grid">
                <div class="benefit-card fade-in">
                    <div class="benefit-icon">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                    <div class="benefit-title">Climate Action</div>
                    <p>Significant reduction in agricultural GHG emissions through enhanced pea crop rotation, contributing to Canada's 2030 climate targets.</p>
                </div>
                <div class="benefit-card fade-in">
                    <div class="benefit-icon">
                        <i class="fas fa-tractor"></i>
                    </div>
                    <div class="benefit-title">Producer Benefits</div>
                    <p>Enhanced profitability for farmers through improved yields, disease resistance, and reduced fertilizer requirements.</p>
                </div>
                <div class="benefit-card fade-in">
                    <div class="benefit-icon">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="benefit-title">Industry Growth</div>
                    <p>Strengthened Canadian protein industry with access to high-quality, sustainable pea varieties for processing.</p>
                </div>
                <div class="benefit-card fade-in">
                    <div class="benefit-icon">
                        <i class="fas fa-globe-americas"></i>
                    </div>
                    <div class="benefit-title">Global Leadership</div>
                    <p>Positioning Canada as a world leader in sustainable agriculture and plant-based protein production.</p>
                </div>
                <div class="benefit-card fade-in">
                    <div class="benefit-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <div class="benefit-title">Scientific Innovation</div>
                    <p>Cutting-edge genomic resources and technologies transferable to other crops and research programs.</p>
                </div>
                <div class="benefit-card fade-in">
                    <div class="benefit-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="benefit-title">Training & Development</div>
                    <p>World-class training for highly qualified personnel in genomics, breeding, and agricultural innovation.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Resources & Publications -->
    <section id="resources" class="section resources">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Resources & Publications</h2>
                <p class="section-subtitle">
                    Access our latest research outputs and data resources
                </p>
            </div>

            <div class="resource-tabs">
                <button class="tab-button active" data-tab="publications">Publications</button>
                <button class="tab-button" data-tab="datasets">Datasets</button>
                <button class="tab-button" data-tab="presentations">Presentations</button>
                <button class="tab-button" data-tab="reports">Reports</button>
            </div>

            <div id="publications" class="tab-content active">
                <div class="publication-item fade-in">
                    <div class="publication-title">Towards (super-)pangenomics in grain legumes</div>
                    <div class="publication-authors">Kagale, S., et al.</div>
                    <span class="publication-status status-submitted">Submitted</span>
                </div>
                <div class="publication-item fade-in">
                    <div class="publication-title">Pisum 60+: Unlocking the pea pan-genome for crop innovation</div>
                    <div class="publication-authors">Konkin, D., et al.</div>
                    <span class="publication-status status-published">Published</span>
                </div>
                <div class="publication-item fade-in">
                    <div class="publication-title">The hunt for novel sources of resistance to Aphanomyces root rot in pea and lentil</div>
                    <div class="publication-authors">Chatterton, S., et al.</div>
                    <span class="publication-status status-published">Published</span>
                </div>
            </div>

            <div id="datasets" class="tab-content">
                <div class="publication-item fade-in">
                    <div class="publication-title">CSPGR Genome Assemblies</div>
                    <div class="publication-authors">Climate-Smart Pea Germplasm Resource</div>
                    <span class="publication-status status-progress">In Progress</span>
                </div>
                <div class="publication-item fade-in">
                    <div class="publication-title">MAGIC Population Phenotyping Data</div>
                    <div class="publication-authors">Multi-parent Advanced Generation Inter-Cross</div>
                    <span class="publication-status status-progress">In Progress</span>
                </div>
                <div class="publication-item fade-in">
                    <div class="publication-title">GHG Measurement Dataset</div>
                    <div class="publication-authors">Cumulative GHG emissions from crop rotations</div>
                    <span class="publication-status status-progress">In Progress</span>
                </div>
            </div>

            <div id="presentations" class="tab-content">
                <div class="publication-item fade-in">
                    <div class="publication-title">Building high-quality comparative genomic resources for legumes</div>
                    <div class="publication-authors">AGBT Agriculture 2025</div>
                    <span class="publication-status status-published">Delivered</span>
                </div>
                <div class="publication-item fade-in">
                    <div class="publication-title">Climate-efficient pea breeding strategies</div>
                    <div class="publication-authors">Canadian Plant Biotechnology Conference</div>
                    <span class="publication-status status-progress">Planned</span>
                </div>
            </div>

            <div id="reports" class="tab-content">
                <div class="publication-item fade-in">
                    <div class="publication-title">Breeding priorities for high-value pea processing sector</div>
                    <div class="publication-authors">GE³LS Market Analysis Report</div>
                    <span class="publication-status status-submitted">Draft Complete</span>
                </div>
                <div class="publication-item fade-in">
                    <div class="publication-title">Annual Progress Report 2024</div>
                    <div class="publication-authors">PeaCE Project Team</div>
                    <span class="publication-status status-published">Published</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard -->
    <section id="dashboard" class="section dashboard">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Project Dashboard</h2>
                <p class="section-subtitle">
                    Real-time project progress and key milestones
                </p>
            </div>
            <div class="dashboard-grid">
                <div class="chart-container fade-in">
                    <h3>Activity Progress Overview</h3>
                    <div style="display: flex; flex-direction: column; gap: 1.5rem; margin-top: 2rem;">
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; font-weight: 600;">
                                <span>Activity 1: Genomic Resources</span>
                                <span style="color: var(--accent-emerald);">80%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 80%"></div>
                            </div>
                        </div>
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; font-weight: 600;">
                                <span>Activity 2: Health & GHG</span>
                                <span style="color: var(--accent-emerald);">65%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 65%"></div>
                            </div>
                        </div>
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; font-weight: 600;">
                                <span>Activity 3: Adaptive Breeding</span>
                                <span style="color: var(--accent-emerald);">40%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 40%"></div>
                            </div>
                        </div>
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; font-weight: 600;">
                                <span>Activity 4: Gene Editing</span>
                                <span style="color: var(--accent-emerald);">55%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 55%"></div>
                            </div>
                        </div>
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; font-weight: 600;">
                                <span>Activity 5: GE³LS Research</span>
                                <span style="color: var(--accent-emerald);">70%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 70%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="timeline fade-in">
                    <h3>Upcoming Milestones</h3>
                    <div class="timeline-item">
                        <div class="timeline-date">Jun 2025</div>
                        <div class="timeline-content">
                            <div class="timeline-title">Field Trial Completion</div>
                            <div class="timeline-description">Complete phenotyping of CSPGR collection</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">Aug 2025</div>
                        <div class="timeline-content">
                            <div class="timeline-title">Genome Assembly Release</div>
                            <div class="timeline-description">Publish reference genomes for key accessions</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">Dec 2025</div>
                        <div class="timeline-content">
                            <div class="timeline-title">Gene Editing Validation</div>
                            <div class="timeline-description">Complete functional validation of candidate genes</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">Mar 2026</div>
                        <div class="timeline-content">
                            <div class="timeline-title">Breeding Program Integration</div>
                            <div class="timeline-description">Transfer elite germplasm to industry partners</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact -->
    <section id="contact" class="section contact">
        <div class="container">
            <div class="contact-grid">
                <div class="contact-info fade-in">
                    <h3>Get In Touch</h3>
                    <p style="margin-bottom: 2rem;">
                        Connect with our team for collaboration opportunities, data sharing and access options, or project inquiries.
                    </p>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+****************</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>University of Calgary, AB, Canada</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-globe"></i>
                        <span>www.PeaCEproject.ca</span>
                    </div>
                </div>
                <div class="contact-form fade-in">
                    <h4 style="margin-bottom: 1.5rem;">Send us a message</h4>
                    <form>
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <input type="text" id="subject" name="subject" required>
                        </div>
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" name="message" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>PeaCE Project</h4>
                    <p>Developing climate-resilient, low carbon footprint field pea through genomic innovation for a sustainable agricultural future.</p>
                    <div style="margin-top: 2rem;">
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="GitHub"><i class="fab fa-github"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#overview">Project Overview</a></li>
                        <li><a href="#activities">Research Activities</a></li>
                        <li><a href="#team">Research Team</a></li>
                        <li><a href="#resources">Publications</a></li>
                        <li><a href="#dashboard">Progress Dashboard</a></li>
                        <li><a href="#funding">Funding & Partners</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Partners & Funding</h4>
                    <ul>
                        <li><a href="#funding">View All Partners</a></li>
                        <li><a href="#" target="_blank">Genome Canada</a></li>
                        <li><a href="#" target="_blank">University of Calgary</a></li>
                        <li><a href="#" target="_blank">NRC Canada</a></li>
                        <li><a href="#" target="_blank">Agriculture & Agri-Food Canada</a></li>
                        <li><a href="#" target="_blank">Saskatchewan Pulse Growers</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="#">PeaCE Database</a></li>
                        <li><a href="#">Data Portal</a></li>
                        <li><a href="#">API Documentation</a></li>
                        <li><a href="#">User Guides</a></li>
                        <li><a href="#contact">Contact & Support</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 PeaCE Project. All rights reserved. | <a href="#" style="color: #27AE60;">Privacy Policy</a> | <a href="#" style="color: #27AE60;">Terms of Use</a></p>
            </div>
        </div>
    </footer>

    <script>
        // Enhanced navigation scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                const offset = 100;
                const targetPosition = target.offsetTop - offset;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            });
        });

        // Enhanced intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Tab switching functionality
        function switchTab(tabName, buttonElement) {
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Remove active class from all tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Add active class to clicked button
            if (buttonElement) {
                buttonElement.classList.add('active');
            }
            
            // Show the selected tab content
            const targetTab = document.getElementById(tabName);
            if (targetTab) {
                targetTab.classList.add('active');
            }
        }

        // Event-driven tab switching
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function() {
                    const tabName = this.getAttribute('data-tab');
                    switchTab(tabName, this);
                });
            });
        });

        // Enhanced form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<div class="loading"></div> Sending...';
            submitBtn.disabled = true;

            setTimeout(() => {
                submitBtn.innerHTML = '<i class="fas fa-check"></i> Message Sent!';
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    this.reset();
                }, 2000);
            }, 2000);
        });

        // Enhanced progress animation
        function animateProgress() {
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
            });
        }

        // Trigger progress animation when dashboard is visible
        const dashboardObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateProgress();
                }
            });
        }, { threshold: 0.5 });

        const dashboard = document.getElementById('dashboard');
        if (dashboard) {
            dashboardObserver.observe(dashboard);
        }

        // Mobile menu toggle
        document.querySelector('.mobile-menu').addEventListener('click', function() {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.toggle('active');
        });

        // Parallax effect for hero section and background
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            const backgroundElement = document.querySelector('body::before');
            
            // Hero parallax effect
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
            
            // Dynamic background opacity based on scroll
            const scrollPercentage = scrolled / (document.body.scrollHeight - window.innerHeight);
            const newOpacity = 0.08 + (scrollPercentage * 0.07); // Gradually increase opacity from 0.08 to 0.15
            document.documentElement.style.setProperty('--background-opacity', newOpacity);
        });

        // Add loading states to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (!this.classList.contains('loading')) {
                    this.classList.add('loading');
                    setTimeout(() => {
                        this.classList.remove('loading');
                    }, 1000);
                }
            });
        });

        // Auto-update copyright year
        document.querySelector('.footer-bottom p').innerHTML =
            `&copy; ${new Date().getFullYear()} PeaCE Project. All rights reserved. | <a href="#" style="color: #27AE60;">Privacy Policy</a> | <a href="#" style="color: #27AE60;">Terms of Use</a>`;
    </script>
</body>
</html>
                

