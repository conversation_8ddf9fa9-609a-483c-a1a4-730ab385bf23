h1 Bulk Import General Info
br

ul.nav.nav-tabs
  - BulkImport.uniq.pluck(:category).each do |category|
    li.nav-item
      - classes = ['nav-link']
      - classes << 'active' if category == @category
      = link_to "#{category.pluralize}",
          eval("bulk_import_#{category.pluralize.downcase}_path(category: category)"), class: classes
br

h2 Instructions 
ol
  li
    |  Click to download: 
    = link_to 'characteristics_import_template.csv', download_characteristics_template_path
  li
    |  Paste data into the template file that corresponds to the template's given headers. Please refer to the figure below which shows an example input. Data must adhere to strict formatting to ensure database integrity. Incorrect input will not be imported.
    ul
      li Do not add, remove, or re-order headings in the template file.
      li Note that the following columns are required data fields and cannot be left blank: <b>Unique Tree Code</b> and <b>Age Recorded (yr)</b>.
      li Leave fields without information blank. Do not put placeholders (e.g. X, N/A).
      li Note that this can be used to both <b>import new characteristics</b> and <b>update existing ones</b>.

br
table.image
  caption[align="bottom"]
    h5
      | Figure 1. Example input for bulk import using characteristic_import_template.csv
  tr
    td
      = image_tag("characteristics_bulk_import.png", :alt => "Example input for characteristic import using characteristic_import_template.csv")

br
ol[start="3"]
  li Upload the filled-in template (saved as a CSV) to the #{link_to "General Files", general_files_path} page.
  li Select the newly uploaded file using the drop down list below and then press <b>Import</b>. If there are any errors with the file's data formatting, an error message will appear and the import process will stop.
br

h2 Choose File for Import 

= form_tag bulk_import_characteristics_path do
  = collection_select(:file, :id, GeneralFile.all.reverse, :id, :title, {:include_blank => "Select an Uploaded File"}, {class: "select2-basic"})
  br
  br
  = submit_tag "Import", disabled: true
br
