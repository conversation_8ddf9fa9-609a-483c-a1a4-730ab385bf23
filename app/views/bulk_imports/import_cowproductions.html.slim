h1 Bulk Cow Production Data
br

ul.nav.nav-tabs
  - BulkImport.uniq.pluck(:category).each do |category|
    li.nav-item
      - classes = ['nav-link']
      - classes << 'active' if category == @category
      - if category == "CowProduction"
        = link_to "Cow Production",
          eval("bulk_import_#{category.pluralize.downcase}_path(category: category)"), class: classes
      - else  
        = link_to "#{category.pluralize}",
          eval("bulk_import_#{category.pluralize.downcase}_path(category: category)"), class: classes
br

h2 Instructions 
ol
  li
    |  Click to download: 
    = link_to 'production_import_template.csv', download_cow_production_template_path
  li
    |  Paste data into the template file that corresponds to the template's given headers. Please refer to the figure below which shows an example input. Data must adhere to strict formatting to ensure database integrity. Incorrect input will not be imported.
    ul
      li Do not add, remove, or re-order headings in the template file.
      li Note that the following columns are required data fields and cannot be left blank: <b>CW_TAG</b>, <b>CW_GENO_ID</b>, <b>CW_BRDT</b>, <b>CW_YR_LETTER</b>, <b>CW_AGE_JUL2025_YR</b>, <b>RFI_gEPD</b>, and <b>RANCH</b>.
      li Leave fields without information blank. Do not put placeholders (e.g. X, N/A).
      /li Note that this can be used to both <b>import new characteristics</b> and <b>update existing ones</b>.

br
table.image
  caption[align="bottom"]
    h5
      | Figure 1. Example input for bulk import using production_import_template.csv
  tr
    td
      = image_tag("production_bulk_import.png", :alt => "Example input for cow production import using production_import_template.csv", :style => "max-height: 150px;") 

br
ol[start="3"]
  li Upload the filled-in template (saved as a CSV) to the #{link_to "General Files", general_files_path} page.
  li Select the newly uploaded file using the drop down list below and then press <b>Import</b>. If there are any errors with the file's data formatting, an error message will appear and the import process will stop.
br

h2 Choose File for Import 

= form_tag bulk_import_cowproductions_path do
  = collection_select(:file, :id, GeneralFile.all.reverse, :id, :title, {:include_blank => "Select an Uploaded File"}, {class: "select2-basic"})
  br
  br
  = submit_tag "Import", disabled: false
br
