h1
  |  Bulk Import GHG and Soil
br

ul.nav.nav-tabs
  - ["General Info", "Veg and Omics", "GHG and Soil", "Production"].each do |tab_name|
    / - BulkImport.uniq.pluck(:category).each do |category|
    li.nav-item
      - classes = ['nav-link']
      / - classes << 'active' if category == @category
      = link_to "#{tab_name.pluralize}",
          eval("bulk_import_#{category.pluralize.downcase}_path(category: category)"), class: classes
br

.form
  = select_tag 'measurement_type', options_from_collection_for_select(BulkImport.where("category = 'Measurement'").order(:name), 'name', 'display_measurement_name'), {:include_blank => "Select Measurement Type", class: "select2-basic"}

.measurements_container
  - MeasurementType.order(:name).pluck(:name).each do |measurement_type_name|
    - measurement_type_parameter = measurement_type_name.parameterize.underscore

    div id="import_#{measurement_type_parameter}"
      = render "bulk_imports/measurement_import_pages/#{measurement_type_parameter}_import"
