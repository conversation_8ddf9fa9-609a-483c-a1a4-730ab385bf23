br
h1 Lesion Length
h2 Instructions 
ol
  li
    |  Click to download: 
    = link_to 'measurements_lesion_length_import_template.csv', download_measurements_lesion_length_template_path
  li
    |  Paste data into the template file that corresponds to the template's given headers. Please refer to the figure below which shows an example input. Data must adhere to strict formatting to ensure database integrity. Incorrect input will not be imported. <b>Note that the template combines both sample and measurement data.</b>
    ul
      li Do not add, remove, or re-order headings in the template file.
      li Note that the column <b>Unique Tree Code</b> is a required data field and cannot be left blank.
      li Leave fields without information blank. Do not put placeholders (e.g. X, N/A).
      li Note that this can be used to both <b>import new measurement values</b> and <b>update existing ones</b>.

br
table.image
  caption[align="bottom"]
    h5
      | Figure 1. Example input for bulk import using measurements_lesion_length_import_template.csv
  tr
    td
      = image_tag("measurements_bulk_import/measurements_lesion_length_bulk_import.png", :alt => "Example input for lesion length measurements import using measurements_lesion_length_import_template.csv")

br
ol[start="3"]
  li Upload the filled-in template (saved as a CSV) to the #{link_to "General Files", general_files_path} page.
  li Select the newly uploaded file using the drop down list below. Selecting a protocol to associate with the measurement is optional. After making the selections, press <b>Import</b>. If there are any errors with the file's data formatting, an error message will appear and the import process will stop.
br

h2 Choose File for Import 

= form_tag bulk_import_measurements_path do
  = collection_select(:file, :id, GeneralFile.all.reverse, :id, :title, {:include_blank => "Select an Uploaded File"}, {class: "select2-basic", style: 'width: 50%;'})
  br
  br

  h2 Provide Additional Information
  = collection_select(:protocol, :id, Protocol.all, :id, :to_label, {:include_blank => "Select a Protocol (optional)"}, {class: "select2-basic", style: 'width: 50%;'})
  
  = hidden_field_tag 'measurement_type_id', MeasurementType.find_by(code: 'LL').id
  = hidden_field_tag 'sample_type', 'whole plant'

  br
  br
  = submit_tag "Import"
br
