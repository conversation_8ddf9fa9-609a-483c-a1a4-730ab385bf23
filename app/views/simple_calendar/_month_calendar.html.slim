.simple-calendar
  .calendar-heading
    = link_to t('simple_calendar.previous', default: '<<'), calendar.url_for_previous_view
    span.calendar-title
      = t('date.month_names')[start_date.month]
      = start_date.year
    = link_to t('simple_calendar.next', default: '>>'), calendar.url_for_next_view
  table.table.table-striped
    thead
      tr
        - date_range.slice(0, 7).each do |day|
          th
            = t('date.abbr_day_names')[day.wday]
    tbody
      - date_range.each_slice(7) do |week|
        tr
          - week.each do |day|
            = content_tag :td, class: calendar.td_classes_for(day) do
              - if defined?(Haml) && respond_to?(:block_is_haml?) && block_is_haml?(block)
                - capture_haml(day, sorted_events.fetch(day, []), &block)
              - else
                - block.call day, sorted_events.fetch(day, [])
