<div id="gantt-chart">

  <div class="float-right">
    <%= form_tag nil, method: :get, class: 'form-inline' do %>
      <div class="form-group mx-sm-1">
        <%= text_field_tag 'months', @gantt.months, class: 'form-control form-control-sm', size: 2 %>
      </div>
      <div class="form-group mx-sm-2">
        <p class="form-control-static">months from</p>
      </div>
      <div class="form-group">
        <%= select_month(@gantt.month_from, { prefix: "month", discard_type: true }, class: 'form-control form-control-sm') %>
      </div>
      <div class="form-group mx-sm-2">
        <%= select_year(@gantt.year_from, { prefix: "year", discard_type: true }, class: 'form-control form-control-sm') %>
      </div>
      <%= hidden_field_tag 'zoom', @gantt.zoom %>

      <%= submit_tag 'Apply', class: 'btn btn-primary btn-sm' %> &nbsp;
      <%= link_to "#{icon('home')} Today".html_safe, @gantt.params.merge(month: nil, year: nil),
          method: :get,
          class: 'btn btn-secondary btn-sm' %>
    <% end %>
  </div>

  <div class="btn-toolbar" role="toolbar">
    <div class='btn-group btn-group-sm mr-3'>
      <%= if @gantt.zoom > 1
        link_to "zoom out #{icon('minus-circle')}".html_safe, @gantt.params.merge(zoom: (@gantt.zoom - 1)),
          class: 'btn btn-secondary'
      end %>
      <%= if @gantt.zoom < 4
        link_to "zoom in #{icon('plus-circle')}".html_safe, @gantt.params.merge(zoom: (@gantt.zoom + 1)),
          class: 'btn btn-secondary'
      end %>
    </div>
    <div class='btn-group btn-group-sm mr-3'>
      <%= link_to "#{icon('step-backward')} Previous".html_safe, @gantt.params_previous, method: :get,
            class: 'btn btn-secondary' %>
      <%= link_to "#{icon('step-forward')} Next".html_safe, @gantt.params_next, method: :get,
            class: 'btn btn-secondary' %>
    </div>
  </div>


  <% zoom = 1
  @gantt.zoom.times { zoom = zoom * 2 }

  subject_width = 330
  header_height = 18

  headers_height = header_height
  show_weeks = false
  show_days = false

  if @gantt.zoom >1
    show_weeks = true
    headers_height = 2*header_height
    if @gantt.zoom > 2
      show_days = true
      headers_height = 3*header_height
    end
  end

  g_width = ((@gantt.date_to - @gantt.date_from + 1)*zoom).to_f.round
  g_height = [(20 * @gantt.events.length + 6)+150, 206].max
  t_height = g_height + headers_height
  %>

  <table id="gantt">
    <tr>
      <!-- Task list -->
      <td style="width:<%= subject_width %>px; padding:2px; ">
        <div style="position:relative;height:<%= t_height + 24 %>px;width:<%= subject_width + 1 %>px;">
          <div style="right:-2px;width:<%= subject_width %>px;height:<%= headers_height %>px;background: #eee;" class="gantt_hdr"></div>
          <div style="right:-2px;width:<%= subject_width %>px;height:<%= t_height %>px;border-left: 1px solid #c0c0c0;overflow:hidden;" class="gantt_hdr"></div>

          <!-- # Tasks subjects  -->
          <% top = headers_height + 8 %>
          <% @gantt.events.each do |task| %>
            <div style="position:absolute; line-height:1.2em; height:16px;top:<%= top %>px;left:4px;overflow:hidden;">
              <small><%= link_to task.subject, task %></small>
            </div>
            <% top = top + 20 %>
          <% end %>
        </div>
      </td>

      <!-- Actual Gantt chart dates, etc. -->
      <td style="padding: 0px">
        <div style="position:relative;height:<%= t_height + 24 %>px;overflow:auto;">
          <div style="width:<%= g_width-1 %>px;height:<%= headers_height %>px;background: #eee;" class="gantt_hdr">&nbsp;</div>
          <%
          #
          # Months headers
          #
          month_f = @gantt.date_from
          left = 0
          height = show_weeks ? header_height : header_height + g_height
          @gantt.months.times do
            width = (((month_f >> 1) - month_f) * zoom).to_f.round - 1
            %>
            <div style="left:<%= left %>px;width:<%= width %>px;height:<%= height %>px;" class="gantt_month gantt_hdr">
              <%= link_to "#{month_f.year}-#{month_f.month}", @gantt.params.merge(year: month_f.year, month: month_f.month), title: "#{month_f.month} #{month_f.year}"%>
            </div>
            <%
            left = left + width + 1
            month_f = month_f >> 1
          end %>

          <%
          #
          # Weeks headers
          #
          if show_weeks
            left = 0
            height = (show_days ? header_height-1 : header_height-1 + g_height)
            if @gantt.date_from.cwday == 1
              # @date_from is monday
              week_f = @gantt.date_from
            else
              # find next monday after @date_from
              week_f = @gantt.date_from + (7 - @gantt.date_from.cwday + 1)
              width = ((7 - @gantt.date_from.cwday + 1) * zoom).to_f.round - 1
              %>
              <div style="left:<%= left %>px;top:19px;width:<%= width %>px;height:<%= height %>px;" class="gantt_hdr gantt_week">&nbsp;</div>
              <%
              left = left + width+1
            end %>
            <%
            while week_f <= @gantt.date_to
              width = (week_f + 6 <= @gantt.date_to) ? 7 * zoom - 1 : (@gantt.date_to - week_f + 1) * zoom-1
              width = width.to_f.round
              %>
              <div style="left:<%= left %>px;top:19px;width:<%= width %>px;height:<%= height %>px;" class="gantt_hdr gantt_week">
                <small><%= week_f.cweek if width >= 16 %></small>
              </div>
              <%
              left = left + width+1
              week_f = week_f+7
            end
          end %>

          <%
          #
          # Days headers
          #
          if show_days
            left = 0
            height = g_height + header_height - 1
            wday = @gantt.date_from.cwday
            (@gantt.date_to - @gantt.date_from + 1).to_i.times do
              width =  zoom - 1
              %>
              <div style="left:<%= left %>px;top:37px;width:<%= width %>px;height:<%= height %>px;font-size:0.4em;<%= "background:#f1f1f1;" if wday > 5 %>" class="gantt_hdr gantt_day">
                <%= Date::DAYNAMES[wday % 7].first %>
              </div>
              <%
              left = left + width+1
              wday = wday + 1
              wday = 1 if wday > 7
            end
          end %>

          <%
          #
          # Tasks
          #
          top = headers_height + 10
          @gantt.events.each do |i|
            i_start_date = (i.start_date >= @gantt.date_from ? i.start_date : @gantt.date_from )
            i_end_date = (i.due_before <= @gantt.date_to ? i.due_before : @gantt.date_to )

            i_done_date = i.start_date + ((i.due_before - i.start_date+1)*i.done_ratio/100).floor
            i_done_date = (i_done_date <= @gantt.date_from ? @gantt.date_from : i_done_date )
            i_done_date = (i_done_date >= @gantt.date_to ? @gantt.date_to : i_done_date )

            i_late_date = [i_end_date, Date.today].min if i_start_date < Date.today

            i_left = ((i_start_date - @gantt.date_from)*zoom).floor
            i_width = ((i_end_date - i_start_date + 1)*zoom).floor - 2                  # total width of the issue (- 2 for left and right borders)
            d_width = ((i_done_date - i_start_date)*zoom).floor - 2                     # done width
            l_width = i_late_date ? ((i_late_date - i_start_date+1)*zoom).floor - 2 : 0 # delay width
            %>

            <div style="top:<%= top %>px;left:<%= i_left %>px;width:<%= i_width %>px;" class="task task_todo">&nbsp;</div>
            <% if l_width > 0 %>
            <div style="top:<%= top %>px;left:<%= i_left %>px;width:<%= l_width %>px;" class="task task_late">&nbsp;</div>
            <% end %>
            <% if d_width > 0 %>
            <div style="top:<%= top %>px;left:<%= i_left %>px;width:<%= d_width %>px;" class="task task_done">&nbsp;</div>
            <% end %>
            <div style="top:<%= top %>px;left:<%= i_left + i_width + 5 %>px;background:#fff;" class="task">
              <%= i.status.name if i.status %>
              <%= (i.done_ratio).to_i %>%
            </div>
            <div class="task-tooltip" style="position: absolute;top:<%= top %>px;left:<%= i_left %>px;width:<%= i_width %>px;height:12px;">
              <span class="tip">
                <%== render_task_tooltip i %>
              </span>
            </div>
            <% top = top + 20
          end %>

          <%
          #
          # Today red line (excluded from cache)
          #
          if Date.today >= @gantt.date_from && Date.today <= @gantt.date_to %>
          <div style="position: absolute;height:<%= g_height %>px;top:<%= headers_height + 1 %>px;left:<%= ((Date.today-@gantt.date_from+1)*zoom).floor()-1 %>px;width:10px;border-left: 1px dashed red;">&nbsp;</div>
          <% end %>

        </div>
      </td>
    </tr>
  </table>
</div>

<%= render 'actions' %>
