= knoxy_form_for(@task) do |f|
  = render_form_errors(@task)

  fieldset
    legend Task Details
    = f.input :subject, required: true
    = f.input :description, as: :text
    .row
      .col
        = f.association :category, include_blank: true,
            input_html: { class: 'select2-basic', style: 'width: 100%' }
      .col
        = f.association :priority, include_blank: true,
            input_html: { class: 'select2-basic', style: 'width: 100%' }
      .col
        = f.association :assigned_to, include_blank: true,
            input_html: { class: 'select2-basic', style: 'width: 100%'}
  fieldset
    legend Progress
    .row
      .col
        = f.association :status, include_blank: true,
            input_html: { class: 'select2-basic', style: 'width: 100%' }
      .col = f.input :start_date, as: :string, placeholder: 'YYYY-MM-DD'
      .col = f.input :due_date, as: :string, placeholder: 'YYYY-MM-DD'
      .col = f.input :estimated_hours
      .col = f.input :done_ratio, label: "% complete", size: 5
  .form-actions
    = f.submit

