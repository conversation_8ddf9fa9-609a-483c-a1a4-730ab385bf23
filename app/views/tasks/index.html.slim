- if @user
  h1 Listing tasks for user "#{@user.name}"
- else
  h1 Listing tasks for all users

table.list
  thead
    tr
      th Subject
      th Set completed
      th Status
      th Priority
      th Assigned to
      th Category
      th Start date
      th Due date
      th Estimated hours
      th % done
      th colspan="3"  Actions
  tbody
    - @tasks.each do |task|
      tr
        td
          span title="#{task.description}" = task.subject
        td= link_to('Set completed', complete_task_path(task), confirm: 'Set task to complete and close?', method: :put) unless task.done_ratio == 100
        td= task.status.name if task.status
        td= task.priority.name if task.priority
        td= task.assigned_to.name if task.assigned_to
        td= task.category.name if task.category
        td= task.start_date
        td= task.due_date
        td= task.estimated_hours
        td= task.done_ratio
        td= link_to_show task
        td= link_to_edit edit_task_path(task)
        td= link_to_destroy task

= render 'actions'
