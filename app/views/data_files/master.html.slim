h1 
  = 'Data Files'

= search_form_for @search, url: data_files_all_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th Filename
        th Description
        th Size
        th Type
        th Uploaded On
        th colspan="2"
          = 'Actions'
      tr.table-search
        th= f.text_field :data_file_name_cont, size: 8
        th= f.text_field :description_cont
        th
        th= f.select :data_file_type_id_eq, DataFileType.order(:name).map { |s| [s.name, s.id] }, include_blank: true
        th= f.text_field :created_at_cont
        th colspan="2"
          = table_search_actions
    tbody
      - @data_files.each do |data_file|
        tr
          td= link_to h(data_file.data_file_name), data_file.data.url
          td= data_file.description
          td= number_to_human_size(data_file.data_file_size)
          td= link_to h(data_file.data_file_type.name), data_file_type_path(data_file.data_file_type)
          td= data_file.created_at.to_date
          td= link_to_show measurement_data_file_path(data_file.measurement, data_file)
          td= link_to_destroy measurement_data_file_path(data_file.measurement, data_file)

= will_paginate@data_files
