<% if @measurement %>
  <h1>Data Files for Measurement <%= @measurement.id %></h1>
<% else %>
  <h1>Data Files</h1>
<% end %>

<table class="list">
	<tr>
		<th>Filename</th>
		<th>Size</th>
		<th>Type</th>
		<th colspan="3">Actions</th>
	</tr>

	<% @data_files.each do |data_file| %>
	<tr class="<%= cycle('odd', 'even') %>">
		<td><%= link_to h(data_file.data_file_name), data_file.data.url %></td>
		<td><%= number_to_human_size(data_file.data_file_size) %></td>
		<td><%= link_to h(data_file.data_file_type.name), data_file_type_path(data_file.data_file_type) %></td>
		<td><%= link_to_show measurement_data_file_path(data_file.measurement, data_file) %></td>
		<td><%= link_to_destroy measurement_data_file_path(data_file.measurement, data_file) %></td>
	</tr>
	<% end %>
</table>

<br />

<% if @measurement %>
  <%= link_to_new 'add data file for this measurement', new_measurement_data_file_path(@measurement) %>
<% end %>
