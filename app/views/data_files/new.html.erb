<h1>Data File Upload</h1>

<%= render_form_errors(@data_file) %>
<%= knoxy_form_for([@measurement, @data_file], html: { multipart: true }) do |f| %>
  <fieldset>
    <legend>Data File Details</legend>
    <%= f.input :data, label: 'File to Upload', required: true %>
    <%= f.input :data_file_type_id, collection: DataFileType.all, include_blank: true, required: true %>
    <!- f.input :has_concentrations, label: 'This file contains concentration data in CSV format that should be imported' -> <!- link_to 'File Format Information', '/file_format.html', target: '_blank' ->
    <!- f.input :has_concentration_units, collection: ["uM", "mmol / mol creatinine"], label: 'Units' ->

    <%= f.input :description %>
  </fieldset>

  <%= f.submit "Upload" %>
<% end %>
