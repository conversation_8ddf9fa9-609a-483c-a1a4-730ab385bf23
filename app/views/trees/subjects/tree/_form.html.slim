= render_form_errors(@tree)
= knoxy_form_for @tree do |f|
  /fieldset
    legend Identification
    .row: .col-6
      = f.association :species
      = f.association :source, collection: Source.order(:code)
      = f.input :trial, collection: ['G128A', 'G128B', 'G128C', 'G128D', 'G132A', 'G132C', 'G132D']
      = f.input :family
      = f.input :code
      = f.input :forward_selected, label: 'Forward Selected', as: :radio_buttons, :collection => [ ['Yes', 'Y'], ['No', 'N'] ]
      
  fieldset
    legend Details
    /.row: .col-3
      = f.input :breeding_value_rank, label: 'Breeding Value Rank', :collection => [ ['1 - High BV, Tall Trees', 1], ['2 - Medium BV, Medium Trees', 2], ['3 - Low BV, Short Trees', 3], ['4 - FS, not in a selected family', 4] ]
      = f.input :label_present, label: 'RES-FOR Label on Tree', :collection => [ ['Yes', 'Y'], ['No', 'N'] ]
    .row: .col-6
      = f.input :tag_list, collection: all_tags('Tree'), label: "Tags",
          input_html: { class: 'taggable', multiple: true }
      = f.input :notes

  /fieldset
    legend Location
    .row: .col-3
      = f.input :rep
      = f.input :row
      = f.input :tier
      = f.input :set
      = f.input :stake
      = f.input :tree

  .form-actions
    = f.submit 'Update'
