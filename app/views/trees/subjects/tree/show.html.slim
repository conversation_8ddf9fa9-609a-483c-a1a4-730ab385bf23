h1 Showing Pasture #{@tree}
.card-deck
  .card
    .card-header Identification
    .card-block
      dl
        dt CAT-G Label:
        dd = nah @tree.res_for_label
        dt Total Area:
        dd = '60 ha'
        /nah @tree.species.name
        dt Realized Area:
        dd = '60 ha'
        /nah @tree.source.name
        / - if @tree.source.code != "GH"
        /   dt Trial:
        /   dd = nah @tree.trial
        / - else
        /   dt Treatment:
        /   dd = nah @tree.treatment
        dt Grass Area:
        dd = '60 ha'
        /nah @tree.family
        dt Forest Area:
        dd = '60 ha'
        /nah @tree.code
        dt Alkali Area:
        dd = '60 ha'
        /nah @tree.code
        dt Wetland Area:
        dd = '60 ha'
        /nah @tree.code        
        dt Shrubland Area:
        dd = '60 ha'
        /nah @tree.code
        dt Infrastructure Area:
        dd = '60 ha'
        /nah @tree.code
        / - if @tree.source.code != "GH"
        /   dt Forward Selected:
        /   dd = nah boolean_word_to_english(@tree.forward_selected)
        / - else
        /   dt Block:
        /   dd = @tree.block
        
  .card
    .card-header Cattle Information
    .card-block
      dl
        - if @tree.source.code != "GH"
          dt Breeding Value Rank:
          dd = nah @tree.breeding_value_rank
          dt RES-FOR Label on Tree:
          dd = nah boolean_word_to_english(@tree.forward_selected)
        dt SUC Code:
        dd = nah @tree.suc_code
        dt Mother Tree:
        dd
          - if @tree.suc_code.present? && @tree.mother_tree != 0
            = link_to @tree.mother_tree, parent_tree_path(@tree.mother_parent_tree)
          - else
            = nah @tree.mother_tree
        dt Father Tree:
        dd = nah @tree.father_tree
        dt Tag List:
        dd = nah @tree.tag_list
        dt Notes:
        dd = nah @tree.notes
  .card
    .card-header Location
    .card-block
      dl
        - if @tree.source.code != "GH"
          dt Rep:
          dd = nah @tree.rep
          dt Row:
          dd = nah @tree.row
          dt Tier:
          dd = nah @tree.tier
          dt Set:
          dd = nah @tree.set
          dt Stake:
          dd = nah @tree.stake
          dt Tree:
          dd = nah @tree.tree
          dt x:
          dd = nah @tree.x_coordinate
          dt y:
          dd = nah @tree.y_coordinate

br
dt Last Updated: #{@tree.updated_at.in_time_zone('Mountain Time (US & Canada)').strftime("%Y-%m-%d %H:%M")} MST by #{@tree.last_user_updated.name}
br
= link_to_edit edit_tree_path(@tree)

