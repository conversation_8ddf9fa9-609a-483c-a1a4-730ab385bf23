h1
  = 'Kinsella Pastures'
  /= link_to_new_right_aligned 'add tree', new_tree_path
= search_form_for @search, url: trees_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th CAT-G Label
        th Subpasture
        th Plot
        th Year
        th Treatment
        th Total Area
        / th Family
        / th Unique Tree Code
        / th Forward Selected
        / th Breeding Value Rank
        / th Tags
        th colspan="2" Actions
      tr.table-search
        th colspan="1"
        th= f.select :species_short_code_eq, Species.order(:code).map { |s| [s.code] }, include_blank: true
        th= f.select :source_short_code_eq, Source.order(:code).map { |s| [s.code] }, include_blank: true
        th= f.select :treatment_eq, ['CTRL1', 'CTRL2', 'NOID', 'WIND', 'INDD'], include_blank: true
        th= f.select :trial_eq, ['G128A', 'G128B', 'G128C', 'G128D', 'G132A', 'G132C', 'G132D'], include_blank: true
        th= f.text_field :family_cont, size: 8
        / th= f.text_field :code_eq, size: 8
        / th= f.select :forward_selected_eq, [ ['Yes', 'Y'], ['No', 'N'] ], include_blank: true
        / th= f.select :breeding_value_rank_eq, [1, 2, 3, 4], include_blank: true
        / th= f.select :tags_name_cont, all_tags('Tree'), include_blank: true
        th colspan="2"
          = table_search_actions
    tbody
      - @trees.each do |tree|
        tr
          - if tree.res_for_tree
            <td style="min-width: 200px">#{link_to(tree.res_for_label, tree)}</td>
          - else
            td= nah
          td= tree.species.code
          td= tree.source.code
          td= tree.treatment
          td= tree.trial
          td= tree.family
          / td= tree.code
          / td= boolean_word_to_english(tree.forward_selected)
          / td= tree.breeding_value_rank
          / td= tag_list(tree.tags)
          td= link_to_show tree
          td= link_to_edit edit_tree_path(tree)
          /td= link_to_destroy tree
= will_paginate@trees

