h1 PeaCE Data Header Reference
= render 'jumper'
/ br
/ = render 'greenhouse_info'
br

= render 'simple/header_definitions/general_info'
= render 'simple/header_definitions/veg_omics'
= render 'simple/header_definitions/ghg'
= render 'simple/header_definitions/livinglabs'
= render 'simple/header_definitions/production'
/ = render 'simple/header_definitions/parent_trees'
/ = render 'simple/header_definitions/characteristics'
/ = render 'simple/header_definitions/sample_codes'

/ - MeasurementType.order(:name).each do |measurement_type|
/   = render "simple/header_definitions/#{measurement_type.name.parameterize.underscore}"
