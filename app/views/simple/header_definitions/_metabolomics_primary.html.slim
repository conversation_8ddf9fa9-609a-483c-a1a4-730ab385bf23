#metabolomics_primary
  br
  br
  br

  h3.text-primary
    = 'Metabolomics (Primary Metabolites) Measurements'

  table.table-sm.table-striped.table-bordered
    thead
      tr
        th Header Name
        th Description
        th Field
        th Greenhouse
    tbody
      tr
        td Phase
        td
          = "Phase during the greenhouse experiment in which the measurement was taken"
          br
          br
          b
            = "Note: "
          = "Greenhouse measurements were taken at three time points: PRE (Day-0), DD2, and HAR"
        td
        td ✓

      - Measurement.measurement_value_types_array('MP').each do |measurement_value|
        - if measurement_value.include? "FIELD" # Field has all of the different columns (only want to list once)
          - if field_only_measurement_values.include? measurement_value
            tr
              td #{MeasurementValue.header(measurement_value).gsub(' (Field)', '')}
              td #{MeasurementValue.title(measurement_value).capitalize.gsub('Dry Weight', 'dry weight')}
              td ✓
              td
          - else
            tr
              td #{MeasurementValue.header(measurement_value).gsub(' (Field)', '')}
              td #{MeasurementValue.title(measurement_value).capitalize.gsub('Dry Weight', 'dry weight').gsub('n-oxide', 'N-oxide').gsub('Trans-h', 'trans-H').gsub('Alpha-a', 'alpha-A').gsub('Xleu', 'xLeu').gsub('Lysoc', 'LYSOC').gsub('Pc aa c', 'PC aa C').gsub('Pc ae c', 'PC ae C').gsub('Beta-h', 'beta-H').gsub('Alpha-k', 'alpha-K')}
              td ✓
              td ✓
  br
  b
    = "Note: "            
  = "LOD: Limit of Detection; LOQ = Limit of Quantitation; ND = Not Detected"
  br
  br
  b
    = "Lipid Nomenclature: "
  ul
    li 
      = "Headgroups: LYSO = lysophosphatidylcholine; PC = phosphatidylcholine"
    li 
      = "Fatty acid chains: Cx:y"
      ul
        li 
          = "x = the total number of carbons in the chain" 
        li 
          = "y = the number of double bonds in the chain"
    li 
      = "Glycerol backbone bonds:"
      ul
        li
          = "aa = sn-1 and sn-2 position fatty acids are both bound via ester bonds (diacyl)"
        li
          = "ae = sn-1 position fatty acid bound via ether bond and sn-2 position fatty acid bound via ester bond (acyl-alkyl)"
