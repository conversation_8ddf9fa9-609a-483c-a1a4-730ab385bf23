#metabolomics
  br
  br
  br

  h3.text-primary
    = 'Metabolomics (Polyphenols and Metals/Chemical Elements) Measurements'

  table.table-sm.table-striped.table-bordered
    thead
      tr
        th Header Name
        th Description
        th Field
        th Greenhouse
    tbody
      tr
        td Phase
        td
          = "Phase during the greenhouse experiment in which the measurement was taken"
          br
          br
          b
            = "Note: "
          = "Greenhouse measurements were taken at three time points: PRE (Day-0), DD2, and HAR"
        td
        td ✓

      - Measurement.measurement_value_types_array('MB').each do |measurement_value|
        - if measurement_value.include? "FIELD" # Field has all of the different columns (only want to list once)
          - if field_only_measurement_values.include? measurement_value
            tr
              td #{MeasurementValue.header(measurement_value).gsub(' (Field)', '')}
              td #{MeasurementValue.title(measurement_value).capitalize.gsub('Dry Weight', 'dry weight')}
              td ✓
              td
          - else
            tr
              td #{MeasurementValue.header(measurement_value).gsub(' (Field)', '')}
              td #{MeasurementValue.title(measurement_value).capitalize.gsub('Dry Weight', 'dry weight').gsub('P-c', 'p-C').gsub('3,4-d', '3,4-D')}
              td ✓
              td ✓
  br
  b
    = "Note: "            
  = "LOD: Limit of Detection; LOQ = Limit of Quantitation; ND = Not Detected"
