#sample_codes
  br
  br
  br

  h3.text-primary
    = 'Sample Codes'

  table.table-sm.table-striped.table-bordered
    thead
      tr
        th Sample Code
        th Description
    tbody
      tr
        td CD
        td 
          = 'Chemical Defenses'
          br
          = '(Parent sample for MT and PP samples)'

      - MeasurementType.order(:code).each do |measurement_type|
        tr
          td = measurement_type.code
          td = measurement_type.name
