.container
  .container
    .jumbotron[style="background-image: url('images/peas.png'); background-size: cover; background-repeat: no-repeat; background-position: center;"]
      h1.display-3[style="color: black;"]
        | <strong>Welcome to the PeaCE LIMS!</strong>
      p.lead[style="color: black;"]
        | The <strong>PeaCE-LIMS</strong> is the backend system for Peas Climate Efficient (PeaCE), a nationally funded study.
      a.btn.btn-primary.btn-lg[href="trees"]
        | Get started!
  .container
    = month_calendar events: CalendarEvent.all do |date, events|
      = date.day
      - events.each do |event|
        div.text-danger
          = event.name
          - if event != events.last # line breaks to separate more than one event on the same day
            br
            br
  br
  .card-deck
    .col-md-4.d-flex.align-items-stretch
    /   .card.text-center
    /     img.img-fluid.card-img-top[src="images/Davidson_Blue.png" alt=""]
    /     p.card-body
    /       h2.card-title
    /         | Davidson/Blue
    /         h3 GE<sup>3</sup>LS Impacts Group Portal
    /     .card-footer[style="position:absolute; bottom:0; width:100%;"]
    /       a.btn.btn-primary[href="davidson_blue_ge3ls_files"]
    /         | Enter portal
    .col-md-4.d-flex.align-items-stretch
      .card
        h2.card-header.text-center Announcements
        .card-body
          p.card-text
            ul[style="list-style-type:disc"]
              - Announcement.put_on_display.order('created_at DESC').each do |announcement|
                  li = announcement.message
    .col-md-4.d-flex.align-items-stretch
    /   .card.text-center
    /     img.img-fluid.card-img-top[src="images/An_Cool_Gaston.png" alt=""]
    /       p.card-body
    /         h2.card-title
    /           | An/Cool/Gaston
    /           h3 GE<sup>3</sup>LS Economics Group Portal
    /     .card-footer
    /       a.btn.btn-primary[href="an_cool_gaston_ge3ls_files"]
    /         | Enter portal
