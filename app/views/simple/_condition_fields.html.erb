<div class="field">
  <%= f.attribute_fields do |a| %>
    <%= a.attribute_select associations: [:source, :species, :mother_parent_tree, :characteristics, :measurement_types, :measurement_values] %>
  <% end %>
  <%= f.predicate_select only: [:eq, :not_eq, :cont, :not_cont, :gt, :lt, :null, :not_null], compounds: false %>
  <%= f.value_fields do |v| %>
    <%= v.text_field :value, :value => 1 %>    
  <% end %>
  <%= link_to "remove", '#', class: "remove_fields" %>
</div>
