h1
  = 'Results'
  = link_to_csv_download 'export as csv', advanced_search_result_path(request.query_parameters.merge({format: :csv}))

- if !@columns
  - @columns = default_columns

/ counts of values for each attribute for each age
- characteristics_presence_hash = get_characteristics_presence_hash(@columns, @ages)

/ list of models requested in display headers (for pre-loading)
- models = get_models_array(@columns)

style[type="text/css"]
  |  table th, table td { min-width: 100px; text-align: center;}

.table-responsive
  table.list
    thead
      tr.header
        - @columns.each do |column|
          - if get_model_name(column) == 'Characteristic'
            - @ages.each do |age|
              / only output columns with values
              - if characteristics_presence_hash["#{get_attribute_name(column)}_#{age}"]
                th #{get_display_name(column)} at Age #{age}
          - else
            th #{get_display_name(column)} 

    tbody
      - @trees.select('trees.*').each do |tree|
        / Preload (speed up query)
        - if models.include? "Mother"
          - mother_tree = tree.mother_parent_tree

        tr
          - @columns.each do |column|
            - if get_model_name(column) == 'Characteristic'
              - @ages.each do |age|
                / only output columns with values
                - if characteristics_presence_hash["#{get_attribute_name(column)}_#{age}"]
                  - characteristic = Characteristic.find_by(tree_id: "#{tree.id}", age_recorded: "#{age}")
                  - if characteristic.blank?
                    <td></td>  
                  - else
                    - if get_attribute_name(column) == 'condition_code'
                      <td>#{format_code(characteristic.send('condition_code'))}</td>
                    - else
                      <td>#{characteristic.send(get_attribute_name(column))}
            - elsif get_model_name(column).include? 'Measurement'
              - measurement = Measurement.joins(:sample).where("samples.tree_id = #{tree.id} AND measurement_type_id = #{MeasurementType.find_by(code: get_measurement_type(column)).id}")
              - if measurement.blank?
                <td></td>
              - elsif get_attribute_name(column) == 'performed_on'
                <td>#{measurement.first.performed_on_range}</td>
              - else
                <td>#{measurement.first.send(get_attribute_name(column))}</td>
            - elsif get_model_name(column) == 'Source'
              - if get_attribute_name(column) == 'name'
                <td style="min-width: 150px">#{tree.source.name}</td>
              - else
                <td>#{tree.source.attributes[get_attribute_name(column)]}</td>
            - elsif get_model_name(column) == 'Species'
              - if get_attribute_name(column) == 'name'
                <td style="min-width: 150px">#{tree.species.name}</td>
              - else
                <td>#{tree.species.attributes[get_attribute_name(column)]}</td>

            / Pedigree
            - elsif get_display_name(column).include? 'Tree SUC Code'
              <td>#{tree.send(get_attribute_name(column))}</td>
            - elsif get_model_name(column) == 'Mother'
              - if mother_tree
                <td>#{mother_tree.send(get_attribute_name(column))}
              - else
                <td></td>

            - elsif get_attribute_name(column) == 'res_for_label'
              <td style="min-width: 200px">#{tree.res_for_label}</td>
            - else
              <td>#{tree.attributes[get_attribute_name(column)]}</td>

br              
= will_paginate @trees
