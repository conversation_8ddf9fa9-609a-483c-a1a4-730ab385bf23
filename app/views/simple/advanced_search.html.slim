h1
  | Advanced Search (Export)

p.alert.alert-info
  | #{hint} To export all data press 'Search' without adding search conditions, specifying display fields, or adding filters.
br

h3
  | Search Conditions:

p.alert.alert-info
  | #{hint} Queries will match ALL selected conditions. </br>
  | #{hint} Leave value as '1' for 'is null' and 'is not null' queries

= search_form_for @search, url: advanced_search_result_path do |f|
  = f.condition_fields do |c|
    = render 'condition_fields', f: c
  p
    = link_to_add_fields "Add Search Condition", f, :condition
  br


  br
  h3
    | Apply Filters:

  p.alert.alert-info
    | #{hint} Tree Groups allow for the export of data for select trees. Create new groups using the #{link_to "Tree Groups", tree_groupings_path} menu.

  = collection_select(:grouping, :id, Grouping.where(type: "TreeGrouping"), :id, :name, {:include_blank => 'Select a Tree Group'}, {class: "dropdown_search"})
  br


  br
  br
  h3
    | Display Fields:

  p.alert.alert-info
    | #{hint} Choose which fields to display the query results.

  .table-responsive.table-display-fields
    table
      thead
        tr
          - options_for_select_collection.each do |key, values|
              th (style="min-width:350px")
                = key
      tbody
        tr
          - options_for_select_collection.each do |key, values|
            td (style="vertical-align:top;")
              = check_box_tag "selectAll_#{get_model_name(values[0])}"
              = "All"
              - values.each do |value|
                ul style=("list-style: none; padding-left: 0px; margin-bottom: 0px;")
                  li
                    = check_box_tag 'columns[]', value, false, class: "selectable_#{get_model_name(value)}"
                    = get_display_name(value)
  br

  br
  .actions
    = f.submit "Search"
