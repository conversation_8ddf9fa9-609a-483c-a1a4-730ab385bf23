br
h3 Quick Jump

- jumps = {'General Info' => 'general', 'Veg and Omics' => 'veg-omics', 'GHG and Soil' => 'ghg-soil', 'LivingLabs' => 'livinglabs', 'Production' => 'production'}

/- MeasurementType.order(:name).each do |measurement_type|
  - jumps[measurement_type.name] = measurement_type.name.parameterize.underscore

#jumper
  - jumps.each do |link, partial|
    = link_to link, "##{partial}", class: 'btn btn-sm'
    br
br
