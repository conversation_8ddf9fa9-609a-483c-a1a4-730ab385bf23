h1
  = 'LivingLabs Ranches'

= search_form_for @search, url: parent_trees_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th = 'Ranch Name'
        / th
        /   = sort_link(@search, :suc_code, 'SUC Code')
        / th
        /   = sort_link(@search, :accession_number, 'Pedigree Corrected Family')
        / th Species (P/S)
        / th Cone Collection Date
        / th Location
        / th
        /   = sort_link(@search, :latitude, 'Latitude (°)')
        / th
        /   = sort_link(@search, :longitude, 'Longitude (°)')
        / th
        /   = sort_link(@search, :elevation, 'Elevation (m)')
        th colspan="1" Actions
      tr.table-search
        th= f.text_field :suc_code_cont
        / th= f.text_field :accession_number_cont
        / th= f.select :species_short_code_eq, Species.order(:code).map { |s| [s.code] }, include_blank: true
        / th= f.text_field :cone_collected_cont
        / th= f.select :location_eq, ParentTree.uniq.pluck(:location).compact.sort, include_blank: true
        / th= f.text_field :latitude_cont
        / th= f.text_field :longitude_cont
        / th= f.text_field :elevation_cont
        th colspan="1"
          = table_search_actions
    tbody
      - @parent_trees.each do |parent_tree|
        tr
          / td= parent_tree.suc_code
          / td= parent_tree.accession_number
          / td= parent_tree.species.code
          / td= parent_tree.cone_collected
          td= parent_tree.location
          / td= parent_tree.latitude
          / td= parent_tree.longitude
          / td= parent_tree.elevation
          td= link_to_show parent_tree

= will_paginate@parent_trees

