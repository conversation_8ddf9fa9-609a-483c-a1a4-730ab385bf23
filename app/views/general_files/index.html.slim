h1 
  = 'Uploaded Files'
  '
  = link_to "List All", all_general_files_path, class: 'btn btn-sm btn-outline-primary'
  = link_to_new_right_aligned 'upload new file', new_general_file_path

= search_form_for @search, url: general_files_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th
          = sort_link(@search, :title, 'Title')
        th
          = sort_link(@search, :description, 'Description')
        th Uploaded By
        th
          = sort_link(@search, :created_at, 'Uploaded On')
        th Tags
        th
          = sort_link(@search, :content_file_size, 'Size')
        th File
        th colspan="1" Actions
      tr.table-search
        th= f.text_field :title_cont, size: 8
        th= f.text_field :description_cont
        th= f.select :user_id_eq, User.order(:name).map { |s| [s.name, s.id] }, include_blank: true
        th= f.text_field :created_at_cont
        th= f.select :tags_name_cont, all_tags('GeneralFile'), include_blank: true
        th
        th
        th colspan="1"
          = table_search_actions
    tbody
      - @general_files.each do |general_file|
        tr
          td= general_file.title
          td= general_file.description
          td= nah general_file.user&.name
          td= general_file.created_at.to_date
          td= tag_list(general_file.tags)
          td= number_to_human_size(general_file.content.size)
          td= link_to truncate(general_file.content_file_name), general_file.content.url
          td= link_to_edit edit_general_file_path(general_file)
          /td= link_to_destroy [general_file]

= will_paginate@general_files
