= knoxy_form_for @general_file do |f|
  = render_form_errors(@general_file)

  fieldset
    = f.input :title
    = f.input :description
    = f.input :tag_list, collection: all_tags('GeneralFile'), label: "Tags",
          input_html: { class: 'taggable', multiple: true } 
    

    - if @general_file.content.exists? 
      | Attached File: <b>#{@general_file.content_file_name}</b>
      br
      br
      - if @current_user.id == @general_file.user_id || @current_user.administrator?
        = "Attach a New File"
        = f.input :content, label: false
    - else
      = f.input :content, as: :file

      

  .form-actions
    = f.submit
