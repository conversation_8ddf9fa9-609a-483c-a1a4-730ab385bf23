= knoxy_form_for @an_cool_gaston_ge3ls_file do |f|
  = render_form_errors(@an_cool_gaston_ge3ls_file)

  fieldset
    = f.input :title
    = f.input :description
    = f.input :tag_list, collection: all_tags('AnCoolGastonGe3lsFile'), label: "Tags",
          input_html: { class: 'taggable', multiple: true } 
   
    - if @an_cool_gaston_ge3ls_file.content.exists? 
      | Attached File: <b>#{@an_cool_gaston_ge3ls_file.content_file_name}</b>
      br
      br
      = "Attach a New File"
      = f.input :content, label: false
    - else
      = f.input :content, as: :file

  .form-actions
    = f.submit
