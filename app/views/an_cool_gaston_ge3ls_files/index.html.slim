h1 
  | An/Cool/Gaston GE<sup>3</sup>LS Economics Group Files
  = link_to_new_right_aligned 'upload new file', new_an_cool_gaston_ge3ls_file_path

= search_form_for @search, url: an_cool_gaston_ge3ls_files_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th
          = sort_link(@search, :title, 'Title')
        th
          = sort_link(@search, :description, 'Description')
        th Uploaded By
        th
          = sort_link(@search, :created_at, 'Uploaded On')
        th Tags
        th
          = sort_link(@search, :content_file_size, 'Size')
        th File
        th colspan="2" Actions
      tr.table-search
        th= f.text_field :title_cont, size: 8
        th= f.text_field :description_cont
        th= f.select :user_id_eq, User.order(:name).map { |s| [s.name, s.id] }, include_blank: true
        th= f.text_field :created_at_cont
        th= f.select :tags_name_cont, all_tags('AnCoolGastonGe3lsFile'), include_blank: true
        th
        th
        th colspan="2"
          = table_search_actions
    tbody
      - @an_cool_gaston_ge3ls_files.each do |an_cool_gaston_ge3ls_file|
        tr
          td= an_cool_gaston_ge3ls_file.title
          td= an_cool_gaston_ge3ls_file.description
          td= nah an_cool_gaston_ge3ls_file.user&.name
          td= an_cool_gaston_ge3ls_file.created_at.to_date
          td= tag_list(an_cool_gaston_ge3ls_file.tags)
          td= number_to_human_size(an_cool_gaston_ge3ls_file.content.size)
          td= link_to an_cool_gaston_ge3ls_file.content_file_name, an_cool_gaston_ge3ls_file.content.url
          td= link_to_edit edit_an_cool_gaston_ge3ls_file_path(an_cool_gaston_ge3ls_file)
          td= link_to_destroy [an_cool_gaston_ge3ls_file]

= will_paginate@an_cool_gaston_ge3ls_files
