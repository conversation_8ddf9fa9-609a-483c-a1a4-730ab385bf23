h1 Showing #{Characteristic.title.pluralize} Recorded at Age #{@characteristic.age_recorded} for Tree #{@tree.code}
.card-deck
  .card
    .card-header Metrics
    .card-block
      dl
        dt Height (cm):
        dd = nah @characteristic.height
        dt DBH (cm):
        dd = nah @characteristic.dbh
        dt DBH Position:
        dd = nah @characteristic.dbh_position
        dt Height Breeding Value (%height):
        dd = nah @characteristic.height_breeding_value
        dt DBH Breeding Value (%DBH):
        dd = nah @characteristic.dbh_breeding_value
        - if (@tree.source.code == 'REDE') || (@tree.source.code == 'CALL')
          dt Height Breeding Value GxE (%height):
          dd = nah @characteristic.height_breeding_value_gxe
          dt DBH Breeding Value GxE (%DBH):
          dd = nah @characteristic.dbh_breeding_value_gxe
        
  .card
    .card-header Condition
    .card-block
      dl
        dt #{@tree.species.name} Status Code:
        dd = nah @characteristic.status_code
        dt Condition Code(s):
        dd = nah @characteristic.format_condition_code
        - if @tree.species_id == 1 # lodgepole pine
          dt Western Gall Rust (Code 0-6):
          dd = nah @characteristic.western_gall_rust_six_code
          dt Western Gall Rust (Code 0-2):
          dd = nah @characteristic.western_gall_rust_two_code
          dt Mountain Pine Beetle Presence:
          dd = nah boolean_word_to_english(@characteristic.mountain_pine_beetle_presence)
        - elsif @tree.species_id == 2 # white spruce
          - if @characteristic.age_recorded == 24
            dt White Pine Weevil Sum (Ages 21 and 24):
            dd = nah @characteristic.white_pine_weevil_score_sum
          dt White Pine Weevil Presence (1) or Absence (0):
          dd = nah @characteristic.white_pine_weevil_presence
        dt Notes:
        dd = nah @characteristic.notes
br
dt Last Updated: #{@characteristic.updated_at.in_time_zone('Mountain Time (US & Canada)').strftime("%Y-%m-%d %H:%M")} MST by #{@characteristic.last_user_updated.name}
br

/= link_to_edit edit_tree_characteristic_path(@tree, @characteristic)

