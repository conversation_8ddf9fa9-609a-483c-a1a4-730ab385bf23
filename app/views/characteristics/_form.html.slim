= knoxy_form_for([@tree, @characteristic]) do |f|
  = render_form_errors(@characteristic)

  fieldset
    legend Metrics
    .row
      .col-4 = f.input :age_recorded, label: 'Age Recorded (yr)'
    .row
      .col-4 = f.input :height, label: 'Height (cm)'
      .col-4 = f.input :dbh, label: 'DBH (cm)', hint: 'Diameter at Breast Height (1.3 m)'
      .col-4 = f.input :dbh_position, label: 'DBH Position', :collection => [ 'DBH', 'Above', 'Below' ]
    .row
      .col-4 = f.input :height_breeding_value, label: 'Height Breeding Value (%height)'
      .col-4 = f.input :dbh_breeding_value, label: 'DBH Breeding Value (%DBH)'
    - if (@tree.source.code == 'REDE') || (@tree.source.code == 'CALL')
      .row
        .col-4 = f.input :height_breeding_value_gxe, label: 'Height Breeding Value GxE (%height)', hint: 'Genotype by Environment Interaction'
        .col-4 = f.input :dbh_breeding_value_gxe, label: 'DBH Breeding Value GxE (%DBH)', hint: 'Genotype by Environment Interaction'

  fieldset
    legend Condition
    .row
      - if @tree.species_id == 1 # lodgepole pine
        .col-8 = f.input :status_code, label: 'Lodgepole Pine Status Code', :collection => lodgepole_pine_status_code_collection
      - elsif @tree.species_id == 2 # white spruce
        .col-8 = f.input :status_code, label: 'White Spruce Status Code', :collection => white_spruce_status_code_collection
    .row
      .col-8 = f.input :condition_code, label: 'Condition Code(s)', :collection => condition_code_collection, as: :check_boxes, input_html: { multiple: true }
    .row
      - if @tree.species_id == 1 # lodgepole pine
        .col-4 = f.input :western_gall_rust_six_code, label: 'Western Gall Rust (Code 0-6)', :collection => western_gall_rust_six_code_collection
        .col-4 = f.input :western_gall_rust_two_code, label: 'Western Gall Rust (Code 0-2)', :collection => western_gall_rust_two_code_collection
        .col-4 = f.input :mountain_pine_beetle_presence, label: 'Mountain Pine Beetle Presence', :collection => [ ['Yes', 'Y'], ['No', 'N'] ]
      - elsif @tree.species_id == 2 # white spruce
        .col-4 = f.input :white_pine_weevil_presence, label: 'White Pine Weevil Presence (1) or Absence (0)', :collection => [0, 1]
        - if @characteristic.age_recorded == 24
          .col-4 = f.input :white_pine_weevil_score_sum, label: 'White Pine Weevil Sum', collection: 0..2, hint: 'the sum of white pine weevil scores (presence = 1, absence = 0) at ages 21 and 24'
    .row
      .col-8 = f.input :notes
  .form-actions
    = f.submit
