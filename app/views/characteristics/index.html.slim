h1 
  = "Showing Characteristics for Pasture #{@tree.code}"
  /= link_to_new_right_aligned "add #{Characteristic.title.downcase}", new_tree_characteristic_path(@tree)
table.list
  thead
    tr
      th Age Recorded (yr)
      th Height (cm)
      th DBH (cm)
      th Notes
      th colspan='1' Actions
  tbody
    - @characteristics.each do |characteristic|
      tr
        td = characteristic.age_recorded
        td = characteristic.height
        td = characteristic.dbh
        td = truncate(characteristic.notes, :ommision => "...", :length => 50)
        td
          = link_to_show tree_characteristic_path(@tree, characteristic)
        /td
          = link_to_edit edit_tree_characteristic_path(@tree, characteristic)
        /td
          = link_to_destroy tree_characteristic_path(@tree, characteristic)

br
