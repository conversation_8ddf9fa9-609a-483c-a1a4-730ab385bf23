li#root rel="subject" data-jstree='{"type":"subject"}'
  - if @tree.res_for_tree
    = link_to "Pasture #{@tree.code} (#{@tree.res_for_label})", tree_path(@tree)
  - else
    = link_to "Pasture #{@tree.code}", tree_path(@tree)
  ul
    li#tree-characteristics rel="characteristics" data-jstree='{"type":"characteristics"}'
      /= link_to Characteristic.title.pluralize, tree_characteristics_path(@tree)
      = link_to   "Pasture Characteristics", tree_characteristics_path(@tree)
      ul
        - @tree.characteristics.each do |characteristic|
          li id="tree-characteristic-#{characteristic.id}" rel="characteristic" data-jstree='{"type":"characteristic"}'
            = link_to "Year: #{characteristic.age_recorded}",
                tree_characteristic_path(@tree, characteristic)
    li#tree-samples
      = link_to "Samples", tree_samples_path(@tree)
      ul
        - @tree.samples.non_children.each do |sample|
          /- type = sample.aliquot? ? 'sub_sample' : 'sample'
          li id="tree-sample-#{sample.id}" rel="sample" data-jstree='{"type":"sample"}'
            = link_to "#{sample.code}", tree_sample_path(@tree, sample)

            - if sample.samples.any?
              ul
                - sample.samples.each do |sub_sample|
                  li id="tree-sample-#{sub_sample.id}" rel="sub_sample" data-jstree='{"type":"sub_sample"}'
                    = link_to "#{sub_sample.code}", tree_sample_path(@tree, sub_sample)
    li#tree-measurements
      = link_to "Measurements", tree_measurements_path(@tree)
      ul
        - @tree.measurements.each do |measurement|
          li id="tree-measurement-#{measurement.id}" rel="measurement" data-jstree='{"type":"measurement"}'
            = link_to "#{measurement.measurement_type.name}",
                sample_measurement_path(measurement.sample, measurement)
    
    li#tree-groups
      = link_to "Groups", tree_grouping_assignments_path(@tree)
      ul
        - @tree.grouping_assignments.each do |grouping_assignment|
          li id="tree-group-#{grouping_assignment.id}" rel="group" data-jstree='{"type":"group"}'
            = link_to grouping_assignment.grouping.name, tree_grouping_path(grouping_assignment.grouping)

