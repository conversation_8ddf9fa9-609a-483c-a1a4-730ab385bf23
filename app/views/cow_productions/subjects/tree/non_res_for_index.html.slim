h1
  = '<PERSON><PERSON><PERSON>'
  /= link_to_new_right_aligned 'add tree', new_tree_path
= search_form_for @search, url: trees_path, html: { method: :get } do |f|
  = f.hidden_field :category, value: "non_res_for"
  
  table.list
    thead
      tr.header
        th Pasture Name
        th Subpasture Name
        th Plot
        th Year
        th Treatment
        th Total Area
        th colspan="2" Actions
      tr.table-search
        th= f.text_field :code_eq, size: 8
        th= f.select :species_short_code_eq, Species.order(:code).map { |s| [s.code] }, include_blank: true
        th= f.select :source_short_code_eq, Source.order(:code).map { |s| [s.code] }, include_blank: true
        th= f.select :trial_eq, ['G128A', 'G128B', 'G128C', 'G128D', 'G132A', 'G132C', 'G132D'], include_blank: true
        th= f.text_field :family_cont, size: 8
        /th= f.text_field :notes_cont
        th= f.select :tags_name_cont, all_tags('Tree'), include_blank: true
        th colspan="2"
          = table_search_actions
    tbody
      - @trees.each do |tree|
        tr
          td= tree.code
          td= tree.species.code
          td= tree.source.code
          td= tree.updated_at.strftime("%Y")
          td= tree.treatment
          td= tree.notes
          /td= truncate(tree.notes)
          /td= tag_list(tree.tags)
          td= link_to_show tree
          td= link_to_edit edit_tree_path(tree)
          /td= link_to_destroy tree
= will_paginate@trees

