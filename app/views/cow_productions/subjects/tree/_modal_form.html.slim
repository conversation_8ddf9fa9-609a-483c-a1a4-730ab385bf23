= simple_form_for @tree, remote: true, format: :json, authenticity_token: true do |f|
  .modal-header
    h3.modal-title New Tree
    button type="button" class="close" data-dismiss="modal" aria-label="Close"
      span aria-hidden="true" &times;
  .modal-body
    div class="validation-error alert alert-danger" style="display: none"

    .row
      .col-4 = f.input :code
      .col-4 = f.association :species
      .col-4 = f.association :source
    .row: .col-12
      = f.input :notes

  .modal-footer
    button.btn.btn-default data-dismiss="modal" type="button" Cancel
    = f.submit 'Create tree', class: 'btn btn-primary', id: 'submit-new-subject'
