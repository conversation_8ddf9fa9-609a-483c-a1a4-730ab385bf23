h1
  = 'Cow Production Data'
  /= link_to_new_right_aligned 'add tree', new_tree_path
= search_form_for @search, url: cow_productions_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th Cow Tag
        th Cow Genotype ID 
        th Cow Birth Date 
        th Year Letter of Cow
        th Age of Cow (2025)
        th Pregnancy Status
        th Calf Birth Date
        th Estimated Expected Progeny Difference
        th Ranch
        /th Total Area
        / th Family
        / th Unique Tree Code
        / th Forward Selected
        / th Breeding Value Rank
        / th Tags
        th colspan="2"
      tr.table-search
        th= f.text_field :cw_tag_cont, size: 1
        th= f.text_field :cw_geno_id_cont, size: 1
        th= f.text_field :cw_brdt_cont
        th= f.select :cw_yr_letter_eq, CowProduction.pluck(:cw_yr_letter).uniq.sort!, include_blank: true
        th= f.select :cw_age_jul2025_yr_eq, CowProduction.pluck(:cw_age_jul2025_yr).uniq.sort!, include_blank: true
        th= f.select :cw_pgpreg_eq, ['P'], include_blank: true
        th= f.text_field :cf_brdt_cont, size: 1
        th
        th= f.select :ranch_eq, ['Kinsella', 'Mattheis'], include_blank: true
        th colspan="2"
          = table_search_actions
    tbody
      - @cow_productions.each do |prod|
        tr
          td= prod.cw_tag
          td= prod.cw_geno_id 
          td= prod.cw_brdt
          td= prod.cw_yr_letter
          td= prod.cw_age_jul2025_yr
          td= prod.cw_pgpreg
          td= prod.cf_brdt
          td= prod.rfi_gepd
          td= prod.ranch
          td
          td
          / td= link_to_edit edit_tree_path(tree)
          / td= link_to_destroy tree
= will_paginate@cow_productions

