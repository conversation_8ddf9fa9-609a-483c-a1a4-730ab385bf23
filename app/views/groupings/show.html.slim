h1
  | Showing #{humanized_type(@type)} Group "#{@grouping.name}"
  = link_to_edit_right_aligned({:action => 'edit', :type => @type})

p
  | <b>Description: </b> 
  = nah "#{@grouping.description}"

.new-association
  = render :partial => '/grouping_assignments/add_assignable_form', :locals => { :from => 'grouping' }
br

.group-count
  = render :partial => '/grouping_assignments/group_count'

.association
  table.list
    thead
      tr
        th
          = humanized_type(@type)
        th
          | Remove
    tbody#assignable-associations
      = render :partial => '/grouping_assignments/grouping_assignment', 
          :collection => @assignments, 
          :locals => { :from => 'grouping' }
  = will_paginate @assignments
  br
