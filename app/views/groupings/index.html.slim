h1 
  = "#{humanized_type @type} Groups"
  = link_to_new_right_aligned("add #{humanized_type(@type).downcase} group", { action: 'new', type: @type })
p.alert.alert-info
  ' #{hint} Groups allow you to make arbitrary collections of
  ' trees, samples, and measurements.
  ' You can organize your data into groups that can later be exported for further study.

ul.nav.nav-tabs
  - Grouping.valid_types.each do |grouping_type|
    li.nav-item
      - classes = ['nav-link']
      - classes << 'active' if grouping_type == @type
      = link_to "#{humanized_type grouping_type} Groups",
          groupings_path(type: grouping_type), class: classes
br

- if @groupings.present?
  table.list
    thead
      tr
        th Name
        th Description
        th Size
        th colspan="3" Actions
    tbody
      - @groupings.each do |grouping|
        tr
          td= grouping.name
          td= grouping.description
          td= grouping.assignables.count
          td= link_to_show grouping
          td= link_to_edit polymorphic_path(grouping, action: :edit)
          td= link_to_destroy polymorphic_path(grouping)
- else
  p.alert.alert-warning No groups

