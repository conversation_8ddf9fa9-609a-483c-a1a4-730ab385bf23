h1 
  = 'Pictures'
  = link_to_new_right_aligned 'upload new picture', new_picture_path

= search_form_for @search, url: pictures_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th Picture
        th Tags
        th Uploaded By
        th
          = sort_link(@search, :created_at, 'Uploaded On')
        th
          = sort_link(@search, :content_file_size, 'Size')
        th
          = sort_link(@search, :content_file_name, 'File')
        th colspan="2" Actions
      tr.table-search
        th
        th= f.select :tags_name_cont, all_tags('Picture'), include_blank: true
        th= f.select :user_id_eq, User.order(:name).map { |s| [s.name, s.id] }, include_blank: true
        th= f.text_field :created_at_cont
        th
        th
        th colspan="2"
          = table_search_actions
    tbody
      - @pictures.each do |picture|
        tr
          td= link_to image_tag(picture.content.url(:thumb)), picture.content.url
          td.align-middle
            = tag_list(picture.tags)
          td.align-middle
            = nah picture.user&.name
          td.align-middle
            = picture.created_at.to_date
          td.align-middle
            = number_to_human_size(picture.content.size)
          td.align-middle
            = link_to picture.content_file_name, picture.content.url
          td.align-middle
            = link_to_edit edit_picture_path(picture)
          td.align-middle
            = link_to_destroy [picture]

= will_paginate@pictures
