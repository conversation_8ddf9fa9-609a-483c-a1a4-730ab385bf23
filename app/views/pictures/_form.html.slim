= knoxy_form_for @picture do |f|
  = render_form_errors(@picture)

  fieldset
    = f.input :tag_list, collection: all_tags('Picture'), label: "Tags",
          input_html: { class: 'taggable', multiple: true } 
    

    - if @picture.content.exists? 
      | Attached File: <b>#{@picture.content_file_name}</b>
      br

      - if @picture.content
        p = image_tag @picture.content.url(:thumb)

      - if @current_user.administrator?
        = "Attach a New File"
        = f.input :content, label: false
    - else
      = f.input :content, as: :file

      

  .form-actions
    = f.submit
