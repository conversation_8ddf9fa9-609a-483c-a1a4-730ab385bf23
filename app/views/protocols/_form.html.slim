= render_form_errors(@protocol)
= knoxy_form_for(@protocol) do |f|
  fieldset[style="width: 50%"]
    legend
      | Protocol Details
    = f.input :name, required: true
    = f.input :version, size: 5
    = f.input :comments

    - if @protocol.storage.exists? 
      | Attached File: <b>#{@protocol.storage_file_name}</b>
      br
      br
      /= "Attach a New File"
      /= f.input :storage, label: false, required: true
    - else
      = f.input :storage, required: true

  .field-no-label.actions
    = f.submit
