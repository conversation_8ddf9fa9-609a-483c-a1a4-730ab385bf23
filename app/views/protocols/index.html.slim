h1
  = "All Protocols"
  = link_to_new_right_aligned 'add protocol', new_protocol_path

= search_form_for @search, url: protocols_path, html: { method: :get } do |f|
  table.list
    thead
      tr
        th Name
        th Version
        th Comments
        th Created At
        th colspan="2" Actions
      tr.table-search
        th= f.text_field :name_cont
        th= f.select :version_eq, Protocol.all.pluck('version').uniq, include_blank: true
        th= f.text_field :comments_cont
        th= f.text_field :created_at_cont
        th colspan="2"
          = table_search_actions
    tbody
      - @protocols.each do |protocol|
        tr
          td= protocol.name
          td= protocol.version
          td= protocol.comments
          td= protocol.created_at.strftime("%Y-%m-%d")
          td= link_to_show protocol
          td= link_to_edit edit_protocol_path(protocol)
          /td= link_to_destroy protocol

= will_paginate@protocols
