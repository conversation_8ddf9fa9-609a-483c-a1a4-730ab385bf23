= form_for( :grouping, :url => grouping_grouping_assignments_path(@grouping), :remote => true, :html => { :class => 'standard' } ) do

  fieldset
    legend
      | Add

    = fields_for :grouping_assignment, GroupingAssignment.new(:assignable_type => @grouping.assignable_type) do |ca_fields|
      = ca_fields.hidden_field :assignable_type
      = "#{humanized_type(@grouping.assignable_type)}(s)"
      | : 
      br

      - if @grouping.assignable_type == "Tree"
        = ca_fields.text_area :assignable_codes, size: "70% x 5%", placeholder: "Enter one tree code per line.\n1000\n2323\n5792"
      - elsif @grouping.assignable_type == "Sample"
        = ca_fields.text_area :assignable_codes, size: "70% x 5%", placeholder: "Enter one sample code per line.\n1000-GE\n2323-RD\n5792-PP"
      - else
        = ca_fields.select(:assignable_id, @grouping.assignable_type.constantize.all.collect {|p| [ p.to_s, p.id ] }, { :include_blank => "Select a #{@grouping.assignable_type}" }, {class: "dropdown_search"})
      br
      br
      = submit_tag "Add #{humanized_type @grouping.assignable_type}(s)"
