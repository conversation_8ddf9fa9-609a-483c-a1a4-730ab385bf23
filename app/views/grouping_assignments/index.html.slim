h1 
  = "Showing Groups for Pasture #{@tree.code}"
  = link_to_new_right_aligned "add to group",
    { action: 'new', "#{@assignable_type.tableize.singularize}_id" => @assignable }

table.list
  thead
    tr
      th Group
      th Size
      th colspan="1" Actions
  tbody
    - @grouping_assignments.each do |grouping_assignment|
      tr
        td= link_to grouping_assignment.grouping.name, grouping_assignment.grouping
        td= grouping_assignment.grouping.assignables.length
        td
          = link_to 'Remove',
              { action: 'destroy', id: grouping_assignment, "#{@assignable_type.tableize.singularize}_id" => @assignable },
              data: { confirm: 'Are you sure?' },
              method: :delete

br
