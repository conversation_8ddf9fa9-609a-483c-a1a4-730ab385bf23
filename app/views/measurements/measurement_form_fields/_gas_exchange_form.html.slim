.row: .col-6
  - Measurement.measurement_value_types_array('GE').each do |measurement_value|
    - if measurement_value == 'time_recorded'
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), placeholder: 'HH:MM:SS', :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
    - elsif measurement_value == 'instrument_num'
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :collection => [ [1, 1], [2, 2] ], selected: @measurement.send(MeasurementValue.value_function(measurement_value)), include_blank: true
    - elsif measurement_value == 'intrinsic_water_use_efficiency'
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), hint: '(photosynthetic rate * 1 000 000)/stomatal conductance', :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
    - else
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
