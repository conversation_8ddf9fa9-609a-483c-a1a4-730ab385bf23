.row: .col-6
  - Measurement.measurement_value_types_array('PP').each do |measurement_value|
    - if measurement_value == 'total_polyphenolics'
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), hint: 'sum of all GC analyzed and identified chemicals', :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
    - else 
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
