.row
  .col-6
    - Measurement.measurement_value_types_array('MT').each do |measurement_value|
      - if measurement_value.include? 'ww'
        - if measurement_value == 'total_monoterpenes'
          = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), hint: 'sum of all GC analyzed and identified chemicals', :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
        - else
          = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
  .col-6
    - Measurement.measurement_value_types_array('MT').each do |measurement_value|
      - if measurement_value.include? 'dw'
        - if measurement_value == 'total_monoterpenes'
          = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), hint: 'sum of all GC analyzed and identified chemicals', :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
        - else
          = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
