- code = "MP"

- if !@tree
  div class="#{code}_measurement_phase_select"
    = select_tag "#{code}_measurement_phase", options_for_select(metabolomics_phases), :prompt => "Select field or a greenhouse phase"
  br

  div class="#{code}_measurements_show_container"
    - metabolomics_phases.each do |phase|
      div id="#{phase}_#{code}_measurements"
        .row
          .col
            u
              h3 Non-Glycerophospholipids
            dl
              - Measurement.measurement_value_types_array(code).each do |measurement_value|
                - if (measurement_value.split("_")[-1] == phase) && (metabolomics_primary_non_glycerophospholipids.any? { |metal| measurement_value.include?(metal) })
                  = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
          .col
            u
              h3 Glycerophospholipids
            dl
              - Measurement.measurement_value_types_array(code).each do |measurement_value|
                - if (measurement_value.split("_")[-1] == phase) && (metabolomics_primary_glycerophospholipids.any? { |metal| measurement_value.include?(metal) })
                  = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}

- elsif @tree.source.code == "GH"
  div class="#{code}_measurement_phase_select"
    = select_tag "#{code}_measurement_phase", options_for_select(metabolomics_greenhouse_phases), :prompt => "Select phase"
  br

  div class="#{code}_measurements_show_container"
    - metabolomics_greenhouse_phases.each do |phase|
      div id="#{phase}_#{code}_measurements"
        .row
          .col
            u
              h3 Non-Glycerophospholipids
            dl
              - Measurement.measurement_value_types_array(code).each do |measurement_value|
                - if (measurement_value.split("_")[-1] == phase) && (metabolomics_primary_non_glycerophospholipids.any? { |metal| measurement_value.include?(metal) })
                  = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
          .col
            u
              h3 Glycerophospholipids
            dl
              - Measurement.measurement_value_types_array(code).each do |measurement_value|
                - if (measurement_value.split("_")[-1] == phase) && (metabolomics_primary_glycerophospholipids.any? { |metal| measurement_value.include?(metal) })
                  = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}

- else # field trees
  .row
    .col
      u
        h3 Non-Glycerophospholipids
      dl
        - Measurement.measurement_value_types_array(code).each do |measurement_value|
          - if (measurement_value.include? "FIELD") && (metabolomics_primary_non_glycerophospholipids.any? { |metal| measurement_value.include?(metal) })
            = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
    .col
      u
        h3 Glycerophospholipids
      dl
        - Measurement.measurement_value_types_array(code).each do |measurement_value|
          - if (measurement_value.include? "FIELD") && (metabolomics_primary_glycerophospholipids.any? { |metal| measurement_value.include?(metal) })
            = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
