.row: .col-6
  - Measurement.measurement_value_types_array('IR').each do |measurement_value|
    - if measurement_value == 'limonene_percentage'
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), hint: 'limonene/(total monoterpenes - beta-phellandrene)', :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
    - elsif measurement_value == 'mpb_resistance_rank'
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :collection => [ [1, 1], [2, 2], [3, 3], [4, 4], [5, 5], [6, 6] ], selected: @measurement.send(MeasurementValue.value_function(measurement_value)), include_blank: true
    - else
      = f.input MeasurementValue.symbol(measurement_value), label: MeasurementValue.full_title(measurement_value), :input_html => {:value => @measurement.send(MeasurementValue.value_function(measurement_value))}
