h1
  - if !@tree.present?
    'Measurements
  - else 
    'Showing Measurements for Pasture #{@tree.code}

  - if @tree.present? && @tree.samples.any?
    = link_to_new_right_aligned "add measurement for Tree #{@tree.code}", new_tree_measurement_path(@tree)
  - elsif !@tree.present?
    = link_to_new_right_aligned 'add measurement', new_measurement_path

= search_form_for @search, url: measurements_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        /th= Tree.title
        th Sample
        th Type
        /th = 'Description' unless @tree.present?
        /th To Be Analyzed By
        /th To Be Analyzed On
        th Measured By
        th Date Measured
        th Data Files
        th Tags
        th colspan="3" Actions
      - if @tree.blank?
        tr.table-search
          th= f.text_field :sample_code_cont
          th= f.select :measurement_type_name_eq, MeasurementType.order(:name).map { |s| [s.name, s.name] }, include_blank: true
          /th= f.select :assigned_to_id_eq, User.order(:name).map { |s| [s.name, s.id] }, include_blank: true
          /th= f.text_field :perform_on_cont
          th= f.select :performed_by_id_eq, User.order(:name).map { |s| [s.name, s.id] }, include_blank: true
          th= f.text_field :performed_on_cont
          th= f.text_field :data_files_data_file_name_cont
          th= f.select :tags_name_cont, all_tags('Measurement'), include_blank: true
          th colspan="3"
            = table_search_actions
    tbody
      - @measurements.each do |measurement|
        tr
          /td= link_to(measurement.sample.root, measurement.sample.root)
          td= link_to(measurement.sample, measurement.sample)
          td= measurement.measurement_type.name unless measurement.measurement_type.nil?
          /td= nah truncate(measurement.description, :length => 50) unless @tree.present?
          /td
            - if measurement.assigned_to.present? 
              = link_to(measurement.assigned_to.name, measurement.assigned_to) 
          /td= measurement.perform_on
          td
            - if measurement.performed_by
              = link_to(measurement.performed_by.name, measurement.performed_by)
          td = measurement.performed_on_range
          td
            - if measurement.data_files.length > 0
              ul
                - measurement.data_files.each do |data_file|
                  li= link_to data_file.data_file_name, data_file.data.url
          td= tag_list(measurement.tags)
          td= link_to_show [measurement.sample, measurement]
          td= link_to_edit edit_sample_measurement_path(measurement.sample, measurement)
          td= link_to_destroy [measurement.sample, measurement]

- if @tree.present? && !@tree.samples.any?
  p.alert.alert-info
    ' #{hint} You must have at least one sample for this tree before you can
    ' create an measurement.
= will_paginate@measurements

