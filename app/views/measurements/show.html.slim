h1
  | Showing  
  font[color="blue"]
    | #{@measurement.measurement_type.name} 
  | Measurement for Sample #{@measurement.sample.code}
  - if @sample != nil
    = link_to_history 'history', history_sample_measurement_path(@sample, @measurement)
br
.card-deck
  .card
    .card-header Measurement Details
    .card-block
      dl
        dt Measurement Type:
        dd = nah @measurement.measurement_type.name
        dt Protocol (SOP):
        dd
          - if @measurement.protocol.present?
            = link_to @measurement.protocol.name, protocol_path(@measurement.protocol)
            '  (Version #{@measurement.protocol.version})
          - else
            = nah
        dt Description:
        dd = nah @measurement.description
        dt Tags
        dd = nah @measurement.tag_list
        /dt Planned Analysis Date:
        /dd = nah @measurement.perform_on
        /dt Assigned To:
        /dd = nah @measurement.assigned_to&.name
        dt Measured By:
        dd = nah @measurement.performed_by&.name
        dt Date Measured:
        dd = nah @measurement.performed_on_range
        dt Amount Used:
        dd = nah "#{@measurement.amount_used} #{@measurement.amount_used_unit}"
    .card-footer
      .btn-group
        - if @sample.present?
          = link_to_edit edit_sample_measurement_path(@sample, @measurement)
        - else
          = link_to_edit edit_measurement_path(@measurement)

  .card
    .card-header Sample Details
    .card-block
      dl
        dt Sample Type:
        dd = nah @measurement.sample.sample_type.capitalize
        dt Sample Code:
        dd = nah @measurement.sample.code
        dt Collected On:
        dd = nah @measurement.sample.collected_on_range
        dt Collected By:
        dd 
          - if @measurement.sample.collected_by.nil?
            = nah
          - else
            = @measurement.sample.collected_by.name

        - if @sample.suffix == 'MT'
          dt Amount (Wet Weight):
        - else
          dt Original Amount:
        dd = nah "#{@sample.original_amount} #{@sample.original_unit}"

        - if @sample.suffix == 'MT'
          dt Amount (Dry Weight):
        - else
          dt Remaining Amount:
        dd = nah "#{@sample.actual_amount} #{@sample.actual_unit}"

        dt Description:
        dd = nah @measurement.sample.description
    .card-footer
      .btn-group
        = link_to_show @measurement.sample, "more info"

br
.card-deck
  .card
    .card-header Measurement Data
    .card-block
      = render :partial => "measurements/measurement_show_fields/#{@measurement.measurement_type.name.parameterize.underscore}_show"
        
  .card
    .card-header Data Files
    .card-block
      - if @measurement.data_files.blank?
        | There are no data file associated with this measurement.
      - else
        table.table.table-sm
          thead
            tr
              th Filename:
              th Size:
              th Type:
              th colspan="3"  Actions
          tbody
            - @measurement.data_files.each do |data_file|
              tr
                td= link_to data_file.data_file_name, data_file.data.url
                td= number_to_human_size(data_file.data_file_size)
                td= link_to data_file.data_file_type.name, data_file_type_path(data_file.data_file_type)
                td= link_to_show measurement_data_file_path(@measurement, data_file)
                td= link_to_destroy measurement_data_file_path(@measurement, data_file)

    .card-footer
      .btn-group
        = link_to_new 'upload new data file', new_measurement_data_file_path(@measurement)

br
dt Last Updated: #{@measurement.updated_at.in_time_zone('Mountain Time (US & Canada)').strftime("%Y-%m-%d %H:%M")} MST by #{@measurement.last_user_updated.name}

