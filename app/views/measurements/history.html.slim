h1
  | History of 
  font[color="blue"]
    | #{@measurement.measurement_type.name} 
  | Measurement for Sample #{@measurement.sample.code}
  = link_to_return_right_aligned 'return', sample_measurement_path(@sample, @measurement)

table.list
  thead
    tr
      th Version ID
      th Date
      th Author
      th Event
      th Diff (Before → After)
      th Rollback
    tbody
      - total = @history.length
      - if @page > 1
        - total -= (15 * (@page - 1))
      - @versions.each do |version|
        - measurement_changeset = parse_measurement_changeset(version)
        - if measurement_changeset.length > 0
          tr
            td= total
            td= "#{version.created_at.in_time_zone('Mountain Time (US & Canada)').strftime("%Y-%m-%d %H:%M")} MST"
            td= User.find_by(id: version.whodunnit).name
            td= version.event.humanize
            td
              ul
                - measurement_changeset.each do |change|
                  li
                    b = change[0]
                    = ": #{change[1][0]} → #{change[1][1]}"
            td= link_to 'Rollback', rollback_sample_measurement_path(@sample, @measurement, version_id: version.id), :method=> :patch
        - total -= 1
br

= will_paginate@versions
