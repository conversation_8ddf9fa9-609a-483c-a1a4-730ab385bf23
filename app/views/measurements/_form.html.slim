= knoxy_form_for @measurement, url: url do |f|
  = render_form_errors(@measurement)

  fieldset
    legend Measurement Details 
    /.row: .col-6
      = f.association :sample, collection: defined?(@tree) ? @tree.samples : Sample.all,
          label: 'Sample Code',
          input_html: { class: 'select2-basic', style: 'width: 100%;' }
    .row
      - if defined?(@sample)
        .col-4 = f.input :sample_placeholder, :input_html => { :value => @sample }, label: 'Sample', readonly: true
        .col-4 
          .measurement_type_select
            = f.association :measurement_type, selected: MeasurementType.find_by(code: @sample.suffix).id, disabled: true, label: 'Measurement Type'
      - else
        .col-4 = f.association :sample, collection: defined?(@tree) ? @tree.samples : Sample.all,
          label: 'Sample',
          input_html: { class: 'select2-basic', style: 'width: 100%;' }
        .col-4 
          .measurement_type_select
            = f.association :measurement_type, collection: MeasurementType.order(:code), include_blank: true, label: 'Measurement Type', input_html: { class: 'select2-basic', style: 'width: 100%;' }
      .col-4
        = f.association :protocol, include_blank: true, input_html: { class: 'select2-basic', style: 'width: 100%;' }, label: 'Protocol (SOP)'
    /- else
      .row
        .col-4 = f.input :tree_code, label: 'Tree Code'
        .col-4 = f.association :measurement_type, include_blank: true, label: 'Measurement Type'
        .col-4 = f.input :sample_type, collection: Sample.sample_types, include_blank: true, label: 'Sample Type'
      .row
        .col-4 = f.association :protocol, include_blank: true

    = f.input :description
    = f.input :tag_list, collection: all_tags('Measurement'), label: "Tags",
        input_html: { class: 'taggable', multiple: true }
    .row
      /.col-6
        = f.association :assigned_to, include_blank: true, label: 'Assigned To',
            input_html: { class: 'select2-basic', style: 'width: 100%;' }
        = f.input :perform_on, as: :string, label: 'Perform On',
            input_html: { type: 'date' }, placeholder: 'YYYY-MM-DD'
      .col-4
        = f.association :performed_by, include_blank: true, label: 'Measured By',
            input_html: { class: 'select2-basic', style: 'width: 100%;' }
      .col-4
        = f.input :performed_on, as: :string, label: 'Date Measured (Start Date)',
            input_html: { type: 'date' }, placeholder: 'YYYY-MM-DD'
      .col-4
        = f.input :performed_on_end, as: :string, label: 'Date Measured (End Date)',
            input_html: { type: 'date' }, placeholder: 'YYYY-MM-DD', hint: '*optional'

    .row
      .col-3 = f.input :amount_used, label: 'Amount Used'
      .col-3 = f.input :amount_used_unit, collection: weight_unit_options, label: 'Amount Used Unit'

  fieldset
    legend Measurement Data
    .measurement_data
      - MeasurementType.pluck(:name).each do |measurement_type_name|
        - measurement_type_parameter = measurement_type_name.parameterize.underscore

        div id="#{measurement_type_parameter}"
          = render :partial => "measurements/measurement_form_fields/#{measurement_type_parameter}_form", :locals => {:f => f}
    
  fieldset
    legend Data Files
    #data-files
      = f.simple_fields_for :data_files do |data_file|
        = render 'data_file_fields', f: data_file
      .links
        = link_to_add_association 'add data file', f, :data_files, class: 'btn btn-secondary btn-sm'
  .form-actions
    = f.submit
