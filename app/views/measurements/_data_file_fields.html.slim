.nested-fields.card style="display: #{f.object._destroy ? 'none' : 'block'}"
  .card-block
    .float-right = link_to_remove_association "remove", f, class: 'btn btn-sm btn-outline-danger'

    - if f.object.data.exists? 
      | Attached File: <b>#{f.object.data_file_name}</b>
    - else
      = f.input :data, as: :file

    = f.association :data_file_type, required: true, label: 'Data File Type'
    = f.input :description
