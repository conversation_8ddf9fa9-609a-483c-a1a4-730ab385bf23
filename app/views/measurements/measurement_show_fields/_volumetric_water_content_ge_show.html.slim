- code = "VG"

- if @measurement.tree.source.code == "GH"
  div class="#{code}_measurement_phase_select"
    = select_tag 'measurement_phase', options_for_select(vwc_phases), :prompt => "Select phase"
  br

  div class="#{code}_measurements_show_container"
    - vwc_phases.each do |phase|
      div id="#{phase}_#{code}_measurements"
        dl
          - Measurement.measurement_value_types_array(code).each do |measurement_value|
            - if measurement_value.split("_")[-1] == phase
              dt #{MeasurementValue.full_title(measurement_value)}:
              dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
