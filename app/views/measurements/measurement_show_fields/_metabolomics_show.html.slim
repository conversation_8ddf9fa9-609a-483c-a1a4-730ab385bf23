- code = "MB"

- if @measurement.tree.source.code == "GH"
  div class="#{code}_measurement_phase_select"
    = select_tag 'measurement_phase', options_for_select(metabolomics_greenhouse_phases), :prompt => "Select phase"
  br

  div class="#{code}_measurements_show_container"
    - metabolomics_greenhouse_phases.each do |phase|
      div id="#{phase}_#{code}_measurements"
        .row
          .col
            u
              h3 Metals/Chemical Elements
            dl
              - Measurement.measurement_value_types_array(code).each do |measurement_value|
                - if (measurement_value.split("_")[-1] == phase) && (metabolomics_elements.any? { |metal| measurement_value.include?(metal) })
                  dt #{MeasurementValue.full_title(measurement_value)}:
                  - display_value = @measurement.send(MeasurementValue.value_function(measurement_value))
                  - if (display_value.present?) && (display_value.include? "<") # issues rendering '<' in HTML
                    dd
                      | #{display_value}
                  - else
                    dd = nah display_value
          .col
            u
              h3 Polyphenols
            dl
              - Measurement.measurement_value_types_array(code).each do |measurement_value|
                - if (measurement_value.split("_")[-1] == phase) && (metabolomics_polyphenols.any? { |metal| measurement_value.include?(metal) })
                  dt #{MeasurementValue.full_title(measurement_value)}:
                  - display_value = @measurement.send(MeasurementValue.value_function(measurement_value))
                  - if (display_value.present?) && (display_value.include? "<") # issues rendering '<' in HTML
                    dd
                      | #{display_value}
                  - else
                    dd = nah display_value

- else # field trees
  .row
    .col
      u
        h3 Metals/Chemical Elements
      dl
        - Measurement.measurement_value_types_array(code).each do |measurement_value|
          - if (measurement_value.split("_")[-1] == "FIELD") && (metabolomics_elements.any? { |metal| measurement_value.include?(metal) })
            dt #{MeasurementValue.full_title(measurement_value)}:
            - display_value = @measurement.send(MeasurementValue.value_function(measurement_value))
            - if (display_value.present?) && (display_value.include? "<") # issues rendering '<' in HTML
              dd
                | #{display_value}
            - else
              dd = nah display_value
    .col
      u
        h3 Polyphenols
      dl
        - Measurement.measurement_value_types_array(code).each do |measurement_value|
          - if (measurement_value.split("_")[-1] == "FIELD") && (metabolomics_polyphenols.any? { |metal| measurement_value.include?(metal) })
            dt #{MeasurementValue.full_title(measurement_value)}:
            - display_value = @measurement.send(MeasurementValue.value_function(measurement_value))
            - if (display_value.present?) && (display_value.include? "<") # issues rendering '<' in HTML
              dd
                | #{display_value}
            - else
              dd = nah display_value
