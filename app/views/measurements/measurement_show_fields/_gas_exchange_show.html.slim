dl
  - Measurement.measurement_value_types_array('GE').each do |measurement_value|
    - if measurement_value == 'photosynthetic_rate'
      dt Photosynthetic Rate (µmol CO<sub>2</sub> m<sup>-2</sup> s<sup>-1</sup>):
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - elsif measurement_value == 'stomatal_conductance'
      dt Stomatal Conductance (mol H<sub>2</sub>O m<sup>-2</sup> s<sup>-1</sup>):
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - elsif measurement_value == 'transpiration_rate'
      dt Transpiration Rate (mmol H<sub>2</sub>O m<sup>-2</sup> s<sup>-1</sup>):
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - elsif measurement_value == 'intercellular_co2_conc'
      dt Intercellular CO<sub>2</sub> concentration (μmol CO<sub>2</sub> mol air<sup>-1</sup>):
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - elsif measurement_value == 'co2_ratio'
      dt Intercellular:Ambient CO<sub>2</sub> Concentration Ratio:
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - elsif measurement_value == 'intrinsic_water_use_efficiency'
      dt Intrinsic Water Use Efficiency (mol CO<sub>2</sub> mol H<sub>2</sub>O<sup>-1</sup>):
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - else
      dt #{MeasurementValue.full_title(measurement_value)}:
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
