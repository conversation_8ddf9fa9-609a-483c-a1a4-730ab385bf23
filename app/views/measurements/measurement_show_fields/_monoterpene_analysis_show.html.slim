.row
  .col
    dl
      - Measurement.measurement_value_types_array('MT').each do |measurement_value|
        - if measurement_value.include? 'ww'
          dt #{MeasurementValue.full_title(measurement_value)}:
          dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
  .col
    dl
      - Measurement.measurement_value_types_array('MT').each do |measurement_value|
         - if measurement_value.include? 'dw'
          dt #{MeasurementValue.full_title(measurement_value)}:
          dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
