dl
  - Measurement.measurement_value_types_array('RD').each do |measurement_value|
    - if measurement_value == 'ten_year_mean_area'
      dt Average area of resin duts for last 10 years (mm<sup>2</sup>):
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - elsif measurement_value == 'ten_year_mean_density'
      dt Average resin duct density for last 10 years (cm<sup>-2</sup>):
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
    - else 
      dt #{MeasurementValue.full_title(measurement_value)}:
      dd = nah @measurement.send(MeasurementValue.value_function(measurement_value))
