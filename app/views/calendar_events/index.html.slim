h1 
  = 'Calendar Events'
  = link_to_new_right_aligned 'add new event', new_calendar_event_path

= search_form_for @search, url: calendar_events_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th Name
        th Start Date
        th End Date (Optional)
        th colspan="3"
          = 'Actions'
      tr.table-search
        th= f.text_field :name_cont
        th= f.text_field :start_time_cont
        th= f.text_field :end_time_cont
        th colspan="3"
          = table_search_actions
    tbody
      - @calendar_events.each do |calendar_event|
        tr
          td= calendar_event.name
          td= calendar_event.start_time
          td= nah calendar_event.end_time
          td= link_to_show calendar_event
          td= link_to_edit edit_calendar_event_path(calendar_event)
          td= link_to_destroy calendar_event

= will_paginate@calendar_events
