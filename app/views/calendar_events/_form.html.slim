= knoxy_form_for @calendar_event do |f|
  = render_form_errors(@calendar_event)
  fieldset[style="width: 50%"]
    legend Details
    .row
      .col-12 = f.input :name
    .row
      .col-6 = f.input :start_time, as: :string, label: 'Start Date', input_html: { type: 'date' }, placeholder: 'YYYY-MM-DD'
      .col-6 = f.input :end_time, as: :string, label: 'End Date (Optional)', input_html: { type: 'date' }, placeholder: 'YYYY-MM-DD', hint: 'No need to provide for one-day events'
  .form-actions
    = f.submit
