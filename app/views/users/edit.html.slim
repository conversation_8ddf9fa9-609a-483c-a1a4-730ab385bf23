- if current_user == @user
  h1 Edit my account
- else
  h1 Edit user

= simple_form_for(setup_user(@user), html: { multipart: true }) do |f|
  = render_form_errors(@user)

  .card-deck
    .card
      h3.card-header Account Details
      .card-block
        = f.input :name
        = f.input :email
        = f.fields_for :photo_file do |ff|
          = ff.input :attachment, label: 'Profile Photo'
          - if @user.persisted? && ff.object.attachment.present?
            = link_to ff.object.attachment_file_name, ff.object.attachment.url
            = ff.input '_destroy', label: 'remove?', as: :boolean
        - if current_user.administrator? && current_user != @user
          .row
            .col-4 = f.association :site, include_blank: true
            .col-4 = f.input :role, collection: User.roles.keys, include_blank: true
            .col-4 = f.input :status, collection: User.statuses.keys

    - if @user == current_user
      .card
        h3.card-header Change your password
        .card-block
          = f.input :password, hint: "Only required if you are changing your password."
          = f.input :password_confirmation, label: 'Confirm Password'
      .card
        h3.card-header Confirm your current password
        .card-block
          = f.input :current_password, required: true,
              hint: 'Your current password is required to make any changes to your account.'

  br
  = f.submit 'Update', class: 'btn btn-primary btn-md'

