<p><%= t("devise.mailer.invitation_instructions.hello", email: @resource.email) %></p>

<p><%= t("devise.mailer.invitation_instructions.someone_invited_you", url: root_url) %></p>

<p><%= link_to t("devise.mailer.invitation_instructions.accept"), accept_invitation_url(@resource, :invitation_token => @token) %></p>

<% if @resource.invitation_due_at %>
  <p><%= t("devise.mailer.invitation_instructions.accept_until", due_date: l(@resource.invitation_due_at, format: :'devise.mailer.invitation_instructions.accept_until_format')) %></p>
<% end %>

<p><%= t("devise.mailer.invitation_instructions.ignore").html_safe %></p>
