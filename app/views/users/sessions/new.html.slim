.container-fluid
  .row.align-items-stretch
    .col-md-4.d-flex
      .card.h-100.w-100
        h2.card-header Access PeaCE LIMS 
        .card-block
          = simple_form_for(resource, as: resource_name, url: session_path(resource_name)) do |f|
            = f.input :email
            = f.input :password
            = f.input :remember_me, as: :boolean

            = f.submit "Login", class: 'btn btn-primary'
          hr
          = render partial: "devise/shared/links"
    /.col-md-8.d-flex
      = image_tag("acknowledgment.png", class: 'img-fluid w-100', :alt => "CAT-G sponsors", style: "border-style: solid; border-width: 1px; border-color: rgba(0, 0, 0, 0.125); border-radius: 0.25rem;")
