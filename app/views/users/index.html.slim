- if @site
    h1 'All Users for "#{@site.name}"''
- else
    h1 
      = 'All Users'
      = link_to_new_right_aligned 'invite new user', new_user_path

= search_form_for @search, url: users_path, html: { method: :get } do |f|
  table.list
    thead
      tr
        th Picture
        th Name
        th = Site.model_name.human
        th Email
        th colspan="2" Actions
      tr.table-search
          th
          th= f.text_field :name_cont
          th= f.select :site_id_eq, Site.order(:name).map { |s| [s.name, s.id] }, include_blank: true
          th= f.text_field :email_cont
          th colspan="2"
            = table_search_actions
    tbody
      - @users.each do |user|
        tr
          td
            - if user.photo_file.present?
              = image_tag(user.photo_file.attachment.url(:thumb))
            - else
              = image_tag("user.png", size: "100x100")
          td.align-middle
            = user.name
          td.align-middle 
            = nah user.site&.name
          td.align-middle
            = user.email
          td.align-middle.fit-row
            = link_to_show user
          td.align-middle.fit-row
            = link_to_edit edit_user_path(user)
          /td.align-middle.fit-row
            = link_to_destroy user

= will_paginate@users
