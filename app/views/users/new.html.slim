h1 Create a new user

= knoxy_form_for(setup_user(@user), html: { multipart: true }) do |f|
  = render_form_errors(@user)
  fieldset
    .row: .col-6
      = f.input :name, required: true
      = f.input :email, required: true
      = f.association :site, include_blank: true
      /= f.input :role, collection: User.roles.keys, include_blank: true
      = f.input :role, collection: User.roles.keys.map { |role| [role.capitalize, role] }, include_blank: true

  .form-actions
    = f.submit 'Create and invite'

