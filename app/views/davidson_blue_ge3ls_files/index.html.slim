h1 
  | Davidson/Blue GE<sup>3</sup>LS Impacts Group Files
  = link_to_new_right_aligned 'upload new file', new_davidson_blue_ge3ls_file_path

= search_form_for @search, url: davidson_blue_ge3ls_files_path, html: { method: :get } do |f|
  table.list
    thead
      tr.header
        th
          = sort_link(@search, :title, 'Title')
        th
          = sort_link(@search, :description, 'Description')
        th Uploaded By
        th
          = sort_link(@search, :created_at, 'Uploaded On')
        th Tags
        th
          = sort_link(@search, :content_file_size, 'Size')
        th File
        th colspan="2" Actions
      tr.table-search
        th= f.text_field :title_cont, size: 8
        th= f.text_field :description_cont
        th= f.select :user_id_eq, User.order(:name).map { |s| [s.name, s.id] }, include_blank: true
        th= f.text_field :created_at_cont
        th= f.select :tags_name_cont, all_tags('DavidsonBlueGe3lsFile'), include_blank: true
        th
        th
        th colspan="2"
          = table_search_actions
    tbody
      - @davidson_blue_ge3ls_files.each do |davidson_blue_ge3ls_file|
        tr
          td= davidson_blue_ge3ls_file.title
          td= davidson_blue_ge3ls_file.description
          td= nah davidson_blue_ge3ls_file.user&.name
          td= davidson_blue_ge3ls_file.created_at.to_date
          td= tag_list(davidson_blue_ge3ls_file.tags)
          td= number_to_human_size(davidson_blue_ge3ls_file.content.size)
          td= link_to davidson_blue_ge3ls_file.content_file_name, davidson_blue_ge3ls_file.content.url
          td= link_to_edit edit_davidson_blue_ge3ls_file_path(davidson_blue_ge3ls_file)
          td= link_to_destroy [davidson_blue_ge3ls_file]

= will_paginate@davidson_blue_ge3ls_files
