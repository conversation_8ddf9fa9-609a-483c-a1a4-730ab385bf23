= knoxy_form_for @davidson_blue_ge3ls_file do |f|
  = render_form_errors(@davidson_blue_ge3ls_file)

  fieldset
    = f.input :title
    = f.input :description
    = f.input :tag_list, collection: all_tags('DavidsonBlueGe3lsFile'), label: "Tags",
          input_html: { class: 'taggable', multiple: true }

    - if @davidson_blue_ge3ls_file.content.exists?
      | Attached File: <b>#{@davidson_blue_ge3ls_file.content_file_name}</b>
      br
      br
      = "Attach a New File"
      = f.input :content, label: false
    - else
      = f.input :content, as: :file

  .form-actions
    = f.submit
