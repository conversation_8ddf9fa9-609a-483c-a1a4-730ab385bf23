h1 Showing Study "#{@study.name}"
- if @study.description.present?
  p= @study.description

hr
h4 Cohorts in this study:
table.list
  thead
    tr
      th Label
      th Control?
      th= Tree.title.pluralize
  tbody
    - @study.cohorts.each do |cohort|
      tr
        td = cohort.label
        td = cohort.control
        td == cohort.trees.map { |ts| link_to "#{ts.code} (#{ts.source.name})", ts }.join(", ")

.btn-group
  = link_to_edit edit_study_path(@study)

