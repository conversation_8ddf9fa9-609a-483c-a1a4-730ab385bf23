= knoxy_form_for @study, html: { autocomplete: false } do |f|
  = render_form_errors(@study)
  fieldset
    = f.input :name
    = f.input :description

  fieldset#cohorts.fields-for
    legend Cohorts
    #cohorts
      = f.simple_fields_for :cohorts do |cohort|
        = render 'cohort_fields', f: cohort
      .links
        = link_to_add_association 'add cohort', f, :cohorts, class: 'btn btn-secondary btn-sm'

  .form-actions
    = f.submit

