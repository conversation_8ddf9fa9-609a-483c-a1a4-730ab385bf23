.nested-fields.card style="display: #{f.object._destroy ? 'none' : 'block'}"
  .card-block
    .float-right = link_to_remove_association "remove", f, class: 'btn btn-sm btn-outline-danger'
    .row
      .col-6 = f.input :label
      .col-3 = f.input :control, collection: Cohort::ALLOWED_CONTROL_VALUES, label: 'Control?'

    #cohorts-trees
      = f.simple_fields_for :cohorts_trees do |cohort_tree|
        = render 'cohorts_tree_fields', f: cohort_tree
      .links
        = link_to_add_association 'add tree', f, :cohorts_trees,
            class: 'btn btn-secondary btn-sm'
