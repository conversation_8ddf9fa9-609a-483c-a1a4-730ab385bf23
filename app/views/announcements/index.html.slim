h1 
  = 'Announcements'
  = link_to_new_right_aligned 'add new announcement', new_announcement_path

table.list
  thead
    tr.header
      th Message
      th Display
      th Created At
      th colspan="1"
        = 'Actions'
  tbody
    - @announcements.each do |announcement|
      tr
        td= announcement.message
        td= boolean_to_english(announcement.display)
        td= announcement.created_at.strftime("%Y-%m-%d")
        td= link_to_edit edit_announcement_path(announcement)

= will_paginate@announcements
