# Methods added to this helper will be available to all templates in the application.
module ApplicationHelper
  VOLUME_UNITS = ['mL', 'L'].freeze
  WEIGHT_UNITS = ['mg', 'g'].freeze
  CONCENTRATION_UNITS = ['mol/L'].freeze

  # Accepts a title for a page - to be yielded in a template layout
  def title(page_title)
    content_for(:title) { strip_tags(page_title.to_s).html_safe }
    page_title.html_safe
  end

  def hint
    icon('info-circle')
  end

  def required
    '<span class="required">*</span>'
  end

  def all_tags(type)
    # Limit tags based on type
    ActsAsTaggableOn::Tag.joins(:taggings).where(:taggings => {:taggable_type => type}).map(&:name).uniq.sort

    # No limit based on type
    #ActsAsTaggableOn::Tag.all.map(&:name).uniq.sort
  end

  def unit_options
    VOLUME_UNITS.concat(WEIGHT_UNITS).concat(CONCENTRATION_UNITS).sort
  end

  def volume_unit_options
    VOLUME_UNITS
  end

  def weight_unit_options
    WEIGHT_UNITS
  end

  def boolean_to_english(val)
    val ? 'Yes' : 'No'
  end

  def boolean_word_to_english(boolean_word)
    if boolean_word == 'Y'
      return 'Yes'
    elsif boolean_word == 'N'
      return 'No'
    end
  end

  # Return the "Not Available" tag when the given value is blank.
  # Optionally pass in a message to use instead of "Not Available".
  # Return the given value if it is not blank.
  def nah(value=nil, message="---")
    if value.present?
      value.respond_to?(:html_safe) ? value.html_safe : value
    else
      "<span class='text text-muted'>#{message}</span>".html_safe
    end
  end

  def link_to_destroy(params, title='delete')
    link_to "#{icon(:remove)} #{title}".html_safe, params,
      data: { confirm: 'Are you sure you want to delete this entry?' },
      method: :delete, class: 'btn btn-outline-danger btn-sm',
      title: 'Delete Entry'
  end

  def link_to_edit(params, title='edit')
    link_to "#{icon(:edit)} #{title}".html_safe, params,
      class: 'btn btn-sm btn-outline-primary', title: 'Edit Entry'
  end

  def link_to_edit_right_aligned(params, title='edit')
    link_to "#{icon(:edit)} #{title}".html_safe, params,
      class: 'btn btn-sm btn-outline-primary pull-right', title: 'Edit Entry'
  end

  def link_to_return_right_aligned(title, params)
    link_to "#{icon(:'arrow-circle-left')} #{title}".html_safe, params,
      class: 'btn btn-md btn-outline-primary btn-space pull-right'
  end

  def link_to_return(title, params)
    link_to "#{icon(:'arrow-circle-left')} #{title}".html_safe, params,
      class: 'btn btn-sm btn-outline-primary'
  end

  def link_to_show(params, title='view')
    link_to "#{icon(:eye)} #{title}".html_safe, params,
      class: 'btn btn-sm btn-outline-primary', title: 'Show Entry'
  end

  def link_to_new(title, params)
    title ||= 'new'
    link_to "#{icon(:'plus-square')} #{title}".html_safe, params,
      class: 'btn btn-md btn-outline-success'
  end

  # right-aligned version
  def link_to_new_right_aligned(title, params)
    title ||= 'new'
    link_to "#{icon(:'plus-square')} #{title}".html_safe, params,
      class: 'btn btn-md btn-outline-success btn-space pull-right'
  end

  def link_to_history(title, params)
    title ||= 'History'
    link_to "#{icon(:'history')} #{title}".html_safe, params,
      class: 'btn btn-md btn-outline-success btn-space pull-right'
  end

  # export results as csv
  def link_to_csv_download(title, params)
    title ||= 'export'
    link_to "#{icon(:'download')} #{title}".html_safe, params,
      class: 'btn btn-md btn-outline-success btn-space pull-right'
  end

  def link_to_import(title, params)
    title ||= 'import'
    link_to "#{icon(:'upload')} #{title}".html_safe, params,
      class: 'btn btn-md btn-outline-success btn-space pull-right'
  end

  def link_to_add_fields(name, f, type)
    new_object = f.object.send "build_#{type}"
    id = "new_#{type}"
    fields = f.send("#{type}_fields", new_object, child_index: id) do |builder|
      render(type.to_s + "_fields", f: builder)
    end
    link_to(name, '#', class: "add_fields", data: {id: id, fields: fields.gsub("\n", "")})
  end

  def table_search_actions
    content_tag(:div, class: 'table-search-actions btn-group') do
      button_tag(icon(:search), type: 'submit', class: 'btn btn-info btn-sm') <<
      link_to('clear', request.path, class: 'btn btn-secondary btn-sm')
    end
  end

  # Provides a list of tags as label for use in a table, etc.
  def tag_list(tags=[])
    tags.map { |t| content_tag(:span, t.name, class: %w(badge badge-pill badge-default)) }.join(' ').html_safe
  end
end
