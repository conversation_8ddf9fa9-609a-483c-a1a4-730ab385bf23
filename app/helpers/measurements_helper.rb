module MeasurementsHelper
  def metabolomics_phases
    %w(FIELD PRE DD2 HAR)
  end

  def metabolomics_greenhouse_phases
    %w(PRE DD2 HAR)
  end

  def metabolomics_elements
    %w(lithium boron sodium magnesium aluminum phosphorus potassium calcium vanadium manganese iron nickel cobalt copper zinc arsenic selenium rubidium strontium cadmium tellurium cesium barium lanthanum thallium)
  end

  def metabolomics_polyphenols
    %w(gallic_acid gallocatchin 3_4_dihydroxybenzoic_acid catechin protocatecuic_aldehyde pungenol caffeic_acid vanillic_acid vanillin piceol taxifolin p_coumaric_acid ferulic_acid myricetin quercetin naringenin kampferol apigenin isorhamnetin)
  end

  def metabolomics_primary_non_glycerophospholipids
    %w(choline betaine creatinine tmao creatine proline_betaine zeatin proline carnosine histidine histamine methylhistidine arginine taurine total_dma trans_hydroxyproline glutamine asparagine citrulline methionine_sulfoxide serine acetylornithine glycine glutamic_acid aspartic_acid sarcosine threonine alpha_aaa dopa alanine tyrosine dopamine serotonin methonine valine tyramine ornithine kynurenine lysine nitro_tyr tryptophan xleu phenylalanine putrescine pea spermidine spermine glucose carnitine shikimic_acid glyceric_acid beta_hydroxybutyric_acid lactic_acid propionic_acid malic_acid butyric_acid hippuric_acid succinic_acid glutaric_acid fumaric_acid valeric_acid benzoic_acid oxalic_acid salicylic_acid citric_acid aconitic_acid pyruvic_acid alpha_ketoglutaric_acid)
  end

  def metabolomics_primary_glycerophospholipids
    %w(lysoc14_0 lysoc16_0 lysoc18_2 lysoc18_1 lysoc18_0 pc_aa_c36_6 pc_aa_c36_0 pc_aa_c38_6 pc_aa_c38_0 pc_aa_c40_6 pc_aa_c32_0 pc_ae_c34_2 pc_ae_c34_1 pc_aa_c34_4 pc_aa_c34_3 pc_aa_c34_2 pc_aa_c34_1 pc_ae_c36_3 pc_ae_c36_2 pc_ae_c36_1 pc_aa_c36_5 pc_aa_c36_4 pc_aa_c36_3 pc_aa_c36_2 pc_aa_c36_1 pc_ae_c38_4 pc_ae_c38_3 pc_ae_c38_2 pc_ae_c38_0 pc_aa_c38_5 pc_aa_c38_4 pc_aa_c38_3 pc_aa_c38_1 pc_ae_c40_4 pc_ae_c40_3 pc_aa_c40_5 pc_ae_c42_5)
  end

  def vwc_phases
    %w(PRE DD1 R1 DD2 R2 DD3 DD3T1 DD3T2)
  end

  def measurement_attribute_mapping
    {
      'amount_used' => 'Amount Used',
      'amount_used_unit' => 'Amount Used Unit',
      'description' => 'Description',
      'performed_by_id' => 'Measured By',
      'performed_on' => 'Date Measured',
      'performed_on_end' => 'Date Measured (End Date)',
      'protocol_id' => 'Protocol'
    }
  end

  # For history view (parse diff into human readable format)
  def parse_measurement_changeset(version)
    exclude = ['updated_at', 'last_user_updated', 'tag_list']

    if version.item_type == 'Measurement'
      changes = {}

      version.changeset.each do |key, value_array|
        # Parse value
        value = []
        value_array.each do |value_item|
          if (value_item == nil) || value_item.blank?
            value.push('---')
          elsif key == 'performed_by_id'
            value.push(User.find_by(id: value_item).name)
          elsif key == 'protocol_id'
            value.push(Protocol.find_by(id: value_item).name)
          else
            value.push(value_item)
          end
        end

        # Change key
        if !exclude.include? key 
          # Don't show changes from nil to blank or vice versa
          if value[0] != value[1]
            changes[measurement_attribute_mapping[key]] = value
          end
        end
      end

    else # MeasurementValue
      changes = version.changeset
      measurement_value = MeasurementValue.find_by(id: version.item_id)
      changes[MeasurementValue.title(measurement_value.name)] = changes.delete('value')
      changes.delete('updated_at')
    end

    return changes
  end
end
