require_dependency 'knoxy_form_builder'

module FormHelper
  def knoxy_form_for(object, *args, &block)
    options = args.extract_options!
    options[:html] ||= {}
    options[:html][:class] = 'standard' unless options[:html].has_key?(:class)

    simple_form_for(object, *(args << options.merge(builder: KnoxyFormBuilder)), &block)
  end

  def render_form_errors(entry)
    render '/shared/form_errors', entry: entry
  end
end
