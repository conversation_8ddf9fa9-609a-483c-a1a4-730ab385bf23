module SamplesHelper
  # Prepares a new sample for creation (via form) by adding the required number of nested associations
  def setup_sample(sample)
    sample.tap do |s|
      1.upto(4) do |i|
        s.stored_files.build
      end
    end
  end

  def measurable?(sample)
    MeasurementType.all.pluck('code').include? @sample.suffix
  end

  # List of suffixes for select should come from MeasurementTypes table
  # e.g. Gas Exchange (GE)
  def suffix_collection(source_code, identity)
    suffixes = []
    suffixes_hash = {}
    children_suffixes_with_measurements = ['MT', 'PP']
    children_suffixes_without_measurements = {
      'Metabolomics (PRE)' => 'MB-PRE',
      'Metabolomics (DD2)' => 'MB-DD2',
      'Metabolomics (HAR)' => 'MB-HAR',
      'Volumetric Water Content (GE) (PRE)' => 'VG-PRE',
      'Volumetric Water Content (GE) (DD1)' => 'VG-DD1',
      'Volumetric Water Content (GE) (R1)' => 'VG-R1',
      'Volumetric Water Content (GE) (DD2)' => 'VG-DD2',
      'Volumetric Water Content (GE) (R2)' => 'VG-R2',
      'Volumetric Water Content (GE) (DD3)' => 'VG-DD3',
      'Volumetric Water Content (GE) (DD3T1)' => 'VG-DD3T1',
      'Volumetric Water Content (GE) (DD3T2)' => 'VG-DD3T2'
    }

    # Put all measurement types in the hash
    MeasurementType.all.each do |measurement_type|
      if identity == 'parent'
        if !children_suffixes_with_measurements.include? measurement_type.code
          suffixes_hash[measurement_type.name] = measurement_type.code
        end
      else
        suffixes_hash[measurement_type.name] = measurement_type.code
      end
    end

    # Add non-measurement type codes (for parents)
    if identity == 'parent'
      suffixes_hash['Chemical Defenses'] = 'CD'
    # Add non-measurement type codes (for children)
    elsif identity == 'child'
      if source_code == "GH" # Greenhouse trees have phases which have sub-sample codes
        children_suffixes_without_measurements.each do |k, v|
          suffixes_hash[k] = v
        end

        # Delete parents
        suffixes_hash.delete('Metabolomics')
        suffixes_hash.delete('Volumetric Water Content (GE)')
      end
    end

    # Add to suffixes array for form
    suffixes_hash = suffixes_hash.sort_by { |name, suffix| name }
    suffixes_hash.each do |key, value|
      array = []
      array.push("#{key} (#{value})")
      array.push(value)
      suffixes.push(array)
    end
    
    return suffixes
  end

  def sample_attribute_mapping
    {
      'actual_amount' => 'Amount Remaining',
      'actual_unit' => 'Amount Remaining Unit',
      'box' => 'Box',
      'building' => 'Building',
      'collected_by_id' => 'Collected By',
      'collected_on' => 'Collected On',
      'collected_on_end' => 'Collected On (End Date)',
      'description' => 'Description',
      'freezer' => 'Freezer',
      'original_amount' => 'Amount Received',
      'original_unit' => 'Amount Received Unit',
      'room' => 'Room',
      'sample_type' => 'Sample Type',
      'site_id' => 'Lab'
    }
  end

  # For history view (parse diff into human readable format)
  def parse_sample_changeset(version)
    exclude = ['updated_at', 'last_user_updated']

    changes = {}
    version.changeset.each do |key, value_array|
      # Parse value
      value = []
      value_array.each do |value_item|
        if (value_item == nil) || value_item.blank?
          value.push('---')
        elsif key == 'collected_by_id'
          value.push(User.find_by(id: value_item).name)
        elsif key == 'site_id'
          value.push(Site.find_by(id: value_item).name)
        else
          value.push(value_item)
        end
      end

      # Change key
      if !exclude.include? key 
        # Don't show changes from nil to blank or vice versa
        if value[0] != value[1]
          changes[sample_attribute_mapping[key]] = value
        end
      end
    end
    
    return changes
  end
end
