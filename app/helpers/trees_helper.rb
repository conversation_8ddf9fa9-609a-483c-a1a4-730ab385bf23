module <PERSON><PERSON><PERSON><PERSON>
  def selected_tree_node
    cname = controller.controller_name
    aname = controller.action_name

    case cname
    when 'characteristics'
      if defined?(@characteristic)
        "tree-characteristic-#{@characteristic.id}"
      else
        'tree-characteristics'
      end
    when 'measurements'
      if defined?(@measurement)
        "tree-measurement-#{@measurement.id}"
      else
        'tree-measurements'
      end
    when 'samples'
      if defined?(@sample)
        "tree-sample-#{@sample.id}"
      else
        'tree-samples'
      end
    when 'grouping_assignments'
      "tree-groups"
    else
      "root"
    end
  end
end
