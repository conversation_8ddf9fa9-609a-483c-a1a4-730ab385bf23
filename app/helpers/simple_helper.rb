module <PERSON><PERSON><PERSON><PERSON>
  def options_for_select_collection
    # <Group Display Name> => ["<Model>, <Attribute>, <Display Name>"]
    {
      # Tree Attributes
      'Tree' => [
        'Tree, res_for_label, RES-FOR Label',
        'Tree, trial, Trial',
        'Tree, family, Family',
        'Tree, code, Unique Tree Code',
        'Tree, treatment, GH Treatment',
        'Tree, block, Block',
        'Tree, notes, Notes',
        'Tree, rep, Rep',
        'Tree, row, Row',
        'Tree, tier, Tier',
        'Tree, set, Set',
        'Tree, stake, Stake',
        'Tree, tree, Tree',
        'Tree, x_coordinate, x',
        'Tree, y_coordinate, y',
        'Tree, forward_selected, Forward Selected (Y/N)',
        'Tree, breeding_value_rank, Breeding Value Rank',
        'Tree, label_present, RES-FOR Label on Tree (Y/N)',
      ],

      # Source Attributes
      'Site' => [
        'Source, code, Site Code',
        #'Source, name, Site Name'
      ],

      # Species Attributes
      'Species' => [
        'Species, code, Species Code (P/S)',
        #'Species, name, Species Name',
      ],

      # Parent Tree/Pedigree Attributes (only mother tree has data)
      'Pedigree' => [
        'Pedigree, suc_code, SUC Code',
        'Pedigree, mother_tree, Mother Tree SUC Code',
        'Pedigree, father_tree, Father Tree SUC Code',
      ],

      'Mother Tree' => [
        'Mother, accession_number, Pedigree Corrected Family',
        'Mother, cone_collected, Parent Cone Collection Date',
        'Mother, location, Parent Location',
        'Mother, latitude, Parent Latitude (°)',
        'Mother, longitude, Parent Longitude (°)',
        'Mother, elevation, Parent Elevation (m)',
      ],

      # Characteristic Attributes
      'Characteristic' => [
        'Characteristic, height, Height (cm)',
        'Characteristic, dbh, DBH (cm)',
        'Characteristic, dbh_position, DBH Position',
        'Characteristic, height_breeding_value, Height Breeding Value (%height)',
        'Characteristic, dbh_breeding_value, DBH Breeding Value (%DBH)',
        'Characteristic, height_breeding_value_gxe, Height Breeding Value GxE (%height)',
        'Characteristic, dbh_breeding_value_gxe, DBH Breeding Value GxE (%DBH)',
        'Characteristic, white_pine_weevil_presence, WPW Presence (1) or Absence (0)',
        'Characteristic, white_pine_weevil_score_sum, WPW Sum',
        'Characteristic, western_gall_rust_six_code, WGR (Code 0-6)',
        'Characteristic, western_gall_rust_two_code, WGR (Code 0-2)',
        'Characteristic, mountain_pine_beetle_presence, MPB',
        'Characteristic, status_code, Status Code',
        'Characteristic, condition_code, Condition Code',
        'Characteristic, notes, Notes',
      ],

      # Measurement Attributes
      # 'Measurement' => [
      #   'Measurement, performed_on, Date Measured (YYYY-MM-DD)',
      # ],

      'Measurement: Biomass' => measurement_with_notes_array('BM'),
      'Measurement: Gas Exchange' => measurement_with_date_array('GE'),
      'Measurement: Growth (GH)' => simple_measurement_array('GG'),
      'Measurement: Insect Resistance' => simple_measurement_array('IR'),
      'Measurement: Lesion Length' => measurement_with_date_array('LL'),
      'Measurement: Metabolomics (Field)' => multiple_measurement_array('MB', 'FIELD'),
      'Measurement: Metabolomics (PRE)' => multiple_measurement_array('MB', 'PRE'),
      'Measurement: Metabolomics (DD2)' => multiple_measurement_array('MB', 'DD2'),
      'Measurement: Metabolomics (HAR)' => multiple_measurement_array('MB', 'HAR'),
      'Measurement: Metabolomics (Primary) (Field)' => multiple_measurement_array('MP', 'FIELD'),
      'Measurement: Metabolomics (Primary) (PRE)' => multiple_measurement_array('MP', 'PRE'),
      'Measurement: Metabolomics (Primary) (DD2)' => multiple_measurement_array('MP', 'DD2'),
      'Measurement: Metabolomics (Primary) (HAR)' => multiple_measurement_array('MP', 'HAR'),
      'Measurement: Microfibril Angle' => simple_measurement_array('MA'),
      'Measurement: Monoterpene Analysis' => measurement_with_date_array('MT'),
      'Measurement: Polyphenolic Analysis' => measurement_with_date_array('PP'),
      'Measurement: Resin Duct Analysis' => simple_measurement_array('RD'),
      'Measurement: 13C' => simple_measurement_array('TC'),
      'Measurement: Volumetric Water Content (GE) (PRE)' => multiple_measurement_array('VG', 'PRE'),
      'Measurement: Volumetric Water Content (GE) (DD1)' => multiple_measurement_array('VG', 'DD1'),
      'Measurement: Volumetric Water Content (GE) (R1)' => multiple_measurement_array('VG', 'R1'),
      'Measurement: Volumetric Water Content (GE) (DD2)' => multiple_measurement_array('VG', 'DD2'),
      'Measurement: Volumetric Water Content (GE) (R2)' => multiple_measurement_array('VG', 'R2'),
      'Measurement: Volumetric Water Content (GE) (DD3)' => multiple_measurement_array('VG', 'DD3'),
      'Measurement: Volumetric Water Content (GE) (DD3T1)' => multiple_measurement_array('VG', 'DD3T1'),
      'Measurement: Volumetric Water Content (GE) (DD3T2)' => multiple_measurement_array('VG', 'DD3T2'),
      'Measurement: Wood Quality' => simple_measurement_array('WQ')
    }
  end

  # Just measurement data for options_for_select_collection (advanced search)
  def simple_measurement_array(code)
    array = []
    
    Measurement.measurement_value_types_array(code).each do |measurement_value|
      array.push(MeasurementValue.attribute_string(measurement_value))
    end

    return array
  end

  # For measurements taken at multiple time points
  def multiple_measurement_array(code, phase)
    array = []

    Measurement.measurement_value_types_array(code).each do |measurement_value|
      if measurement_value.split("_")[-1] == phase
        array.push(MeasurementValue.attribute_string(measurement_value).gsub("-#{code}", "-#{code}-#{phase}"))
      end
    end

    return array
  end

  # Measurement data with date recorded for options_for_select_collection (advanced search)
  def measurement_with_date_array(code)
    array = []
    date_range_required = ["LL"] # Need YYYY-MM-DD-DD

    if date_range_required.include? code
      array.push("Measurement-#{code}, performed_on, #{code} Date Measured (YYYY-MM-DD-DD)")
    else
      array.push("Measurement-#{code}, performed_on, #{code} Date Measured (YYYY-MM-DD)")
    end

    Measurement.measurement_value_types_array(code).each do |measurement_value|
      array.push(MeasurementValue.attribute_string(measurement_value))
    end

    return array
  end

  # Measurement data with notes/comments for options_for_select_collection (advanced search)
  def measurement_with_notes_array(code)
    array = []
    array.push("Measurement-#{code}, description, #{code} Notes")

    Measurement.measurement_value_types_array(code).each do |measurement_value|
      array.push(MeasurementValue.attribute_string(measurement_value))
    end

    return array
  end

  # If no display fields are selected, output all
  def default_columns
    columns = []

    options_for_select_collection.each do |key, value|
      value.each do |option|
        if key == 'Species'
          columns.insert(1, option) # Insert the species right after the RES-FOR label to match master list
        elsif key == 'Site'
          columns.insert(1, option) # Insert the source right after the species to match master list
        else
          columns.push(option)
        end
      end
    end

    return columns
  end

  # Attribute String: [Model, Attribute, Display Name]

  def get_model_name(attribute_string)
    return attribute_string.split(", ")[0]
  end

  def get_attribute_name(attribute_string)
    return attribute_string.split(", ")[1]
  end

  def get_display_name(attribute_string)
    return attribute_string.split(", ")[2]
  end

  def get_measurement_type(attribute_string)
    return attribute_string.split(", ")[0].split('-')[1]
  end

  # For ransack sort on header
  #def get_model_attribute(attribute_string)
  #  return attribute_string.split(/\s*,\s*/)[0].pluralize.downcase + '_' + attribute_string.split(/\s*,\s*/)[1]
  #end

  # Modify condition code for view
  def format_code(code)
    formatted_code = ''
    
    code.each do |code|
      if code.length > 0
        formatted_code += code + ','
      end
    end

    return formatted_code.chomp(",")
  end

  # Get the status of attributes at each age to determine if they're empty or not (reduce query time)
  def get_characteristics_presence_hash(columns, ages)
    characteristics_presence_hash = {}

    columns.each do |column|
      if get_model_name(column) == 'Characteristic'
        ages.each do |age|
          characteristics_presence_hash["#{get_attribute_name(column)}_#{age}"] = Characteristic.where("age_recorded = #{age} AND #{get_attribute_name(column)} IS NOT NULL").exists?
        end
      end
    end

    return characteristics_presence_hash
  end

  # Get a list of models in the query for pre-loading
  def get_models_array(columns)
    models = []

    columns.each do |column|
      if !models.include? get_model_name(column)
        models.push(get_model_name(column))
      end
    end

    return models
  end

  # For header definitions
  def field_only_measurement_values
    return %w(vanillic_acid_FIELD)
  end
end
