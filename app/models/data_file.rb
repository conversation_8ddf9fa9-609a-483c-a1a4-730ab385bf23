class DataFile < ApplicationRecord
  belongs_to :measurement
  has_attached_file :data,
    url: "/system/:attachment/:id/:filename",
    path: ":rails_root/public/system/:attachment/:id/:filename"
  do_not_validate_attachment_file_type :data

  belongs_to :data_file_type

  validates :data_file_type, presence: true
  validates_with AttachmentPresenceValidator, attributes: :data


  def root
    measurement.root
  end

  # Allow LIKE search for dates
  ransacker :created_at do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(data_files.created_at, "%Y-%m-%d %H:%M:%S")')
  end
end
