class ParentTree < ApplicationRecord
  belongs_to :species, inverse_of: :trees
  has_many :maternal_line_child_trees, class_name: "Tree", foreign_key: "mother_parent_tree_id"
  has_many :paternal_line_child_trees, class_name: "Tree", foreign_key: "father_parent_tree_id"

  validates :suc_code, uniqueness: true
  validates_numericality_of :latitude, :longitude, allow_blank: true
  validates :elevation, numericality: {only_integer: true, greater_than_or_equal_to: 1}, allow_blank: true
  validates_format_of :cone_collected_before_type_cast,
    with: /\A((19|20)\d\d)-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])\z/,
    message: "must be in YYYY-MM-DD format", allow_blank: true

  def to_s
    suc_code.to_s
  end

  def child_trees
    Tree.where("mother_parent_tree_id = ? OR father_parent_tree_id = ?", self.id, self.id)
  end

  # for advanced search
  def self.ransackable_attributes(auth_object = nil)
    ['suc_code', 'accession_number', 'cone_collected', 'location', 'latitude', 'longitude', 'elevation']
  end

  # Allow LIKE search for integers
  ransacker :suc_code do
    Arel.sql("CONVERT(parent_trees.suc_code, CHAR(8))")
  end

  ransacker :latitude do
    Arel.sql("CONVERT(parent_trees.latitude, CHAR(8))")
  end

  ransacker :longitude do
    Arel.sql("CONVERT(parent_trees.longitude, CHAR(8))")
  end

  ransacker :elevation do
    Arel.sql("CONVERT(parent_trees.elevation, CHAR(8))")
  end

  # Allow LIKE search for dates
  ransacker :cone_collected do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(parent_trees.cone_collected, "%Y-%m-%d %H:%M:%S")')
  end
end
