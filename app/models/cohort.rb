class Cohort < ApplicationRecord
  ALLOWED_CONTROL_VALUES = [ 'Not Applicable', 'Yes', 'No']

  belongs_to :study
  has_many :cohorts_trees, dependent: :destroy
  has_many :trees, through: :cohorts_trees
  accepts_nested_attributes_for :cohorts_trees, allow_destroy: true

  validates_presence_of :label
  validates_inclusion_of :control, in: ALLOWED_CONTROL_VALUES, allow_blank: true

  accepts_nested_attributes_for :cohorts_trees, allow_destroy: true, reject_if: :all_blank
end
