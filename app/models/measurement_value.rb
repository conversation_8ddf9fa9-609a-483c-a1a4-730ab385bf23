class MeasurementValue < ApplicationRecord
  has_paper_trail on: [:update]

  TIME_ATTRIBUTES = ['time_recorded']
  DATE_ATTRIBUTES = ['date_inoculated', 'date_measured_ge_PRE', 'date_measured_ge_DD1', 'date_measured_ge_R1', 'date_measured_ge_DD2', 'date_measured_ge_R2', 'date_measured_ge_DD3', 'date_measured_ge_DD3T1', 'date_measured_ge_DD3T2']
  STRING_ATTRIBUTES = ['phase_ge', 'lithium_FIELD', 'boron_FIELD', 'sodium_FIELD', 'magnesium_FIELD', 'aluminum_FIELD', 'phosphorus_FIELD', 'potassium_FIELD', 'calcium_FIELD', 'vanadium_FIELD', 'manganese_FIELD', 'iron_FIELD', 'nickel_FIELD', 'cobalt_FIELD', 'copper_FIELD', 'zinc_FIELD', 'arsenic_FIELD', 'selenium_FIELD', 'rubidium_FIELD', 'strontium_FIELD', 'cadmium_FIELD', 'tellurium_FIELD', 'cesium_FIELD', 'barium_FIELD', 'lanthanum_FIELD', 'thallium_FIELD', 'gallic_acid_FIELD', 'gallocatchin_FIELD', '3_4_dihydroxybenzoic_acid_FIELD', 'catechin_FIELD', 'protocatecuic_aldehyde_FIELD', 'pungenol_FIELD', 'caffeic_acid_FIELD', 'vanillic_acid_FIELD', 'vanillin_FIELD', 'piceol_FIELD', 'taxifolin_FIELD', 'p_coumaric_acid_FIELD', 'ferulic_acid_FIELD', 'myricetin_FIELD', 'quercetin_FIELD', 'naringenin_FIELD', 'kampferol_FIELD', 'apigenin_FIELD', 'isorhamnetin_FIELD', 'lithium_PRE', 'boron_PRE', 'sodium_PRE', 'magnesium_PRE', 'aluminum_PRE', 'phosphorus_PRE', 'potassium_PRE', 'calcium_PRE', 'vanadium_PRE', 'manganese_PRE', 'iron_PRE', 'nickel_PRE', 'cobalt_PRE', 'copper_PRE', 'zinc_PRE', 'arsenic_PRE', 'selenium_PRE', 'rubidium_PRE', 'strontium_PRE', 'cadmium_PRE', 'tellurium_PRE', 'cesium_PRE', 'barium_PRE', 'lanthanum_PRE', 'thallium_PRE', 'gallic_acid_PRE', 'gallocatchin_PRE', '3_4_dihydroxybenzoic_acid_PRE', 'catechin_PRE', 'protocatecuic_aldehyde_PRE', 'pungenol_PRE', 'caffeic_acid_PRE', 'vanillin_PRE', 'piceol_PRE', 'taxifolin_PRE', 'p_coumaric_acid_PRE', 'ferulic_acid_PRE', 'myricetin_PRE', 'quercetin_PRE', 'naringenin_PRE', 'kampferol_PRE', 'apigenin_PRE', 'isorhamnetin_PRE', 'lithium_DD2', 'boron_DD2', 'sodium_DD2', 'magnesium_DD2', 'aluminum_DD2', 'phosphorus_DD2', 'potassium_DD2', 'calcium_DD2', 'vanadium_DD2', 'manganese_DD2', 'iron_DD2', 'nickel_DD2', 'cobalt_DD2', 'copper_DD2', 'zinc_DD2', 'arsenic_DD2', 'selenium_DD2', 'rubidium_DD2', 'strontium_DD2', 'cadmium_DD2', 'tellurium_DD2', 'cesium_DD2', 'barium_DD2', 'lanthanum_DD2', 'thallium_DD2', 'gallic_acid_DD2', 'gallocatchin_DD2', '3_4_dihydroxybenzoic_acid_DD2', 'catechin_DD2', 'protocatecuic_aldehyde_DD2', 'pungenol_DD2', 'caffeic_acid_DD2', 'vanillin_DD2', 'piceol_DD2', 'taxifolin_DD2', 'p_coumaric_acid_DD2', 'ferulic_acid_DD2', 'myricetin_DD2', 'quercetin_DD2', 'naringenin_DD2', 'kampferol_DD2', 'apigenin_DD2', 'isorhamnetin_DD2', 'lithium_HAR', 'boron_HAR', 'sodium_HAR', 'magnesium_HAR', 'aluminum_HAR', 'phosphorus_HAR', 'potassium_HAR', 'calcium_HAR', 'vanadium_HAR', 'manganese_HAR', 'iron_HAR', 'nickel_HAR', 'cobalt_HAR', 'copper_HAR', 'zinc_HAR', 'arsenic_HAR', 'selenium_HAR', 'rubidium_HAR', 'strontium_HAR', 'cadmium_HAR', 'tellurium_HAR', 'cesium_HAR', 'barium_HAR', 'lanthanum_HAR', 'thallium_HAR', 'gallic_acid_HAR', 'gallocatchin_HAR', '3_4_dihydroxybenzoic_acid_HAR', 'catechin_HAR', 'protocatecuic_aldehyde_HAR', 'pungenol_HAR', 'caffeic_acid_HAR', 'vanillin_HAR', 'piceol_HAR', 'taxifolin_HAR', 'p_coumaric_acid_HAR', 'ferulic_acid_HAR', 'myricetin_HAR', 'quercetin_HAR', 'naringenin_HAR', 'kampferol_HAR', 'apigenin_HAR', 'isorhamnetin_HAR', 'measured_by_david_love_PRE', 'sensor_ge_PRE', 'measured_by_david_love_DD1', 'sensor_ge_DD1', 'measured_by_david_love_R1', 'sensor_ge_R1', 'measured_by_david_love_DD2', 'sensor_ge_DD2', 'measured_by_david_love_R2', 'sensor_ge_R2', 'measured_by_david_love_DD3', 'sensor_ge_DD3', 'measured_by_david_love_DD3T1', 'sensor_ge_DD3T1', 'measured_by_david_love_DD3T2', 'sensor_ge_DD3T2', 'choline_FIELD', 'betaine_FIELD', 'creatinine_FIELD', 'tmao_FIELD', 'creatine_FIELD', 'proline_betaine_FIELD', 'zeatin_FIELD', 'proline_FIELD', 'carnosine_FIELD', 'histidine_FIELD', 'histamine_FIELD', 'methylhistidine_FIELD', 'arginine_FIELD', 'taurine_FIELD', 'total_dma_FIELD', 'trans_hydroxyproline_FIELD', 'glutamine_FIELD', 'asparagine_FIELD', 'citrulline_FIELD', 'methionine_sulfoxide_FIELD', 'serine_FIELD', 'acetylornithine_FIELD', 'glycine_FIELD', 'glutamic_acid_FIELD', 'aspartic_acid_FIELD', 'sarcosine_FIELD', 'threonine_FIELD', 'alpha_aaa_FIELD', 'dopa_FIELD', 'alanine_FIELD', 'tyrosine_FIELD', 'dopamine_FIELD', 'serotonin_FIELD', 'methonine_FIELD', 'valine_FIELD', 'tyramine_FIELD', 'ornithine_FIELD', 'kynurenine_FIELD', 'lysine_FIELD', 'nitro_tyr_FIELD', 'tryptophan_FIELD', 'xleu_FIELD', 'phenylalanine_FIELD', 'putrescine_FIELD', 'pea_FIELD', 'spermidine_FIELD', 'spermine_FIELD', 'glucose_FIELD', 'carnitine_FIELD', 'lysoc14_0_FIELD', 'lysoc16_0_FIELD', 'lysoc18_2_FIELD', 'lysoc18_1_FIELD', 'lysoc18_0_FIELD', 'pc_aa_c36_6_FIELD', 'pc_aa_c36_0_FIELD', 'pc_aa_c38_6_FIELD', 'pc_aa_c38_0_FIELD', 'pc_aa_c40_6_FIELD', 'pc_aa_c32_0_FIELD', 'pc_ae_c34_2_FIELD', 'pc_ae_c34_1_FIELD', 'pc_aa_c34_4_FIELD', 'pc_aa_c34_3_FIELD', 'pc_aa_c34_2_FIELD', 'pc_aa_c34_1_FIELD', 'pc_ae_c36_3_FIELD', 'pc_ae_c36_2_FIELD', 'pc_ae_c36_1_FIELD', 'pc_aa_c36_5_FIELD', 'pc_aa_c36_4_FIELD', 'pc_aa_c36_3_FIELD', 'pc_aa_c36_2_FIELD', 'pc_aa_c36_1_FIELD', 'pc_ae_c38_4_FIELD', 'pc_ae_c38_3_FIELD', 'pc_ae_c38_2_FIELD', 'pc_ae_c38_0_FIELD', 'pc_aa_c38_5_FIELD', 'pc_aa_c38_4_FIELD', 'pc_aa_c38_3_FIELD', 'pc_aa_c38_1_FIELD', 'pc_ae_c40_4_FIELD', 'pc_ae_c40_3_FIELD', 'pc_aa_c40_5_FIELD', 'pc_ae_c42_5_FIELD', 'shikimic_acid_FIELD', 'glyceric_acid_FIELD', 'beta_hydroxybutyric_acid_FIELD', 'lactic_acid_FIELD', 'propionic_acid_FIELD', 'malic_acid_FIELD', 'butyric_acid_FIELD', 'hippuric_acid_FIELD', 'succinic_acid_FIELD', 'glutaric_acid_FIELD', 'fumaric_acid_FIELD', 'valeric_acid_FIELD', 'benzoic_acid_FIELD', 'oxalic_acid_FIELD', 'salicylic_acid_FIELD', 'citric_acid_FIELD', 'aconitic_acid_FIELD', 'pyruvic_acid_FIELD', 'alpha_ketoglutaric_acid_FIELD', 'choline_PRE', 'betaine_PRE', 'creatinine_PRE', 'tmao_PRE', 'creatine_PRE', 'proline_betaine_PRE', 'zeatin_PRE', 'proline_PRE', 'carnosine_PRE', 'histidine_PRE', 'histamine_PRE', 'methylhistidine_PRE', 'arginine_PRE', 'taurine_PRE', 'total_dma_PRE', 'trans_hydroxyproline_PRE', 'glutamine_PRE', 'asparagine_PRE', 'citrulline_PRE', 'methionine_sulfoxide_PRE', 'serine_PRE', 'acetylornithine_PRE', 'glycine_PRE', 'glutamic_acid_PRE', 'aspartic_acid_PRE', 'sarcosine_PRE', 'threonine_PRE', 'alpha_aaa_PRE', 'dopa_PRE', 'alanine_PRE', 'tyrosine_PRE', 'dopamine_PRE', 'serotonin_PRE', 'methonine_PRE', 'valine_PRE', 'tyramine_PRE', 'ornithine_PRE', 'kynurenine_PRE', 'lysine_PRE', 'nitro_tyr_PRE', 'tryptophan_PRE', 'xleu_PRE', 'phenylalanine_PRE', 'putrescine_PRE', 'pea_PRE', 'spermidine_PRE', 'spermine_PRE', 'glucose_PRE', 'carnitine_PRE', 'lysoc14_0_PRE', 'lysoc16_0_PRE', 'lysoc18_2_PRE', 'lysoc18_1_PRE', 'lysoc18_0_PRE', 'pc_aa_c36_6_PRE', 'pc_aa_c36_0_PRE', 'pc_aa_c38_6_PRE', 'pc_aa_c38_0_PRE', 'pc_aa_c40_6_PRE', 'pc_aa_c32_0_PRE', 'pc_ae_c34_2_PRE', 'pc_ae_c34_1_PRE', 'pc_aa_c34_4_PRE', 'pc_aa_c34_3_PRE', 'pc_aa_c34_2_PRE', 'pc_aa_c34_1_PRE', 'pc_ae_c36_3_PRE', 'pc_ae_c36_2_PRE', 'pc_ae_c36_1_PRE', 'pc_aa_c36_5_PRE', 'pc_aa_c36_4_PRE', 'pc_aa_c36_3_PRE', 'pc_aa_c36_2_PRE', 'pc_aa_c36_1_PRE', 'pc_ae_c38_4_PRE', 'pc_ae_c38_3_PRE', 'pc_ae_c38_2_PRE', 'pc_ae_c38_0_PRE', 'pc_aa_c38_5_PRE', 'pc_aa_c38_4_PRE', 'pc_aa_c38_3_PRE', 'pc_aa_c38_1_PRE', 'pc_ae_c40_4_PRE', 'pc_ae_c40_3_PRE', 'pc_aa_c40_5_PRE', 'pc_ae_c42_5_PRE', 'shikimic_acid_PRE', 'glyceric_acid_PRE', 'beta_hydroxybutyric_acid_PRE', 'lactic_acid_PRE', 'propionic_acid_PRE', 'malic_acid_PRE', 'butyric_acid_PRE', 'hippuric_acid_PRE', 'succinic_acid_PRE', 'glutaric_acid_PRE', 'fumaric_acid_PRE', 'valeric_acid_PRE', 'benzoic_acid_PRE', 'oxalic_acid_PRE', 'salicylic_acid_PRE', 'citric_acid_PRE', 'aconitic_acid_PRE', 'pyruvic_acid_PRE', 'alpha_ketoglutaric_acid_PRE', 'choline_DD2', 'betaine_DD2', 'creatinine_DD2', 'tmao_DD2', 'creatine_DD2', 'proline_betaine_DD2', 'zeatin_DD2', 'proline_DD2', 'carnosine_DD2', 'histidine_DD2', 'histamine_DD2', 'methylhistidine_DD2', 'arginine_DD2', 'taurine_DD2', 'total_dma_DD2', 'trans_hydroxyproline_DD2', 'glutamine_DD2', 'asparagine_DD2', 'citrulline_DD2', 'methionine_sulfoxide_DD2', 'serine_DD2', 'acetylornithine_DD2', 'glycine_DD2', 'glutamic_acid_DD2', 'aspartic_acid_DD2', 'sarcosine_DD2', 'threonine_DD2', 'alpha_aaa_DD2', 'dopa_DD2', 'alanine_DD2', 'tyrosine_DD2', 'dopamine_DD2', 'serotonin_DD2', 'methonine_DD2', 'valine_DD2', 'tyramine_DD2', 'ornithine_DD2', 'kynurenine_DD2', 'lysine_DD2', 'nitro_tyr_DD2', 'tryptophan_DD2', 'xleu_DD2', 'phenylalanine_DD2', 'putrescine_DD2', 'pea_DD2', 'spermidine_DD2', 'spermine_DD2', 'glucose_DD2', 'carnitine_DD2', 'lysoc14_0_DD2', 'lysoc16_0_DD2', 'lysoc18_2_DD2', 'lysoc18_1_DD2', 'lysoc18_0_DD2', 'pc_aa_c36_6_DD2', 'pc_aa_c36_0_DD2', 'pc_aa_c38_6_DD2', 'pc_aa_c38_0_DD2', 'pc_aa_c40_6_DD2', 'pc_aa_c32_0_DD2', 'pc_ae_c34_2_DD2', 'pc_ae_c34_1_DD2', 'pc_aa_c34_4_DD2', 'pc_aa_c34_3_DD2', 'pc_aa_c34_2_DD2', 'pc_aa_c34_1_DD2', 'pc_ae_c36_3_DD2', 'pc_ae_c36_2_DD2', 'pc_ae_c36_1_DD2', 'pc_aa_c36_5_DD2', 'pc_aa_c36_4_DD2', 'pc_aa_c36_3_DD2', 'pc_aa_c36_2_DD2', 'pc_aa_c36_1_DD2', 'pc_ae_c38_4_DD2', 'pc_ae_c38_3_DD2', 'pc_ae_c38_2_DD2', 'pc_ae_c38_0_DD2', 'pc_aa_c38_5_DD2', 'pc_aa_c38_4_DD2', 'pc_aa_c38_3_DD2', 'pc_aa_c38_1_DD2', 'pc_ae_c40_4_DD2', 'pc_ae_c40_3_DD2', 'pc_aa_c40_5_DD2', 'pc_ae_c42_5_DD2', 'shikimic_acid_DD2', 'glyceric_acid_DD2', 'beta_hydroxybutyric_acid_DD2', 'lactic_acid_DD2', 'propionic_acid_DD2', 'malic_acid_DD2', 'butyric_acid_DD2', 'hippuric_acid_DD2', 'succinic_acid_DD2', 'glutaric_acid_DD2', 'fumaric_acid_DD2', 'valeric_acid_DD2', 'benzoic_acid_DD2', 'oxalic_acid_DD2', 'salicylic_acid_DD2', 'citric_acid_DD2', 'aconitic_acid_DD2', 'pyruvic_acid_DD2', 'alpha_ketoglutaric_acid_DD2', 'choline_HAR', 'betaine_HAR', 'creatinine_HAR', 'tmao_HAR', 'creatine_HAR', 'proline_betaine_HAR', 'zeatin_HAR', 'proline_HAR', 'carnosine_HAR', 'histidine_HAR', 'histamine_HAR', 'methylhistidine_HAR', 'arginine_HAR', 'taurine_HAR', 'total_dma_HAR', 'trans_hydroxyproline_HAR', 'glutamine_HAR', 'asparagine_HAR', 'citrulline_HAR', 'methionine_sulfoxide_HAR', 'serine_HAR', 'acetylornithine_HAR', 'glycine_HAR', 'glutamic_acid_HAR', 'aspartic_acid_HAR', 'sarcosine_HAR', 'threonine_HAR', 'alpha_aaa_HAR', 'dopa_HAR', 'alanine_HAR', 'tyrosine_HAR', 'dopamine_HAR', 'serotonin_HAR', 'methonine_HAR', 'valine_HAR', 'tyramine_HAR', 'ornithine_HAR', 'kynurenine_HAR', 'lysine_HAR', 'nitro_tyr_HAR', 'tryptophan_HAR', 'xleu_HAR', 'phenylalanine_HAR', 'putrescine_HAR', 'pea_HAR', 'spermidine_HAR', 'spermine_HAR', 'glucose_HAR', 'carnitine_HAR', 'lysoc14_0_HAR', 'lysoc16_0_HAR', 'lysoc18_2_HAR', 'lysoc18_1_HAR', 'lysoc18_0_HAR', 'pc_aa_c36_6_HAR', 'pc_aa_c36_0_HAR', 'pc_aa_c38_6_HAR', 'pc_aa_c38_0_HAR', 'pc_aa_c40_6_HAR', 'pc_aa_c32_0_HAR', 'pc_ae_c34_2_HAR', 'pc_ae_c34_1_HAR', 'pc_aa_c34_4_HAR', 'pc_aa_c34_3_HAR', 'pc_aa_c34_2_HAR', 'pc_aa_c34_1_HAR', 'pc_ae_c36_3_HAR', 'pc_ae_c36_2_HAR', 'pc_ae_c36_1_HAR', 'pc_aa_c36_5_HAR', 'pc_aa_c36_4_HAR', 'pc_aa_c36_3_HAR', 'pc_aa_c36_2_HAR', 'pc_aa_c36_1_HAR', 'pc_ae_c38_4_HAR', 'pc_ae_c38_3_HAR', 'pc_ae_c38_2_HAR', 'pc_ae_c38_0_HAR', 'pc_aa_c38_5_HAR', 'pc_aa_c38_4_HAR', 'pc_aa_c38_3_HAR', 'pc_aa_c38_1_HAR', 'pc_ae_c40_4_HAR', 'pc_ae_c40_3_HAR', 'pc_aa_c40_5_HAR', 'pc_ae_c42_5_HAR', 'shikimic_acid_HAR', 'glyceric_acid_HAR', 'beta_hydroxybutyric_acid_HAR', 'lactic_acid_HAR', 'propionic_acid_HAR', 'malic_acid_HAR', 'butyric_acid_HAR', 'hippuric_acid_HAR', 'succinic_acid_HAR', 'glutaric_acid_HAR', 'fumaric_acid_HAR', 'valeric_acid_HAR', 'benzoic_acid_HAR', 'oxalic_acid_HAR', 'salicylic_acid_HAR', 'citric_acid_HAR', 'aconitic_acid_HAR', 'pyruvic_acid_HAR', 'alpha_ketoglutaric_acid_HAR']

  # key = database name of measurement
  # value = [measurement type code, header (from researcher), full title (cleaned for others), unit]
  MV_MASTER = {
    # Biomass
    'needle_ww' => ['BM', 'Needle wet weight', 'Needle Wet Weight', 'g'],
    'stem_ww' => ['BM', 'Stem wet weight', 'Stem Wet Weight', 'g'],
    'root_ww' => ['BM', 'Root wet weight', 'Root Wet Weight', 'g'],
    'above_ground_ww' => ['BM', 'Total above-ground wet biomass', 'Total Above-Ground Biomass Wet Weight', 'g'],
    'total_biomass_ww' => ['BM', 'Total wet biomass', 'Total Biomass Wet Weight', 'g'],
    'needle_dw' => ['BM', 'Needle dry weight', 'Needle Dry Weight', 'g'],
    'stem_dw' => ['BM', 'Stem dry weight', 'Stem Dry Weight', 'g'],
    'root_dw' => ['BM', 'Root dry weight', 'Root Dry Weight', 'g'],
    'above_ground_dw' => ['BM', 'Total above-ground dry biomass', 'Total Above-Ground Biomass Dry Weight', 'g'],
    'total_biomass_dw' => ['BM', 'Total dry biomass', 'Total Biomass Dry Weight', 'g'],

    # Gas Exchange
    'phase_ge' => ['GE', 'Phase', 'Phase', nil],
    'time_recorded' => ['GE', 'Time Recorded', 'Time Recorded', 'HH:MM:SS'],
    'instrument_num' => ['GE', 'Instrument', 'Instrument Number', nil],
    'photosynthetic_rate' => ['GE', 'A', 'Photosynthetic Rate', 'μmol CO2 m-2 s-1'],
    'stomatal_conductance' => ['GE', 'gs', 'Stomatal Conductance', 'mol H2O m-2 s-1'],
    'transpiration_rate' => ['GE', 'Trmmol', 'Transpiration Rate', 'mmol H2O m-2 s-1'],
    'intercellular_co2_conc' => ['GE', 'Ci', 'Intercellular CO2 Concentration', 'μmol CO2 mol air-1'],
    'co2_ratio' => ['GE', 'Ci/Ca', 'Intercellular:Ambient CO2 Concentration Ratio', nil],
    'intrinsic_water_use_efficiency' => ['GE', 'WUEint', 'Intrinsic Water Use Efficiency', 'mol CO2 mol H2O-1'],
    'vapour_pressure_defecit' => ['GE', 'VpdL', 'Vapor Pressure Deficit', 'kPa'],
    'relative_humidity' => ['GE', 'RH_R', 'Reference Relative Humidity', nil],
    'leaf_temperature' => ['GE', 'Tleaf', 'Air Temperature Below Needles', '°C'],

    # Growth (GH)
    'height_pre1' => ['GG', 'Height-PRE1', 'Height-PRE1', 'cm'],
    'height_pre2' => ['GG', 'Height-PRE2', 'Height-PRE2', 'cm'],
    'height_r1' => ['GG', 'Height-R1', 'Height-R1', 'cm'],
    'height_r2' => ['GG', 'Height-R2', 'Height-R2', 'cm'],
    'height_f' => ['GG', 'Height-F ', 'Height-F ', 'cm'],
    'rcd_pre1' => ['GG', 'RCD-PRE1', 'RCD-PRE1', 'mm'],
    'rcd_pre2' => ['GG', 'RCD-PRE2', 'RCD-PRE2', 'mm'],
    'rcd_r1' => ['GG', 'RCD-R1', 'RCD-R1', 'mm'],
    'rcd_r2' => ['GG', 'RCD-R2', 'RCD-R2', 'mm'],
    'rcd_f' => ['GG', 'RCD-F', 'RCD-F', 'mm'],

    # Insect Resistance
    'limonene_percentage' => ['IR', 'p_limo_wobphel', 'Limonene Proportion of Total Monoterpenes (without beta-Phellandrene)', nil],
    'mpb_resistance_rank' => ['IR', 'MPB_rank_resistant', 'MPB Resistance Rank', nil],

    # Lesion Length
    'date_inoculated' => ['LL', 'date_inoculated', 'Date Inoculated', 'YYYY-MM-DD'],
    'length_at_2' => ['LL', 'Lesion Length (mm) at 2 cm', 'Lesion Length (mm) at 2 cm', nil],
    'length_at_7' => ['LL', 'Lesion Length (mm) at 7 cm', 'Lesion Length (mm) at 7 cm', nil],
    'length_at_12' => ['LL', 'Lesion Length (mm) at 12 cm', 'Lesion Length (mm) at 12 cm', nil],
    'avg_length' => ['LL', 'Average lesion length', 'Average Lesion Length', 'mm'],

    # Microfibril Angle
    'microfibril_angle' => ['MA', 'Microfibril Angle (MFA)', 'Microfibril Angle (MFA)', '°'],

    # Metabolomics (Metals/Chemical Elements and Polyphenols)
    'lithium_FIELD' => ['MB', 'Li (Field)', 'Lithium Dry Weight', 'mg/kg'],
    'boron_FIELD' => ['MB', 'B (Field)', 'Boron Dry Weight', 'mg/kg'],
    'sodium_FIELD' => ['MB', 'Na (Field)', 'Sodium Dry Weight', 'mg/kg'],
    'magnesium_FIELD' => ['MB', 'Mg (Field)', 'Magnesium Dry Weight', 'mg/kg'],
    'aluminum_FIELD' => ['MB', 'Al (Field)', 'Aluminum Dry Weight', 'mg/kg'],
    'phosphorus_FIELD' => ['MB', 'P (Field)', 'Phosphorus Dry Weight', 'mg/kg'],
    'potassium_FIELD' => ['MB', 'K (Field)', 'Potassium Dry Weight', 'mg/kg'],
    'calcium_FIELD' => ['MB', 'Ca (Field)', 'Calcium Dry Weight', 'mg/kg'],
    'vanadium_FIELD' => ['MB', 'V (Field)', 'Vanadium Dry Weight', 'mg/kg'],
    'manganese_FIELD' => ['MB', 'Mn (Field)', 'Manganese Dry Weight', 'mg/kg'],
    'iron_FIELD' => ['MB', 'Fe (Field)', 'Iron Dry Weight', 'mg/kg'],
    'nickel_FIELD' => ['MB', 'Ni (Field)', 'Nickel Dry Weight', 'mg/kg'],
    'cobalt_FIELD' => ['MB', 'Co (Field)', 'Cobalt Dry Weight', 'mg/kg'],
    'copper_FIELD' => ['MB', 'Cu (Field)', 'Copper Dry Weight', 'mg/kg'],
    'zinc_FIELD' => ['MB', 'Zn (Field)', 'Zinc Dry Weight', 'mg/kg'],
    'arsenic_FIELD' => ['MB', 'As (Field)', 'Arsenic Dry Weight', 'mg/kg'],
    'selenium_FIELD' => ['MB', 'Se (Field)', 'Selenium Dry Weight', 'mg/kg'],
    'rubidium_FIELD' => ['MB', 'Rb (Field)', 'Rubidium Dry Weight', 'mg/kg'],
    'strontium_FIELD' => ['MB', 'Sr (Field)', 'Strontium Dry Weight', 'mg/kg'],
    'cadmium_FIELD' => ['MB', 'Cd (Field)', 'Cadmium Dry Weight', 'mg/kg'],
    'tellurium_FIELD' => ['MB', 'Te (Field)', 'Tellurium Dry Weight', 'mg/kg'],
    'cesium_FIELD' => ['MB', 'Cs (Field)', 'Cesium Dry Weight', 'mg/kg'],
    'barium_FIELD' => ['MB', 'Ba (Field)', 'Barium Dry Weight', 'mg/kg'],
    'lanthanum_FIELD' => ['MB', 'La (Field)', 'Lanthanum Dry Weight', 'mg/kg'],
    'thallium_FIELD' => ['MB', 'Tl (Field)', 'Thallium Dry Weight', 'mg/kg'],
    'gallic_acid_FIELD' => ['MB', 'Gallic acid (Field)', 'Gallic Acid Dry Weight', 'mg/kg'],
    'gallocatchin_FIELD' => ['MB', 'Gallocatchin (Field)', 'Gallocatchin Dry Weight', 'mg/kg'],
    '3_4_dihydroxybenzoic_acid_FIELD' => ['MB', '3,4-Dihydroxybenzoic acid (Field)', '3,4-Dihydroxybenzoic Acid Dry Weight', 'mg/kg'],
    'catechin_FIELD' => ['MB', 'Catechin (Field)', 'Catechin Dry Weight', 'mg/kg'],
    'protocatecuic_aldehyde_FIELD' => ['MB', 'Protocatecuic aldehyde (Field)', 'Protocatecuic Aldehyde Dry Weight', 'mg/kg'],
    'pungenol_FIELD' => ['MB', 'Pungenol (Field)', 'Pungenol Dry Weight', 'mg/kg'],
    'caffeic_acid_FIELD' => ['MB', 'Caffeic acid (Field)', 'Caffeic Acid Dry Weight', 'mg/kg'],
    'vanillic_acid_FIELD' => ['MB', 'Vanillic acid (Field)', 'Vanillic Acid Dry Weight', 'mg/kg'],
    'vanillin_FIELD' => ['MB', 'Vanillin (Field)', 'Vanillin Dry Weight', 'mg/kg'],
    'piceol_FIELD' => ['MB', 'Piceol (Field)', 'Piceol Dry Weight', 'mg/kg'],
    'taxifolin_FIELD' => ['MB', 'Taxifolin (Field)', 'Taxifolin Dry Weight', 'mg/kg'],
    'p_coumaric_acid_FIELD' => ['MB', 'p-Coumaric acid (Field)', 'p-Coumaric Acid Dry Weight', 'mg/kg'],
    'ferulic_acid_FIELD' => ['MB', 'Ferulic acid (Field)', 'Ferulic Acid Dry Weight', 'mg/kg'],
    'myricetin_FIELD' => ['MB', 'Myricetin (Field)', 'Myricetin Dry Weight', 'mg/kg'],
    'quercetin_FIELD' => ['MB', 'Quercetin (Field)', 'Quercetin Dry Weight', 'mg/kg'],
    'naringenin_FIELD' => ['MB', 'Naringenin (Field)', 'Naringenin Dry Weight', 'mg/kg'],
    'kampferol_FIELD' => ['MB', 'Kampferol (Field)', 'Kampferol Dry Weight', 'mg/kg'],
    'apigenin_FIELD' => ['MB', 'Apigenin (Field)', 'Apigenin Dry Weight', 'mg/kg'],
    'isorhamnetin_FIELD' => ['MB', 'Isorhamnetin (Field)', 'Isorhamnetin Dry Weight', 'mg/kg'],

    'lithium_PRE' => ['MB', 'Li (PRE)', 'Lithium Dry Weight', 'mg/kg'],
    'boron_PRE' => ['MB', 'B (PRE)', 'Boron Dry Weight', 'mg/kg'],
    'sodium_PRE' => ['MB', 'Na (PRE)', 'Sodium Dry Weight', 'mg/kg'],
    'magnesium_PRE' => ['MB', 'Mg (PRE)', 'Magnesium Dry Weight', 'mg/kg'],
    'aluminum_PRE' => ['MB', 'Al (PRE)', 'Aluminum Dry Weight', 'mg/kg'],
    'phosphorus_PRE' => ['MB', 'P (PRE)', 'Phosphorus Dry Weight', 'mg/kg'],
    'potassium_PRE' => ['MB', 'K (PRE)', 'Potassium Dry Weight', 'mg/kg'],
    'calcium_PRE' => ['MB', 'Ca (PRE)', 'Calcium Dry Weight', 'mg/kg'],
    'vanadium_PRE' => ['MB', 'V (PRE)', 'Vanadium Dry Weight', 'mg/kg'],
    'manganese_PRE' => ['MB', 'Mn (PRE)', 'Manganese Dry Weight', 'mg/kg'],
    'iron_PRE' => ['MB', 'Fe (PRE)', 'Iron Dry Weight', 'mg/kg'],
    'nickel_PRE' => ['MB', 'Ni (PRE)', 'Nickel Dry Weight', 'mg/kg'],
    'cobalt_PRE' => ['MB', 'Co (PRE)', 'Cobalt Dry Weight', 'mg/kg'],
    'copper_PRE' => ['MB', 'Cu (PRE)', 'Copper Dry Weight', 'mg/kg'],
    'zinc_PRE' => ['MB', 'Zn (PRE)', 'Zinc Dry Weight', 'mg/kg'],
    'arsenic_PRE' => ['MB', 'As (PRE)', 'Arsenic Dry Weight', 'mg/kg'],
    'selenium_PRE' => ['MB', 'Se (PRE)', 'Selenium Dry Weight', 'mg/kg'],
    'rubidium_PRE' => ['MB', 'Rb (PRE)', 'Rubidium Dry Weight', 'mg/kg'],
    'strontium_PRE' => ['MB', 'Sr (PRE)', 'Strontium Dry Weight', 'mg/kg'],
    'cadmium_PRE' => ['MB', 'Cd (PRE)', 'Cadmium Dry Weight', 'mg/kg'],
    'tellurium_PRE' => ['MB', 'Te (PRE)', 'Tellurium Dry Weight', 'mg/kg'],
    'cesium_PRE' => ['MB', 'Cs (PRE)', 'Cesium Dry Weight', 'mg/kg'],
    'barium_PRE' => ['MB', 'Ba (PRE)', 'Barium Dry Weight', 'mg/kg'],
    'lanthanum_PRE' => ['MB', 'La (PRE)', 'Lanthanum Dry Weight', 'mg/kg'],
    'thallium_PRE' => ['MB', 'Tl (PRE)', 'Thallium Dry Weight', 'mg/kg'],
    'gallic_acid_PRE' => ['MB', 'Gallic acid (PRE)', 'Gallic Acid Dry Weight', 'mg/kg'],
    'gallocatchin_PRE' => ['MB', 'Gallocatchin (PRE)', 'Gallocatchin Dry Weight', 'mg/kg'],
    '3_4_dihydroxybenzoic_acid_PRE' => ['MB', '3,4-Dihydroxybenzoic acid (PRE)', '3,4-Dihydroxybenzoic Acid Dry Weight', 'mg/kg'],
    'catechin_PRE' => ['MB', 'Catechin (PRE)', 'Catechin Dry Weight', 'mg/kg'],
    'protocatecuic_aldehyde_PRE' => ['MB', 'Protocatecuic aldehyde (PRE)', 'Protocatecuic Aldehyde Dry Weight', 'mg/kg'],
    'pungenol_PRE' => ['MB', 'Pungenol (PRE)', 'Pungenol Dry Weight', 'mg/kg'],
    'caffeic_acid_PRE' => ['MB', 'Caffeic acid (PRE)', 'Caffeic Acid Dry Weight', 'mg/kg'],
    'vanillin_PRE' => ['MB', 'Vanillin (PRE)', 'Vanillin Dry Weight', 'mg/kg'],
    'piceol_PRE' => ['MB', 'Piceol (PRE)', 'Piceol Dry Weight', 'mg/kg'],
    'taxifolin_PRE' => ['MB', 'Taxifolin (PRE)', 'Taxifolin Dry Weight', 'mg/kg'],
    'p_coumaric_acid_PRE' => ['MB', 'p-Coumaric acid (PRE)', 'p-Coumaric Acid Dry Weight', 'mg/kg'],
    'ferulic_acid_PRE' => ['MB', 'Ferulic acid (PRE)', 'Ferulic Acid Dry Weight', 'mg/kg'],
    'myricetin_PRE' => ['MB', 'Myricetin (PRE)', 'Myricetin Dry Weight', 'mg/kg'],
    'quercetin_PRE' => ['MB', 'Quercetin (PRE)', 'Quercetin Dry Weight', 'mg/kg'],
    'naringenin_PRE' => ['MB', 'Naringenin (PRE)', 'Naringenin Dry Weight', 'mg/kg'],
    'kampferol_PRE' => ['MB', 'Kampferol (PRE)', 'Kampferol Dry Weight', 'mg/kg'],
    'apigenin_PRE' => ['MB', 'Apigenin (PRE)', 'Apigenin Dry Weight', 'mg/kg'],
    'isorhamnetin_PRE' => ['MB', 'Isorhamnetin (PRE)', 'Isorhamnetin Dry Weight', 'mg/kg'],

    'lithium_DD2' => ['MB', 'Li (DD2)', 'Lithium Dry Weight', 'mg/kg'],
    'boron_DD2' => ['MB', 'B (DD2)', 'Boron Dry Weight', 'mg/kg'],
    'sodium_DD2' => ['MB', 'Na (DD2)', 'Sodium Dry Weight', 'mg/kg'],
    'magnesium_DD2' => ['MB', 'Mg (DD2)', 'Magnesium Dry Weight', 'mg/kg'],
    'aluminum_DD2' => ['MB', 'Al (DD2)', 'Aluminum Dry Weight', 'mg/kg'],
    'phosphorus_DD2' => ['MB', 'P (DD2)', 'Phosphorus Dry Weight', 'mg/kg'],
    'potassium_DD2' => ['MB', 'K (DD2)', 'Potassium Dry Weight', 'mg/kg'],
    'calcium_DD2' => ['MB', 'Ca (DD2)', 'Calcium Dry Weight', 'mg/kg'],
    'vanadium_DD2' => ['MB', 'V (DD2)', 'Vanadium Dry Weight', 'mg/kg'],
    'manganese_DD2' => ['MB', 'Mn (DD2)', 'Manganese Dry Weight', 'mg/kg'],
    'iron_DD2' => ['MB', 'Fe (DD2)', 'Iron Dry Weight', 'mg/kg'],
    'nickel_DD2' => ['MB', 'Ni (DD2)', 'Nickel Dry Weight', 'mg/kg'],
    'cobalt_DD2' => ['MB', 'Co (DD2)', 'Cobalt Dry Weight', 'mg/kg'],
    'copper_DD2' => ['MB', 'Cu (DD2)', 'Copper Dry Weight', 'mg/kg'],
    'zinc_DD2' => ['MB', 'Zn (DD2)', 'Zinc Dry Weight', 'mg/kg'],
    'arsenic_DD2' => ['MB', 'As (DD2)', 'Arsenic Dry Weight', 'mg/kg'],
    'selenium_DD2' => ['MB', 'Se (DD2)', 'Selenium Dry Weight', 'mg/kg'],
    'rubidium_DD2' => ['MB', 'Rb (DD2)', 'Rubidium Dry Weight', 'mg/kg'],
    'strontium_DD2' => ['MB', 'Sr (DD2)', 'Strontium Dry Weight', 'mg/kg'],
    'cadmium_DD2' => ['MB', 'Cd (DD2)', 'Cadmium Dry Weight', 'mg/kg'],
    'tellurium_DD2' => ['MB', 'Te (DD2)', 'Tellurium Dry Weight', 'mg/kg'],
    'cesium_DD2' => ['MB', 'Cs (DD2)', 'Cesium Dry Weight', 'mg/kg'],
    'barium_DD2' => ['MB', 'Ba (DD2)', 'Barium Dry Weight', 'mg/kg'],
    'lanthanum_DD2' => ['MB', 'La (DD2)', 'Lanthanum Dry Weight', 'mg/kg'],
    'thallium_DD2' => ['MB', 'Tl (DD2)', 'Thallium Dry Weight', 'mg/kg'],
    'gallic_acid_DD2' => ['MB', 'Gallic acid (DD2)', 'Gallic Acid Dry Weight', 'mg/kg'],
    'gallocatchin_DD2' => ['MB', 'Gallocatchin (DD2)', 'Gallocatchin Dry Weight', 'mg/kg'],
    '3_4_dihydroxybenzoic_acid_DD2' => ['MB', '3,4-Dihydroxybenzoic acid (DD2)', '3,4-Dihydroxybenzoic Acid Dry Weight', 'mg/kg'],
    'catechin_DD2' => ['MB', 'Catechin (DD2)', 'Catechin Dry Weight', 'mg/kg'],
    'protocatecuic_aldehyde_DD2' => ['MB', 'Protocatecuic aldehyde (DD2)', 'Protocatecuic Aldehyde Dry Weight', 'mg/kg'],
    'pungenol_DD2' => ['MB', 'Pungenol (DD2)', 'Pungenol Dry Weight', 'mg/kg'],
    'caffeic_acid_DD2' => ['MB', 'Caffeic acid (DD2)', 'Caffeic Acid Dry Weight', 'mg/kg'],
    'vanillin_DD2' => ['MB', 'Vanillin (DD2)', 'Vanillin Dry Weight', 'mg/kg'],
    'piceol_DD2' => ['MB', 'Piceol (DD2)', 'Piceol Dry Weight', 'mg/kg'],
    'taxifolin_DD2' => ['MB', 'Taxifolin (DD2)', 'Taxifolin Dry Weight', 'mg/kg'],
    'p_coumaric_acid_DD2' => ['MB', 'p-Coumaric acid (DD2)', 'p-Coumaric Acid Dry Weight', 'mg/kg'],
    'ferulic_acid_DD2' => ['MB', 'Ferulic acid (DD2)', 'Ferulic Acid Dry Weight', 'mg/kg'],
    'myricetin_DD2' => ['MB', 'Myricetin (DD2)', 'Myricetin Dry Weight', 'mg/kg'],
    'quercetin_DD2' => ['MB', 'Quercetin (DD2)', 'Quercetin Dry Weight', 'mg/kg'],
    'naringenin_DD2' => ['MB', 'Naringenin (DD2)', 'Naringenin Dry Weight', 'mg/kg'],
    'kampferol_DD2' => ['MB', 'Kampferol (DD2)', 'Kampferol Dry Weight', 'mg/kg'],
    'apigenin_DD2' => ['MB', 'Apigenin (DD2)', 'Apigenin Dry Weight', 'mg/kg'],
    'isorhamnetin_DD2' => ['MB', 'Isorhamnetin (DD2)', 'Isorhamnetin Dry Weight', 'mg/kg'],

    'lithium_HAR' => ['MB', 'Li (HAR)', 'Lithium Dry Weight', 'mg/kg'],
    'boron_HAR' => ['MB', 'B (HAR)', 'Boron Dry Weight', 'mg/kg'],
    'sodium_HAR' => ['MB', 'Na (HAR)', 'Sodium Dry Weight', 'mg/kg'],
    'magnesium_HAR' => ['MB', 'Mg (HAR)', 'Magnesium Dry Weight', 'mg/kg'],
    'aluminum_HAR' => ['MB', 'Al (HAR)', 'Aluminum Dry Weight', 'mg/kg'],
    'phosphorus_HAR' => ['MB', 'P (HAR)', 'Phosphorus Dry Weight', 'mg/kg'],
    'potassium_HAR' => ['MB', 'K (HAR)', 'Potassium Dry Weight', 'mg/kg'],
    'calcium_HAR' => ['MB', 'Ca (HAR)', 'Calcium Dry Weight', 'mg/kg'],
    'vanadium_HAR' => ['MB', 'V (HAR)', 'Vanadium Dry Weight', 'mg/kg'],
    'manganese_HAR' => ['MB', 'Mn (HAR)', 'Manganese Dry Weight', 'mg/kg'],
    'iron_HAR' => ['MB', 'Fe (HAR)', 'Iron Dry Weight', 'mg/kg'],
    'nickel_HAR' => ['MB', 'Ni (HAR)', 'Nickel Dry Weight', 'mg/kg'],
    'cobalt_HAR' => ['MB', 'Co (HAR)', 'Cobalt Dry Weight', 'mg/kg'],
    'copper_HAR' => ['MB', 'Cu (HAR)', 'Copper Dry Weight', 'mg/kg'],
    'zinc_HAR' => ['MB', 'Zn (HAR)', 'Zinc Dry Weight', 'mg/kg'],
    'arsenic_HAR' => ['MB', 'As (HAR)', 'Arsenic Dry Weight', 'mg/kg'],
    'selenium_HAR' => ['MB', 'Se (HAR)', 'Selenium Dry Weight', 'mg/kg'],
    'rubidium_HAR' => ['MB', 'Rb (HAR)', 'Rubidium Dry Weight', 'mg/kg'],
    'strontium_HAR' => ['MB', 'Sr (HAR)', 'Strontium Dry Weight', 'mg/kg'],
    'cadmium_HAR' => ['MB', 'Cd (HAR)', 'Cadmium Dry Weight', 'mg/kg'],
    'tellurium_HAR' => ['MB', 'Te (HAR)', 'Tellurium Dry Weight', 'mg/kg'],
    'cesium_HAR' => ['MB', 'Cs (HAR)', 'Cesium Dry Weight', 'mg/kg'],
    'barium_HAR' => ['MB', 'Ba (HAR)', 'Barium Dry Weight', 'mg/kg'],
    'lanthanum_HAR' => ['MB', 'La (HAR)', 'Lanthanum Dry Weight', 'mg/kg'],
    'thallium_HAR' => ['MB', 'Tl (HAR)', 'Thallium Dry Weight', 'mg/kg'],
    'gallic_acid_HAR' => ['MB', 'Gallic acid (HAR)', 'Gallic Acid Dry Weight', 'mg/kg'],
    'gallocatchin_HAR' => ['MB', 'Gallocatchin (HAR)', 'Gallocatchin Dry Weight', 'mg/kg'],
    '3_4_dihydroxybenzoic_acid_HAR' => ['MB', '3,4-Dihydroxybenzoic acid (HAR)', '3,4-Dihydroxybenzoic Acid Dry Weight', 'mg/kg'],
    'catechin_HAR' => ['MB', 'Catechin (HAR)', 'Catechin Dry Weight', 'mg/kg'],
    'protocatecuic_aldehyde_HAR' => ['MB', 'Protocatecuic aldehyde (HAR)', 'Protocatecuic Aldehyde Dry Weight', 'mg/kg'],
    'pungenol_HAR' => ['MB', 'Pungenol (HAR)', 'Pungenol Dry Weight', 'mg/kg'],
    'caffeic_acid_HAR' => ['MB', 'Caffeic acid (HAR)', 'Caffeic Acid Dry Weight', 'mg/kg'],
    'vanillin_HAR' => ['MB', 'Vanillin (HAR)', 'Vanillin Dry Weight', 'mg/kg'],
    'piceol_HAR' => ['MB', 'Piceol (HAR)', 'Piceol Dry Weight', 'mg/kg'],
    'taxifolin_HAR' => ['MB', 'Taxifolin (HAR)', 'Taxifolin Dry Weight', 'mg/kg'],
    'p_coumaric_acid_HAR' => ['MB', 'p-Coumaric acid (HAR)', 'p-Coumaric Acid Dry Weight', 'mg/kg'],
    'ferulic_acid_HAR' => ['MB', 'Ferulic acid (HAR)', 'Ferulic Acid Dry Weight', 'mg/kg'],
    'myricetin_HAR' => ['MB', 'Myricetin (HAR)', 'Myricetin Dry Weight', 'mg/kg'],
    'quercetin_HAR' => ['MB', 'Quercetin (HAR)', 'Quercetin Dry Weight', 'mg/kg'],
    'naringenin_HAR' => ['MB', 'Naringenin (HAR)', 'Naringenin Dry Weight', 'mg/kg'],
    'kampferol_HAR' => ['MB', 'Kampferol (HAR)', 'Kampferol Dry Weight', 'mg/kg'],
    'apigenin_HAR' => ['MB', 'Apigenin (HAR)', 'Apigenin Dry Weight', 'mg/kg'],
    'isorhamnetin_HAR' => ['MB', 'Isorhamnetin (HAR)', 'Isorhamnetin Dry Weight', 'mg/kg'],

    # Metabolomics (Primary Metabolites)
    'choline_FIELD' => ['MP', 'Choline (Field)', 'Choline Dry Weight', 'mg/kg'],
    'betaine_FIELD' => ['MP', 'Betaine (Field)', 'Betaine Dry Weight', 'mg/kg'],
    'creatinine_FIELD' => ['MP', 'Creatinine (Field)', 'Creatinine Dry Weight', 'mg/kg'],
    'tmao_FIELD' => ['MP', 'TMAO (Field)', 'Trimethylamine N-Oxide Dry Weight', 'mg/kg'],
    'creatine_FIELD' => ['MP', 'Creatine (Field)', 'Creatine Dry Weight', 'mg/kg'],
    'proline_betaine_FIELD' => ['MP', 'Proline betaine (Field)', 'Proline Betaine Dry Weight', 'mg/kg'],
    'zeatin_FIELD' => ['MP', 'Zeatin (Field)', 'Zeatin Dry Weight', 'mg/kg'],
    'proline_FIELD' => ['MP', 'Proline (Field)', 'Proline Dry Weight', 'mg/kg'],
    'carnosine_FIELD' => ['MP', 'Carnosine (Field)', 'Carnosine Dry Weight', 'mg/kg'],
    'histidine_FIELD' => ['MP', 'Histidine (Field)', 'Histidine Dry Weight', 'mg/kg'],
    'histamine_FIELD' => ['MP', 'Histamine (Field)', 'Histamine Dry Weight', 'mg/kg'],
    'methylhistidine_FIELD' => ['MP', 'Methylhistidine (Field)', 'Methylhistidine Dry Weight', 'mg/kg'],
    'arginine_FIELD' => ['MP', 'Arginine (Field)', 'Arginine Dry Weight', 'mg/kg'],
    'taurine_FIELD' => ['MP', 'Taurine (Field)', 'Taurine Dry Weight', 'mg/kg'],
    'total_dma_FIELD' => ['MP', 'Total DMA (Field)', 'Total Dimethylamine Dry Weight', 'mg/kg'],
    'trans_hydroxyproline_FIELD' => ['MP', 'trans-Hydroxyproline (Field)', 'trans-Hydroxyproline Dry Weight', 'mg/kg'],
    'glutamine_FIELD' => ['MP', 'Glutamine (Field)', 'Glutamine Dry Weight', 'mg/kg'],
    'asparagine_FIELD' => ['MP', 'Asparagine (Field)', 'Asparagine Dry Weight', 'mg/kg'],
    'citrulline_FIELD' => ['MP', 'Citrulline (Field)', 'Citrulline Dry Weight', 'mg/kg'],
    'methionine_sulfoxide_FIELD' => ['MP', 'Methionine (Field)', 'Methionine Dry Weight', 'mg/kg'],
    'serine_FIELD' => ['MP', 'Serine (Field)', 'Serine Dry Weight', 'mg/kg'],
    'acetylornithine_FIELD' => ['MP', 'Acetylornithine (Field)', 'Acetylornithine Dry Weight', 'mg/kg'],
    'glycine_FIELD' => ['MP', 'Glycine (Field)', 'Glycine Dry Weight', 'mg/kg'],
    'glutamic_acid_FIELD' => ['MP', 'Glutamic acid (Field)', 'Glutamic Acid Dry Weight', 'mg/kg'],
    'aspartic_acid_FIELD' => ['MP', 'Aspartic acid (Field)', 'Aspartic Acid Dry Weight', 'mg/kg'],
    'sarcosine_FIELD' => ['MP', 'Sarcosine (Field)', 'Sarcosine Dry Weight', 'mg/kg'],
    'threonine_FIELD' => ['MP', 'Threonine (Field)', 'Threonine Dry Weight', 'mg/kg'],
    'alpha_aaa_FIELD' => ['MP', 'alpha-AAA (Field)', 'alpha-Aminoadipic Acid Dry Weight', 'mg/kg'],
    'dopa_FIELD' => ['MP', 'DOPA (Field)', 'Dihydroxyphenylalanine Dry Weight', 'mg/kg'],
    'alanine_FIELD' => ['MP', 'Alanine (Field)', 'Alanine Dry Weight', 'mg/kg'],
    'tyrosine_FIELD' => ['MP', 'Tyrosine (Field)', 'Tyrosine Dry Weight', 'mg/kg'],
    'dopamine_FIELD' => ['MP', 'Dopamine (Field)', 'Dopamine Dry Weight', 'mg/kg'],
    'serotonin_FIELD' => ['MP', 'Serotonin (Field)', 'Serotonin Dry Weight', 'mg/kg'],
    'methonine_FIELD' => ['MP', 'Methonine (Field)', 'Methonine Dry Weight', 'mg/kg'],
    'valine_FIELD' => ['MP', 'Valine (Field)', 'Valine Dry Weight', 'mg/kg'],
    'tyramine_FIELD' => ['MP', 'Tyramine (Field)', 'Tyramine Dry Weight', 'mg/kg'],
    'ornithine_FIELD' => ['MP', 'Ornithine (Field)', 'Ornithine Dry Weight', 'mg/kg'],
    'kynurenine_FIELD' => ['MP', 'Kynurenine (Field)', 'Kynurenine Dry Weight', 'mg/kg'],
    'lysine_FIELD' => ['MP', 'Lysine (Field)', 'Lysine Dry Weight', 'mg/kg'],
    'nitro_tyr_FIELD' => ['MP', 'Nitro-Tyr (Field)', 'Nitrotyrosine Dry Weight', 'mg/kg'],
    'tryptophan_FIELD' => ['MP', 'Tryptophan (Field)', 'Tryptophan Dry Weight', 'mg/kg'],
    'xleu_FIELD' => ['MP', 'xLeucine (Field)', 'xLeucine (combined leucine and isoleucine) Dry Weight', 'mg/kg'],
    'phenylalanine_FIELD' => ['MP', 'Phenylalanine (Field)', 'Phenylalanine Dry Weight', 'mg/kg'],
    'putrescine_FIELD' => ['MP', 'Putrescine (Field)', 'Putrescine Dry Weight', 'mg/kg'],
    'pea_FIELD' => ['MP', 'PEA (Field)', 'Phenethylamine Dry Weight', 'mg/kg'],
    'spermidine_FIELD' => ['MP', 'Spermidine (Field)', 'Spermidine Dry Weight', 'mg/kg'],
    'spermine_FIELD' => ['MP', 'Spermine (Field)', 'Spermine Dry Weight', 'mg/kg'],
    'glucose_FIELD' => ['MP', 'Glucose (Field)', 'Glucose Dry Weight', 'mg/kg'],
    'carnitine_FIELD' => ['MP', 'Carnitine (Field)', 'Carnitine Dry Weight', 'mg/kg'],
    'lysoc14_0_FIELD' => ['MP', 'LYSOC14:0 (Field)', 'LYSOC14:0 Dry Weight', 'mg/kg'],
    'lysoc16_0_FIELD' => ['MP', 'LYSOC16:0 (Field)', 'LYSOC16:0 Dry Weight', 'mg/kg'],
    'lysoc18_2_FIELD' => ['MP', 'LYSOC18:2 (Field)', 'LYSOC18:2 Dry Weight', 'mg/kg'],
    'lysoc18_1_FIELD' => ['MP', 'LYSOC18:1 (Field)', 'LYSOC18:1 Dry Weight', 'mg/kg'],
    'lysoc18_0_FIELD' => ['MP', 'LYSOC18:0 (Field)', 'LYSOC18:0 Dry Weight', 'mg/kg'],
    'pc_aa_c36_6_FIELD' => ['MP', 'PC aa C36:6 (Field)', 'PC aa C36:6 Dry Weight', 'mg/kg'],
    'pc_aa_c36_0_FIELD' => ['MP', 'PC aa C36:0 (Field)', 'PC aa C36:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_6_FIELD' => ['MP', 'PC aa C38:6 (Field)', 'PC aa C38:6 Dry Weight', 'mg/kg'],
    'pc_aa_c38_0_FIELD' => ['MP', 'PC aa C38:0 (Field)', 'PC aa C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c40_6_FIELD' => ['MP', 'PC aa C40:6 (Field)', 'PC aa C40:6 Dry Weight', 'mg/kg'],
    'pc_aa_c32_0_FIELD' => ['MP', 'PC aa C32:0 (Field)', 'PC aa C32:0 Dry Weight', 'mg/kg'],
    'pc_ae_c34_2_FIELD' => ['MP', 'PC ae C34:2 (Field)', 'PC ae C34:2 Dry Weight', 'mg/kg'],
    'pc_ae_c34_1_FIELD' => ['MP', 'PC ae C34:1 (Field)', 'PC ae C34:1 Dry Weight', 'mg/kg'],
    'pc_aa_c34_4_FIELD' => ['MP', 'PC aa C34:4 (Field)', 'PC aa C34:4 Dry Weight', 'mg/kg'],
    'pc_aa_c34_3_FIELD' => ['MP', 'PC aa C34:3 (Field)', 'PC aa C34:3 Dry Weight', 'mg/kg'],
    'pc_aa_c34_2_FIELD' => ['MP', 'PC aa C34:2 (Field)', 'PC aa C34:2 Dry Weight', 'mg/kg'],
    'pc_aa_c34_1_FIELD' => ['MP', 'PC aa C34:1 (Field)', 'PC aa C34:1 Dry Weight', 'mg/kg'],
    'pc_ae_c36_3_FIELD' => ['MP', 'PC ae C36:3 (Field)', 'PC ae C36:3 Dry Weight', 'mg/kg'],
    'pc_ae_c36_2_FIELD' => ['MP', 'PC ae C36:2 (Field)', 'PC ae C36:2 Dry Weight', 'mg/kg'],
    'pc_ae_c36_1_FIELD' => ['MP', 'PC ae C36:1 (Field)', 'PC ae C36:1 Dry Weight', 'mg/kg'],
    'pc_aa_c36_5_FIELD' => ['MP', 'PC aa C36:5 (Field)', 'PC aa C36:5 Dry Weight', 'mg/kg'],
    'pc_aa_c36_4_FIELD' => ['MP', 'PC aa C36:4 (Field)', 'PC aa C36:4 Dry Weight', 'mg/kg'],
    'pc_aa_c36_3_FIELD' => ['MP', 'PC aa C36:3 (Field)', 'PC aa C36:3 Dry Weight', 'mg/kg'],
    'pc_aa_c36_2_FIELD' => ['MP', 'PC aa C36:2 (Field)', 'PC aa C36:2 Dry Weight', 'mg/kg'],
    'pc_aa_c36_1_FIELD' => ['MP', 'PC aa C36:1 (Field)', 'PC aa C36:1 Dry Weight', 'mg/kg'],
    'pc_ae_c38_4_FIELD' => ['MP', 'PC ae C38:4 (Field)', 'PC ae C38:4 Dry Weight', 'mg/kg'],
    'pc_ae_c38_3_FIELD' => ['MP', 'PC ae C38:3 (Field)', 'PC ae C38:3 Dry Weight', 'mg/kg'],
    'pc_ae_c38_2_FIELD' => ['MP', 'PC ae C38:2 (Field)', 'PC ae C38:2 Dry Weight', 'mg/kg'],
    'pc_ae_c38_0_FIELD' => ['MP', 'PC ae C38:0 (Field)', 'PC ae C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_5_FIELD' => ['MP', 'PC aa C38:5 (Field)', 'PC aa C38:5 Dry Weight', 'mg/kg'],
    'pc_aa_c38_4_FIELD' => ['MP', 'PC aa C38:4 (Field)', 'PC aa C38:4 Dry Weight', 'mg/kg'],
    'pc_aa_c38_3_FIELD' => ['MP', 'PC aa C38:3 (Field)', 'PC aa C38:3 Dry Weight', 'mg/kg'],
    'pc_aa_c38_1_FIELD' => ['MP', 'PC aa C38:1 (Field)', 'PC aa C38:1 Dry Weight', 'mg/kg'],
    'pc_ae_c40_4_FIELD' => ['MP', 'PC ae C40:4 (Field)', 'PC ae C40:4 Dry Weight', 'mg/kg'],
    'pc_ae_c40_3_FIELD' => ['MP', 'PC ae C40:3 (Field)', 'PC ae C40:3 Dry Weight', 'mg/kg'],
    'pc_aa_c40_5_FIELD' => ['MP', 'PC aa C40:5 (Field)', 'PC aa C40:5 Dry Weight', 'mg/kg'],
    'pc_ae_c42_5_FIELD' => ['MP', 'PC ae C42:5 (Field)', 'PC ae C42:5 Dry Weight', 'mg/kg'],
    'shikimic_acid_FIELD' => ['MP', 'Shikimic acid (Field)', 'Shikimic Acid Dry Weight', 'mg/kg'],
    'glyceric_acid_FIELD' => ['MP', 'Glyceric acid (Field)', 'Glyceric Acid Dry Weight', 'mg/kg'],
    'beta_hydroxybutyric_acid_FIELD' => ['MP', 'beta-Hydroxybutyric acid (Field)', 'beta-Hydroxybutyric Acid Dry Weight', 'mg/kg'],
    'lactic_acid_FIELD' => ['MP', 'Lactic acid (Field)', 'Lactic Acid Dry Weight', 'mg/kg'],
    'propionic_acid_FIELD' => ['MP', 'Propionic acid (Field)', 'Propionic Acid Dry Weight', 'mg/kg'],
    'malic_acid_FIELD' => ['MP', 'Malic acid (Field)', 'Malic Acid Dry Weight', 'mg/kg'],
    'butyric_acid_FIELD' => ['MP', 'Butyric acid (Field)', 'Butyric Acid Dry Weight', 'mg/kg'],
    'hippuric_acid_FIELD' => ['MP', 'Hippuric acid (Field)', 'Hippuric Acid Dry Weight', 'mg/kg'],
    'succinic_acid_FIELD' => ['MP', 'Succinic acid (Field)', 'Succinic Acid Dry Weight', 'mg/kg'],
    'glutaric_acid_FIELD' => ['MP', 'Glutaric acid (Field)', 'Glutaric Acid Dry Weight', 'mg/kg'],
    'fumaric_acid_FIELD' => ['MP', 'Fumaric acid (Field)', 'Fumaric Acid Dry Weight', 'mg/kg'],
    'valeric_acid_FIELD' => ['MP', 'Valeric acid (Field)', 'Valeric Acid Dry Weight', 'mg/kg'],
    'benzoic_acid_FIELD' => ['MP', 'Benzoic acid (Field)', 'Benzoic Acid Dry Weight', 'mg/kg'],
    'oxalic_acid_FIELD' => ['MP', 'Oxalic acid (Field)', 'Oxalic Acid Dry Weight', 'mg/kg'],
    'salicylic_acid_FIELD' => ['MP', 'Salicylic acid (Field)', 'Salicylic Acid Dry Weight', 'mg/kg'],
    'citric_acid_FIELD' => ['MP', 'Citric acid (Field)', 'Citric Acid Dry Weight', 'mg/kg'],
    'aconitic_acid_FIELD' => ['MP', 'Aconitic acid (Field)', 'Aconitic Acid Dry Weight', 'mg/kg'],
    'pyruvic_acid_FIELD' => ['MP', 'Pyruvic acid (Field)', 'Pyruvic Acid Dry Weight', 'mg/kg'],
    'alpha_ketoglutaric_acid_FIELD' => ['MP', 'alpha-Ketoglutaric acid (Field)', 'alpha-Ketoglutaric Acid Dry Weight', 'mg/kg'],

    'choline_PRE' => ['MP', 'Choline (PRE)', 'Choline Dry Weight', 'mg/kg'],
    'betaine_PRE' => ['MP', 'Betaine (PRE)', 'Betaine Dry Weight', 'mg/kg'],
    'creatinine_PRE' => ['MP', 'Creatinine (PRE)', 'Creatinine Dry Weight', 'mg/kg'],
    'tmao_PRE' => ['MP', 'TMAO (PRE)', 'Trimethylamine N-Oxide Dry Weight', 'mg/kg'],
    'creatine_PRE' => ['MP', 'Creatine (PRE)', 'Creatine Dry Weight', 'mg/kg'],
    'proline_betaine_PRE' => ['MP', 'Proline betaine (PRE)', 'Proline Betaine Dry Weight', 'mg/kg'],
    'zeatin_PRE' => ['MP', 'Zeatin (PRE)', 'Zeatin Dry Weight', 'mg/kg'],
    'proline_PRE' => ['MP', 'Proline (PRE)', 'Proline Dry Weight', 'mg/kg'],
    'carnosine_PRE' => ['MP', 'Carnosine (PRE)', 'Carnosine Dry Weight', 'mg/kg'],
    'histidine_PRE' => ['MP', 'Histidine (PRE)', 'Histidine Dry Weight', 'mg/kg'],
    'histamine_PRE' => ['MP', 'Histamine (PRE)', 'Histamine Dry Weight', 'mg/kg'],
    'methylhistidine_PRE' => ['MP', 'Methylhistidine (PRE)', 'Methylhistidine Dry Weight', 'mg/kg'],
    'arginine_PRE' => ['MP', 'Arginine (PRE)', 'Arginine Dry Weight', 'mg/kg'],
    'taurine_PRE' => ['MP', 'Taurine (PRE)', 'Taurine Dry Weight', 'mg/kg'],
    'total_dma_PRE' => ['MP', 'Total DMA (PRE)', 'Total Dimethylamine Dry Weight', 'mg/kg'],
    'trans_hydroxyproline_PRE' => ['MP', 'trans-Hydroxyproline (PRE)', 'trans-Hydroxyproline Dry Weight', 'mg/kg'],
    'glutamine_PRE' => ['MP', 'Glutamine (PRE)', 'Glutamine Dry Weight', 'mg/kg'],
    'asparagine_PRE' => ['MP', 'Asparagine (PRE)', 'Asparagine Dry Weight', 'mg/kg'],
    'citrulline_PRE' => ['MP', 'Citrulline (PRE)', 'Citrulline Dry Weight', 'mg/kg'],
    'methionine_sulfoxide_PRE' => ['MP', 'Methionine (PRE)', 'Methionine Dry Weight', 'mg/kg'],
    'serine_PRE' => ['MP', 'Serine (PRE)', 'Serine Dry Weight', 'mg/kg'],
    'acetylornithine_PRE' => ['MP', 'Acetylornithine (PRE)', 'Acetylornithine Dry Weight', 'mg/kg'],
    'glycine_PRE' => ['MP', 'Glycine (PRE)', 'Glycine Dry Weight', 'mg/kg'],
    'glutamic_acid_PRE' => ['MP', 'Glutamic acid (PRE)', 'Glutamic Acid Dry Weight', 'mg/kg'],
    'aspartic_acid_PRE' => ['MP', 'Aspartic acid (PRE)', 'Aspartic Acid Dry Weight', 'mg/kg'],
    'sarcosine_PRE' => ['MP', 'Sarcosine (PRE)', 'Sarcosine Dry Weight', 'mg/kg'],
    'threonine_PRE' => ['MP', 'Threonine (PRE)', 'Threonine Dry Weight', 'mg/kg'],
    'alpha_aaa_PRE' => ['MP', 'alpha-AAA (PRE)', 'alpha-Aminoadipic Acid Dry Weight', 'mg/kg'],
    'dopa_PRE' => ['MP', 'DOPA (PRE)', 'Dihydroxyphenylalanine Dry Weight', 'mg/kg'],
    'alanine_PRE' => ['MP', 'Alanine (PRE)', 'Alanine Dry Weight', 'mg/kg'],
    'tyrosine_PRE' => ['MP', 'Tyrosine (PRE)', 'Tyrosine Dry Weight', 'mg/kg'],
    'dopamine_PRE' => ['MP', 'Dopamine (PRE)', 'Dopamine Dry Weight', 'mg/kg'],
    'serotonin_PRE' => ['MP', 'Serotonin (PRE)', 'Serotonin Dry Weight', 'mg/kg'],
    'methonine_PRE' => ['MP', 'Methonine (PRE)', 'Methonine Dry Weight', 'mg/kg'],
    'valine_PRE' => ['MP', 'Valine (PRE)', 'Valine Dry Weight', 'mg/kg'],
    'tyramine_PRE' => ['MP', 'Tyramine (PRE)', 'Tyramine Dry Weight', 'mg/kg'],
    'ornithine_PRE' => ['MP', 'Ornithine (PRE)', 'Ornithine Dry Weight', 'mg/kg'],
    'kynurenine_PRE' => ['MP', 'Kynurenine (PRE)', 'Kynurenine Dry Weight', 'mg/kg'],
    'lysine_PRE' => ['MP', 'Lysine (PRE)', 'Lysine Dry Weight', 'mg/kg'],
    'nitro_tyr_PRE' => ['MP', 'Nitro-Tyr (PRE)', 'Nitrotyrosine Dry Weight', 'mg/kg'],
    'tryptophan_PRE' => ['MP', 'Tryptophan (PRE)', 'Tryptophan Dry Weight', 'mg/kg'],
    'xleu_PRE' => ['MP', 'xLeucine (PRE)', 'xLeu (combined leucine and isoleucine) Dry Weight', 'mg/kg'],
    'phenylalanine_PRE' => ['MP', 'Phenylalanine (PRE)', 'Phenylalanine Dry Weight', 'mg/kg'],
    'putrescine_PRE' => ['MP', 'Putrescine (PRE)', 'Putrescine Dry Weight', 'mg/kg'],
    'pea_PRE' => ['MP', 'PEA (PRE)', 'Phenethylamine Dry Weight', 'mg/kg'],
    'spermidine_PRE' => ['MP', 'Spermidine (PRE)', 'Spermidine Dry Weight', 'mg/kg'],
    'spermine_PRE' => ['MP', 'Spermine (PRE)', 'Spermine Dry Weight', 'mg/kg'],
    'glucose_PRE' => ['MP', 'Glucose (PRE)', 'Glucose Dry Weight', 'mg/kg'],
    'carnitine_PRE' => ['MP', 'Carnitine (PRE)', 'Carnitine Dry Weight', 'mg/kg'],
    'lysoc14_0_PRE' => ['MP', 'LYSOC14:0 (PRE)', 'LYSOC14:0 Dry Weight', 'mg/kg'],
    'lysoc16_0_PRE' => ['MP', 'LYSOC16:0 (PRE)', 'LYSOC16:0 Dry Weight', 'mg/kg'],
    'lysoc18_2_PRE' => ['MP', 'LYSOC18:2 (PRE)', 'LYSOC18:2 Dry Weight', 'mg/kg'],
    'lysoc18_1_PRE' => ['MP', 'LYSOC18:1 (PRE)', 'LYSOC18:1 Dry Weight', 'mg/kg'],
    'lysoc18_0_PRE' => ['MP', 'LYSOC18:0 (PRE)', 'LYSOC18:0 Dry Weight', 'mg/kg'],
    'pc_aa_c36_6_PRE' => ['MP', 'PC aa C36:6 (PRE)', 'PC aa C36:6 Dry Weight', 'mg/kg'],
    'pc_aa_c36_0_PRE' => ['MP', 'PC aa C36:0 (PRE)', 'PC aa C36:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_6_PRE' => ['MP', 'PC aa C38:6 (PRE)', 'PC aa C38:6 Dry Weight', 'mg/kg'],
    'pc_aa_c38_0_PRE' => ['MP', 'PC aa C38:0 (PRE)', 'PC aa C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c40_6_PRE' => ['MP', 'PC aa C40:6 (PRE)', 'PC aa C40:6 Dry Weight', 'mg/kg'],
    'pc_aa_c32_0_PRE' => ['MP', 'PC aa C32:0 (PRE)', 'PC aa C32:0 Dry Weight', 'mg/kg'],
    'pc_ae_c34_2_PRE' => ['MP', 'PC ae C34:2 (PRE)', 'PC ae C34:2 Dry Weight', 'mg/kg'],
    'pc_ae_c34_1_PRE' => ['MP', 'PC ae C34:1 (PRE)', 'PC ae C34:1 Dry Weight', 'mg/kg'],
    'pc_aa_c34_4_PRE' => ['MP', 'PC aa C34:4 (PRE)', 'PC aa C34:4 Dry Weight', 'mg/kg'],
    'pc_aa_c34_3_PRE' => ['MP', 'PC aa C34:3 (PRE)', 'PC aa C34:3 Dry Weight', 'mg/kg'],
    'pc_aa_c34_2_PRE' => ['MP', 'PC aa C34:2 (PRE)', 'PC aa C34:2 Dry Weight', 'mg/kg'],
    'pc_aa_c34_1_PRE' => ['MP', 'PC aa C34:1 (PRE)', 'PC aa C34:1 Dry Weight', 'mg/kg'],
    'pc_ae_c36_3_PRE' => ['MP', 'PC ae C36:3 (PRE)', 'PC ae C36:3 Dry Weight', 'mg/kg'],
    'pc_ae_c36_2_PRE' => ['MP', 'PC ae C36:2 (PRE)', 'PC ae C36:2 Dry Weight', 'mg/kg'],
    'pc_ae_c36_1_PRE' => ['MP', 'PC ae C36:1 (PRE)', 'PC ae C36:1 Dry Weight', 'mg/kg'],
    'pc_aa_c36_5_PRE' => ['MP', 'PC aa C36:5 (PRE)', 'PC aa C36:5 Dry Weight', 'mg/kg'],
    'pc_aa_c36_4_PRE' => ['MP', 'PC aa C36:4 (PRE)', 'PC aa C36:4 Dry Weight', 'mg/kg'],
    'pc_aa_c36_3_PRE' => ['MP', 'PC aa C36:3 (PRE)', 'PC aa C36:3 Dry Weight', 'mg/kg'],
    'pc_aa_c36_2_PRE' => ['MP', 'PC aa C36:2 (PRE)', 'PC aa C36:2 Dry Weight', 'mg/kg'],
    'pc_aa_c36_1_PRE' => ['MP', 'PC aa C36:1 (PRE)', 'PC aa C36:1 Dry Weight', 'mg/kg'],
    'pc_ae_c38_4_PRE' => ['MP', 'PC ae C38:4 (PRE)', 'PC ae C38:4 Dry Weight', 'mg/kg'],
    'pc_ae_c38_3_PRE' => ['MP', 'PC ae C38:3 (PRE)', 'PC ae C38:3 Dry Weight', 'mg/kg'],
    'pc_ae_c38_2_PRE' => ['MP', 'PC ae C38:2 (PRE)', 'PC ae C38:2 Dry Weight', 'mg/kg'],
    'pc_ae_c38_0_PRE' => ['MP', 'PC ae C38:0 (PRE)', 'PC ae C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_5_PRE' => ['MP', 'PC aa C38:5 (PRE)', 'PC aa C38:5 Dry Weight', 'mg/kg'],
    'pc_aa_c38_4_PRE' => ['MP', 'PC aa C38:4 (PRE)', 'PC aa C38:4 Dry Weight', 'mg/kg'],
    'pc_aa_c38_3_PRE' => ['MP', 'PC aa C38:3 (PRE)', 'PC aa C38:3 Dry Weight', 'mg/kg'],
    'pc_aa_c38_1_PRE' => ['MP', 'PC aa C38:1 (PRE)', 'PC aa C38:1 Dry Weight', 'mg/kg'],
    'pc_ae_c40_4_PRE' => ['MP', 'PC ae C40:4 (PRE)', 'PC ae C40:4 Dry Weight', 'mg/kg'],
    'pc_ae_c40_3_PRE' => ['MP', 'PC ae C40:3 (PRE)', 'PC ae C40:3 Dry Weight', 'mg/kg'],
    'pc_aa_c40_5_PRE' => ['MP', 'PC aa C40:5 (PRE)', 'PC aa C40:5 Dry Weight', 'mg/kg'],
    'pc_ae_c42_5_PRE' => ['MP', 'PC ae C42:5 (PRE)', 'PC ae C42:5 Dry Weight', 'mg/kg'],
    'shikimic_acid_PRE' => ['MP', 'Shikimic acid (PRE)', 'Shikimic Acid Dry Weight', 'mg/kg'],
    'glyceric_acid_PRE' => ['MP', 'Glyceric acid (PRE)', 'Glyceric Acid Dry Weight', 'mg/kg'],
    'beta_hydroxybutyric_acid_PRE' => ['MP', 'beta-Hydroxybutyric acid (PRE)', 'beta-Hydroxybutyric Acid Dry Weight', 'mg/kg'],
    'lactic_acid_PRE' => ['MP', 'Lactic acid (PRE)', 'Lactic Acid Dry Weight', 'mg/kg'],
    'propionic_acid_PRE' => ['MP', 'Propionic acid (PRE)', 'Propionic Acid Dry Weight', 'mg/kg'],
    'malic_acid_PRE' => ['MP', 'Malic acid (PRE)', 'Malic Acid Dry Weight', 'mg/kg'],
    'butyric_acid_PRE' => ['MP', 'Butyric acid (PRE)', 'Butyric Acid Dry Weight', 'mg/kg'],
    'hippuric_acid_PRE' => ['MP', 'Hippuric acid (PRE)', 'Hippuric Acid Dry Weight', 'mg/kg'],
    'succinic_acid_PRE' => ['MP', 'Succinic acid (PRE)', 'Succinic Acid Dry Weight', 'mg/kg'],
    'glutaric_acid_PRE' => ['MP', 'Glutaric acid (PRE)', 'Glutaric Acid Dry Weight', 'mg/kg'],
    'fumaric_acid_PRE' => ['MP', 'Fumaric acid (PRE)', 'Fumaric Acid Dry Weight', 'mg/kg'],
    'valeric_acid_PRE' => ['MP', 'Valeric acid (PRE)', 'Valeric Acid Dry Weight', 'mg/kg'],
    'benzoic_acid_PRE' => ['MP', 'Benzoic acid (PRE)', 'Benzoic Acid Dry Weight', 'mg/kg'],
    'oxalic_acid_PRE' => ['MP', 'Oxalic acid (PRE)', 'Oxalic Acid Dry Weight', 'mg/kg'],
    'salicylic_acid_PRE' => ['MP', 'Salicylic acid (PRE)', 'Salicylic Acid Dry Weight', 'mg/kg'],
    'citric_acid_PRE' => ['MP', 'Citric acid (PRE)', 'Citric Acid Dry Weight', 'mg/kg'],
    'aconitic_acid_PRE' => ['MP', 'Aconitic acid (PRE)', 'Aconitic Acid Dry Weight', 'mg/kg'],
    'pyruvic_acid_PRE' => ['MP', 'Pyruvic acid (PRE)', 'Pyruvic Acid Dry Weight', 'mg/kg'],
    'alpha_ketoglutaric_acid_PRE' => ['MP', 'alpha-Ketoglutaric acid (PRE)', 'alpha-Ketoglutaric Acid Dry Weight', 'mg/kg'],

    'choline_DD2' => ['MP', 'Choline (DD2)', 'Choline Dry Weight', 'mg/kg'],
    'betaine_DD2' => ['MP', 'Betaine (DD2)', 'Betaine Dry Weight', 'mg/kg'],
    'creatinine_DD2' => ['MP', 'Creatinine (DD2)', 'Creatinine Dry Weight', 'mg/kg'],
    'tmao_DD2' => ['MP', 'TMAO (DD2)', 'Trimethylamine N-Oxide Dry Weight', 'mg/kg'],
    'creatine_DD2' => ['MP', 'Creatine (DD2)', 'Creatine Dry Weight', 'mg/kg'],
    'proline_betaine_DD2' => ['MP', 'Proline betaine (DD2)', 'Proline Betaine Dry Weight', 'mg/kg'],
    'zeatin_DD2' => ['MP', 'Zeatin (DD2)', 'Zeatin Dry Weight', 'mg/kg'],
    'proline_DD2' => ['MP', 'Proline (DD2)', 'Proline Dry Weight', 'mg/kg'],
    'carnosine_DD2' => ['MP', 'Carnosine (DD2)', 'Carnosine Dry Weight', 'mg/kg'],
    'histidine_DD2' => ['MP', 'Histidine (DD2)', 'Histidine Dry Weight', 'mg/kg'],
    'histamine_DD2' => ['MP', 'Histamine (DD2)', 'Histamine Dry Weight', 'mg/kg'],
    'methylhistidine_DD2' => ['MP', 'Methylhistidine (DD2)', 'Methylhistidine Dry Weight', 'mg/kg'],
    'arginine_DD2' => ['MP', 'Arginine (DD2)', 'Arginine Dry Weight', 'mg/kg'],
    'taurine_DD2' => ['MP', 'Taurine (DD2)', 'Taurine Dry Weight', 'mg/kg'],
    'total_dma_DD2' => ['MP', 'Total DMA (DD2)', 'Total Dimethylamine Dry Weight', 'mg/kg'],
    'trans_hydroxyproline_DD2' => ['MP', 'trans-Hydroxyproline (DD2)', 'trans-Hydroxyproline Dry Weight', 'mg/kg'],
    'glutamine_DD2' => ['MP', 'Glutamine (DD2)', 'Glutamine Dry Weight', 'mg/kg'],
    'asparagine_DD2' => ['MP', 'Asparagine (DD2)', 'Asparagine Dry Weight', 'mg/kg'],
    'citrulline_DD2' => ['MP', 'Citrulline (DD2)', 'Citrulline Dry Weight', 'mg/kg'],
    'methionine_sulfoxide_DD2' => ['MP', 'Methionine (DD2)', 'Methionine Dry Weight', 'mg/kg'],
    'serine_DD2' => ['MP', 'Serine (DD2)', 'Serine Dry Weight', 'mg/kg'],
    'acetylornithine_DD2' => ['MP', 'Acetylornithine (DD2)', 'Acetylornithine Dry Weight', 'mg/kg'],
    'glycine_DD2' => ['MP', 'Glycine (DD2)', 'Glycine Dry Weight', 'mg/kg'],
    'glutamic_acid_DD2' => ['MP', 'Glutamic acid (DD2)', 'Glutamic Acid Dry Weight', 'mg/kg'],
    'aspartic_acid_DD2' => ['MP', 'Aspartic acid (DD2)', 'Aspartic Acid Dry Weight', 'mg/kg'],
    'sarcosine_DD2' => ['MP', 'Sarcosine (DD2)', 'Sarcosine Dry Weight', 'mg/kg'],
    'threonine_DD2' => ['MP', 'Threonine (DD2)', 'Threonine Dry Weight', 'mg/kg'],
    'alpha_aaa_DD2' => ['MP', 'alpha-AAA (DD2)', 'alpha-Aminoadipic Acid Dry Weight', 'mg/kg'],
    'dopa_DD2' => ['MP', 'DOPA (DD2)', 'Dihydroxyphenylalanine Dry Weight', 'mg/kg'],
    'alanine_DD2' => ['MP', 'Alanine (DD2)', 'Alanine Dry Weight', 'mg/kg'],
    'tyrosine_DD2' => ['MP', 'Tyrosine (DD2)', 'Tyrosine Dry Weight', 'mg/kg'],
    'dopamine_DD2' => ['MP', 'Dopamine (DD2)', 'Dopamine Dry Weight', 'mg/kg'],
    'serotonin_DD2' => ['MP', 'Serotonin (DD2)', 'Serotonin Dry Weight', 'mg/kg'],
    'methonine_DD2' => ['MP', 'Methonine (DD2)', 'Methonine Dry Weight', 'mg/kg'],
    'valine_DD2' => ['MP', 'Valine (DD2)', 'Valine Dry Weight', 'mg/kg'],
    'tyramine_DD2' => ['MP', 'Tyramine (DD2)', 'Tyramine Dry Weight', 'mg/kg'],
    'ornithine_DD2' => ['MP', 'Ornithine (DD2)', 'Ornithine Dry Weight', 'mg/kg'],
    'kynurenine_DD2' => ['MP', 'Kynurenine (DD2)', 'Kynurenine Dry Weight', 'mg/kg'],
    'lysine_DD2' => ['MP', 'Lysine (DD2)', 'Lysine Dry Weight', 'mg/kg'],
    'nitro_tyr_DD2' => ['MP', 'Nitro-Tyr (DD2)', 'Nitrotyrosine Dry Weight', 'mg/kg'],
    'tryptophan_DD2' => ['MP', 'Tryptophan (DD2)', 'Tryptophan Dry Weight', 'mg/kg'],
    'xleu_DD2' => ['MP', 'xLeucine (DD2)', 'xLeu (combined leucine and isoleucine) Dry Weight', 'mg/kg'],
    'phenylalanine_DD2' => ['MP', 'Phenylalanine (DD2)', 'Phenylalanine Dry Weight', 'mg/kg'],
    'putrescine_DD2' => ['MP', 'Putrescine (DD2)', 'Putrescine Dry Weight', 'mg/kg'],
    'pea_DD2' => ['MP', 'PEA (DD2)', 'Phenethylamine Dry Weight', 'mg/kg'],
    'spermidine_DD2' => ['MP', 'Spermidine (DD2)', 'Spermidine Dry Weight', 'mg/kg'],
    'spermine_DD2' => ['MP', 'Spermine (DD2)', 'Spermine Dry Weight', 'mg/kg'],
    'glucose_DD2' => ['MP', 'Glucose (DD2)', 'Glucose Dry Weight', 'mg/kg'],
    'carnitine_DD2' => ['MP', 'Carnitine (DD2)', 'Carnitine Dry Weight', 'mg/kg'],
    'lysoc14_0_DD2' => ['MP', 'LYSOC14:0 (DD2)', 'LYSOC14:0 Dry Weight', 'mg/kg'],
    'lysoc16_0_DD2' => ['MP', 'LYSOC16:0 (DD2)', 'LYSOC16:0 Dry Weight', 'mg/kg'],
    'lysoc18_2_DD2' => ['MP', 'LYSOC18:2 (DD2)', 'LYSOC18:2 Dry Weight', 'mg/kg'],
    'lysoc18_1_DD2' => ['MP', 'LYSOC18:1 (DD2)', 'LYSOC18:1 Dry Weight', 'mg/kg'],
    'lysoc18_0_DD2' => ['MP', 'LYSOC18:0 (DD2)', 'LYSOC18:0 Dry Weight', 'mg/kg'],
    'pc_aa_c36_6_DD2' => ['MP', 'PC aa C36:6 (DD2)', 'PC aa C36:6 Dry Weight', 'mg/kg'],
    'pc_aa_c36_0_DD2' => ['MP', 'PC aa C36:0 (DD2)', 'PC aa C36:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_6_DD2' => ['MP', 'PC aa C38:6 (DD2)', 'PC aa C38:6 Dry Weight', 'mg/kg'],
    'pc_aa_c38_0_DD2' => ['MP', 'PC aa C38:0 (DD2)', 'PC aa C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c40_6_DD2' => ['MP', 'PC aa C40:6 (DD2)', 'PC aa C40:6 Dry Weight', 'mg/kg'],
    'pc_aa_c32_0_DD2' => ['MP', 'PC aa C32:0 (DD2)', 'PC aa C32:0 Dry Weight', 'mg/kg'],
    'pc_ae_c34_2_DD2' => ['MP', 'PC ae C34:2 (DD2)', 'PC ae C34:2 Dry Weight', 'mg/kg'],
    'pc_ae_c34_1_DD2' => ['MP', 'PC ae C34:1 (DD2)', 'PC ae C34:1 Dry Weight', 'mg/kg'],
    'pc_aa_c34_4_DD2' => ['MP', 'PC aa C34:4 (DD2)', 'PC aa C34:4 Dry Weight', 'mg/kg'],
    'pc_aa_c34_3_DD2' => ['MP', 'PC aa C34:3 (DD2)', 'PC aa C34:3 Dry Weight', 'mg/kg'],
    'pc_aa_c34_2_DD2' => ['MP', 'PC aa C34:2 (DD2)', 'PC aa C34:2 Dry Weight', 'mg/kg'],
    'pc_aa_c34_1_DD2' => ['MP', 'PC aa C34:1 (DD2)', 'PC aa C34:1 Dry Weight', 'mg/kg'],
    'pc_ae_c36_3_DD2' => ['MP', 'PC ae C36:3 (DD2)', 'PC ae C36:3 Dry Weight', 'mg/kg'],
    'pc_ae_c36_2_DD2' => ['MP', 'PC ae C36:2 (DD2)', 'PC ae C36:2 Dry Weight', 'mg/kg'],
    'pc_ae_c36_1_DD2' => ['MP', 'PC ae C36:1 (DD2)', 'PC ae C36:1 Dry Weight', 'mg/kg'],
    'pc_aa_c36_5_DD2' => ['MP', 'PC aa C36:5 (DD2)', 'PC aa C36:5 Dry Weight', 'mg/kg'],
    'pc_aa_c36_4_DD2' => ['MP', 'PC aa C36:4 (DD2)', 'PC aa C36:4 Dry Weight', 'mg/kg'],
    'pc_aa_c36_3_DD2' => ['MP', 'PC aa C36:3 (DD2)', 'PC aa C36:3 Dry Weight', 'mg/kg'],
    'pc_aa_c36_2_DD2' => ['MP', 'PC aa C36:2 (DD2)', 'PC aa C36:2 Dry Weight', 'mg/kg'],
    'pc_aa_c36_1_DD2' => ['MP', 'PC aa C36:1 (DD2)', 'PC aa C36:1 Dry Weight', 'mg/kg'],
    'pc_ae_c38_4_DD2' => ['MP', 'PC ae C38:4 (DD2)', 'PC ae C38:4 Dry Weight', 'mg/kg'],
    'pc_ae_c38_3_DD2' => ['MP', 'PC ae C38:3 (DD2)', 'PC ae C38:3 Dry Weight', 'mg/kg'],
    'pc_ae_c38_2_DD2' => ['MP', 'PC ae C38:2 (DD2)', 'PC ae C38:2 Dry Weight', 'mg/kg'],
    'pc_ae_c38_0_DD2' => ['MP', 'PC ae C38:0 (DD2)', 'PC ae C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_5_DD2' => ['MP', 'PC aa C38:5 (DD2)', 'PC aa C38:5 Dry Weight', 'mg/kg'],
    'pc_aa_c38_4_DD2' => ['MP', 'PC aa C38:4 (DD2)', 'PC aa C38:4 Dry Weight', 'mg/kg'],
    'pc_aa_c38_3_DD2' => ['MP', 'PC aa C38:3 (DD2)', 'PC aa C38:3 Dry Weight', 'mg/kg'],
    'pc_aa_c38_1_DD2' => ['MP', 'PC aa C38:1 (DD2)', 'PC aa C38:1 Dry Weight', 'mg/kg'],
    'pc_ae_c40_4_DD2' => ['MP', 'PC ae C40:4 (DD2)', 'PC ae C40:4 Dry Weight', 'mg/kg'],
    'pc_ae_c40_3_DD2' => ['MP', 'PC ae C40:3 (DD2)', 'PC ae C40:3 Dry Weight', 'mg/kg'],
    'pc_aa_c40_5_DD2' => ['MP', 'PC aa C40:5 (DD2)', 'PC aa C40:5 Dry Weight', 'mg/kg'],
    'pc_ae_c42_5_DD2' => ['MP', 'PC ae C42:5 (DD2)', 'PC ae C42:5 Dry Weight', 'mg/kg'],
    'shikimic_acid_DD2' => ['MP', 'Shikimic acid (DD2)', 'Shikimic Acid Dry Weight', 'mg/kg'],
    'glyceric_acid_DD2' => ['MP', 'Glyceric acid (DD2)', 'Glyceric Acid Dry Weight', 'mg/kg'],
    'beta_hydroxybutyric_acid_DD2' => ['MP', 'beta-Hydroxybutyric acid (DD2)', 'beta-Hydroxybutyric Acid Dry Weight', 'mg/kg'],
    'lactic_acid_DD2' => ['MP', 'Lactic acid (DD2)', 'Lactic Acid Dry Weight', 'mg/kg'],
    'propionic_acid_DD2' => ['MP', 'Propionic acid (DD2)', 'Propionic Acid Dry Weight', 'mg/kg'],
    'malic_acid_DD2' => ['MP', 'Malic acid (DD2)', 'Malic Acid Dry Weight', 'mg/kg'],
    'butyric_acid_DD2' => ['MP', 'Butyric acid (DD2)', 'Butyric Acid Dry Weight', 'mg/kg'],
    'hippuric_acid_DD2' => ['MP', 'Hippuric acid (DD2)', 'Hippuric Acid Dry Weight', 'mg/kg'],
    'succinic_acid_DD2' => ['MP', 'Succinic acid (DD2)', 'Succinic Acid Dry Weight', 'mg/kg'],
    'glutaric_acid_DD2' => ['MP', 'Glutaric acid (DD2)', 'Glutaric Acid Dry Weight', 'mg/kg'],
    'fumaric_acid_DD2' => ['MP', 'Fumaric acid (DD2)', 'Fumaric Acid Dry Weight', 'mg/kg'],
    'valeric_acid_DD2' => ['MP', 'Valeric acid (DD2)', 'Valeric Acid Dry Weight', 'mg/kg'],
    'benzoic_acid_DD2' => ['MP', 'Benzoic acid (DD2)', 'Benzoic Acid Dry Weight', 'mg/kg'],
    'oxalic_acid_DD2' => ['MP', 'Oxalic acid (DD2)', 'Oxalic Acid Dry Weight', 'mg/kg'],
    'salicylic_acid_DD2' => ['MP', 'Salicylic acid (DD2)', 'Salicylic Acid Dry Weight', 'mg/kg'],
    'citric_acid_DD2' => ['MP', 'Citric acid (DD2)', 'Citric Acid Dry Weight', 'mg/kg'],
    'aconitic_acid_DD2' => ['MP', 'Aconitic acid (DD2)', 'Aconitic Acid Dry Weight', 'mg/kg'],
    'pyruvic_acid_DD2' => ['MP', 'Pyruvic acid (DD2)', 'Pyruvic Acid Dry Weight', 'mg/kg'],
    'alpha_ketoglutaric_acid_DD2' => ['MP', 'alpha-Ketoglutaric acid (DD2)', 'alpha-Ketoglutaric Acid Dry Weight', 'mg/kg'],

    'choline_HAR' => ['MP', 'Choline (HAR)', 'Choline Dry Weight', 'mg/kg'],
    'betaine_HAR' => ['MP', 'Betaine (HAR)', 'Betaine Dry Weight', 'mg/kg'],
    'creatinine_HAR' => ['MP', 'Creatinine (HAR)', 'Creatinine Dry Weight', 'mg/kg'],
    'tmao_HAR' => ['MP', 'TMAO (HAR)', 'Trimethylamine N-Oxide Dry Weight', 'mg/kg'],
    'creatine_HAR' => ['MP', 'Creatine (HAR)', 'Creatine Dry Weight', 'mg/kg'],
    'proline_betaine_HAR' => ['MP', 'Proline betaine (HAR)', 'Proline Betaine Dry Weight', 'mg/kg'],
    'zeatin_HAR' => ['MP', 'Zeatin (HAR)', 'Zeatin Dry Weight', 'mg/kg'],
    'proline_HAR' => ['MP', 'Proline (HAR)', 'Proline Dry Weight', 'mg/kg'],
    'carnosine_HAR' => ['MP', 'Carnosine (HAR)', 'Carnosine Dry Weight', 'mg/kg'],
    'histidine_HAR' => ['MP', 'Histidine (HAR)', 'Histidine Dry Weight', 'mg/kg'],
    'histamine_HAR' => ['MP', 'Histamine (HAR)', 'Histamine Dry Weight', 'mg/kg'],
    'methylhistidine_HAR' => ['MP', 'Methylhistidine (HAR)', 'Methylhistidine Dry Weight', 'mg/kg'],
    'arginine_HAR' => ['MP', 'Arginine (HAR)', 'Arginine Dry Weight', 'mg/kg'],
    'taurine_HAR' => ['MP', 'Taurine (HAR)', 'Taurine Dry Weight', 'mg/kg'],
    'total_dma_HAR' => ['MP', 'Total DMA (HAR)', 'Total Dimethylamine Dry Weight', 'mg/kg'],
    'trans_hydroxyproline_HAR' => ['MP', 'trans-Hydroxyproline (HAR)', 'trans-Hydroxyproline Dry Weight', 'mg/kg'],
    'glutamine_HAR' => ['MP', 'Glutamine (HAR)', 'Glutamine Dry Weight', 'mg/kg'],
    'asparagine_HAR' => ['MP', 'Asparagine (HAR)', 'Asparagine Dry Weight', 'mg/kg'],
    'citrulline_HAR' => ['MP', 'Citrulline (HAR)', 'Citrulline Dry Weight', 'mg/kg'],
    'methionine_sulfoxide_HAR' => ['MP', 'Methionine (HAR)', 'Methionine Dry Weight', 'mg/kg'],
    'serine_HAR' => ['MP', 'Serine (HAR)', 'Serine Dry Weight', 'mg/kg'],
    'acetylornithine_HAR' => ['MP', 'Acetylornithine (HAR)', 'Acetylornithine Dry Weight', 'mg/kg'],
    'glycine_HAR' => ['MP', 'Glycine (HAR)', 'Glycine Dry Weight', 'mg/kg'],
    'glutamic_acid_HAR' => ['MP', 'Glutamic acid (HAR)', 'Glutamic Acid Dry Weight', 'mg/kg'],
    'aspartic_acid_HAR' => ['MP', 'Aspartic acid (HAR)', 'Aspartic Acid Dry Weight', 'mg/kg'],
    'sarcosine_HAR' => ['MP', 'Sarcosine (HAR)', 'Sarcosine Dry Weight', 'mg/kg'],
    'threonine_HAR' => ['MP', 'Threonine (HAR)', 'Threonine Dry Weight', 'mg/kg'],
    'alpha_aaa_HAR' => ['MP', 'alpha-AAA (HAR)', 'alpha-Aminoadipic Acid Dry Weight', 'mg/kg'],
    'dopa_HAR' => ['MP', 'DOPA (HAR)', 'Dihydroxyphenylalanine Dry Weight', 'mg/kg'],
    'alanine_HAR' => ['MP', 'Alanine (HAR)', 'Alanine Dry Weight', 'mg/kg'],
    'tyrosine_HAR' => ['MP', 'Tyrosine (HAR)', 'Tyrosine Dry Weight', 'mg/kg'],
    'dopamine_HAR' => ['MP', 'Dopamine (HAR)', 'Dopamine Dry Weight', 'mg/kg'],
    'serotonin_HAR' => ['MP', 'Serotonin (HAR)', 'Serotonin Dry Weight', 'mg/kg'],
    'methonine_HAR' => ['MP', 'Methonine (HAR)', 'Methonine Dry Weight', 'mg/kg'],
    'valine_HAR' => ['MP', 'Valine (HAR)', 'Valine Dry Weight', 'mg/kg'],
    'tyramine_HAR' => ['MP', 'Tyramine (HAR)', 'Tyramine Dry Weight', 'mg/kg'],
    'ornithine_HAR' => ['MP', 'Ornithine (HAR)', 'Ornithine Dry Weight', 'mg/kg'],
    'kynurenine_HAR' => ['MP', 'Kynurenine (HAR)', 'Kynurenine Dry Weight', 'mg/kg'],
    'lysine_HAR' => ['MP', 'Lysine (HAR)', 'Lysine Dry Weight', 'mg/kg'],
    'nitro_tyr_HAR' => ['MP', 'Nitro-Tyr (HAR)', 'Nitrotyrosine Dry Weight', 'mg/kg'],
    'tryptophan_HAR' => ['MP', 'Tryptophan (HAR)', 'Tryptophan Dry Weight', 'mg/kg'],
    'xleu_HAR' => ['MP', 'xLeucine (HAR)', 'xLeu (combined leucine and isoleucine) Dry Weight', 'mg/kg'],
    'phenylalanine_HAR' => ['MP', 'Phenylalanine (HAR)', 'Phenylalanine Dry Weight', 'mg/kg'],
    'putrescine_HAR' => ['MP', 'Putrescine (HAR)', 'Putrescine Dry Weight', 'mg/kg'],
    'pea_HAR' => ['MP', 'PEA (HAR)', 'Phenethylamine Dry Weight', 'mg/kg'],
    'spermidine_HAR' => ['MP', 'Spermidine (HAR)', 'Spermidine Dry Weight', 'mg/kg'],
    'spermine_HAR' => ['MP', 'Spermine (HAR)', 'Spermine Dry Weight', 'mg/kg'],
    'glucose_HAR' => ['MP', 'Glucose (HAR)', 'Glucose Dry Weight', 'mg/kg'],
    'carnitine_HAR' => ['MP', 'Carnitine (HAR)', 'Carnitine Dry Weight', 'mg/kg'],
    'lysoc14_0_HAR' => ['MP', 'LYSOC14:0 (HAR)', 'LYSOC14:0 Dry Weight', 'mg/kg'],
    'lysoc16_0_HAR' => ['MP', 'LYSOC16:0 (HAR)', 'LYSOC16:0 Dry Weight', 'mg/kg'],
    'lysoc18_2_HAR' => ['MP', 'LYSOC18:2 (HAR)', 'LYSOC18:2 Dry Weight', 'mg/kg'],
    'lysoc18_1_HAR' => ['MP', 'LYSOC18:1 (HAR)', 'LYSOC18:1 Dry Weight', 'mg/kg'],
    'lysoc18_0_HAR' => ['MP', 'LYSOC18:0 (HAR)', 'LYSOC18:0 Dry Weight', 'mg/kg'],
    'pc_aa_c36_6_HAR' => ['MP', 'PC aa C36:6 (HAR)', 'PC aa C36:6 Dry Weight', 'mg/kg'],
    'pc_aa_c36_0_HAR' => ['MP', 'PC aa C36:0 (HAR)', 'PC aa C36:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_6_HAR' => ['MP', 'PC aa C38:6 (HAR)', 'PC aa C38:6 Dry Weight', 'mg/kg'],
    'pc_aa_c38_0_HAR' => ['MP', 'PC aa C38:0 (HAR)', 'PC aa C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c40_6_HAR' => ['MP', 'PC aa C40:6 (HAR)', 'PC aa C40:6 Dry Weight', 'mg/kg'],
    'pc_aa_c32_0_HAR' => ['MP', 'PC aa C32:0 (HAR)', 'PC aa C32:0 Dry Weight', 'mg/kg'],
    'pc_ae_c34_2_HAR' => ['MP', 'PC ae C34:2 (HAR)', 'PC ae C34:2 Dry Weight', 'mg/kg'],
    'pc_ae_c34_1_HAR' => ['MP', 'PC ae C34:1 (HAR)', 'PC ae C34:1 Dry Weight', 'mg/kg'],
    'pc_aa_c34_4_HAR' => ['MP', 'PC aa C34:4 (HAR)', 'PC aa C34:4 Dry Weight', 'mg/kg'],
    'pc_aa_c34_3_HAR' => ['MP', 'PC aa C34:3 (HAR)', 'PC aa C34:3 Dry Weight', 'mg/kg'],
    'pc_aa_c34_2_HAR' => ['MP', 'PC aa C34:2 (HAR)', 'PC aa C34:2 Dry Weight', 'mg/kg'],
    'pc_aa_c34_1_HAR' => ['MP', 'PC aa C34:1 (HAR)', 'PC aa C34:1 Dry Weight', 'mg/kg'],
    'pc_ae_c36_3_HAR' => ['MP', 'PC ae C36:3 (HAR)', 'PC ae C36:3 Dry Weight', 'mg/kg'],
    'pc_ae_c36_2_HAR' => ['MP', 'PC ae C36:2 (HAR)', 'PC ae C36:2 Dry Weight', 'mg/kg'],
    'pc_ae_c36_1_HAR' => ['MP', 'PC ae C36:1 (HAR)', 'PC ae C36:1 Dry Weight', 'mg/kg'],
    'pc_aa_c36_5_HAR' => ['MP', 'PC aa C36:5 (HAR)', 'PC aa C36:5 Dry Weight', 'mg/kg'],
    'pc_aa_c36_4_HAR' => ['MP', 'PC aa C36:4 (HAR)', 'PC aa C36:4 Dry Weight', 'mg/kg'],
    'pc_aa_c36_3_HAR' => ['MP', 'PC aa C36:3 (HAR)', 'PC aa C36:3 Dry Weight', 'mg/kg'],
    'pc_aa_c36_2_HAR' => ['MP', 'PC aa C36:2 (HAR)', 'PC aa C36:2 Dry Weight', 'mg/kg'],
    'pc_aa_c36_1_HAR' => ['MP', 'PC aa C36:1 (HAR)', 'PC aa C36:1 Dry Weight', 'mg/kg'],
    'pc_ae_c38_4_HAR' => ['MP', 'PC ae C38:4 (HAR)', 'PC ae C38:4 Dry Weight', 'mg/kg'],
    'pc_ae_c38_3_HAR' => ['MP', 'PC ae C38:3 (HAR)', 'PC ae C38:3 Dry Weight', 'mg/kg'],
    'pc_ae_c38_2_HAR' => ['MP', 'PC ae C38:2 (HAR)', 'PC ae C38:2 Dry Weight', 'mg/kg'],
    'pc_ae_c38_0_HAR' => ['MP', 'PC ae C38:0 (HAR)', 'PC ae C38:0 Dry Weight', 'mg/kg'],
    'pc_aa_c38_5_HAR' => ['MP', 'PC aa C38:5 (HAR)', 'PC aa C38:5 Dry Weight', 'mg/kg'],
    'pc_aa_c38_4_HAR' => ['MP', 'PC aa C38:4 (HAR)', 'PC aa C38:4 Dry Weight', 'mg/kg'],
    'pc_aa_c38_3_HAR' => ['MP', 'PC aa C38:3 (HAR)', 'PC aa C38:3 Dry Weight', 'mg/kg'],
    'pc_aa_c38_1_HAR' => ['MP', 'PC aa C38:1 (HAR)', 'PC aa C38:1 Dry Weight', 'mg/kg'],
    'pc_ae_c40_4_HAR' => ['MP', 'PC ae C40:4 (HAR)', 'PC ae C40:4 Dry Weight', 'mg/kg'],
    'pc_ae_c40_3_HAR' => ['MP', 'PC ae C40:3 (HAR)', 'PC ae C40:3 Dry Weight', 'mg/kg'],
    'pc_aa_c40_5_HAR' => ['MP', 'PC aa C40:5 (HAR)', 'PC aa C40:5 Dry Weight', 'mg/kg'],
    'pc_ae_c42_5_HAR' => ['MP', 'PC ae C42:5 (HAR)', 'PC ae C42:5 Dry Weight', 'mg/kg'],
    'shikimic_acid_HAR' => ['MP', 'Shikimic acid (HAR)', 'Shikimic Acid Dry Weight', 'mg/kg'],
    'glyceric_acid_HAR' => ['MP', 'Glyceric acid (HAR)', 'Glyceric Acid Dry Weight', 'mg/kg'],
    'beta_hydroxybutyric_acid_HAR' => ['MP', 'beta-Hydroxybutyric acid (HAR)', 'beta-Hydroxybutyric Acid Dry Weight', 'mg/kg'],
    'lactic_acid_HAR' => ['MP', 'Lactic acid (HAR)', 'Lactic Acid Dry Weight', 'mg/kg'],
    'propionic_acid_HAR' => ['MP', 'Propionic acid (HAR)', 'Propionic Acid Dry Weight', 'mg/kg'],
    'malic_acid_HAR' => ['MP', 'Malic acid (HAR)', 'Malic Acid Dry Weight', 'mg/kg'],
    'butyric_acid_HAR' => ['MP', 'Butyric acid (HAR)', 'Butyric Acid Dry Weight', 'mg/kg'],
    'hippuric_acid_HAR' => ['MP', 'Hippuric acid (HAR)', 'Hippuric Acid Dry Weight', 'mg/kg'],
    'succinic_acid_HAR' => ['MP', 'Succinic acid (HAR)', 'Succinic Acid Dry Weight', 'mg/kg'],
    'glutaric_acid_HAR' => ['MP', 'Glutaric acid (HAR)', 'Glutaric Acid Dry Weight', 'mg/kg'],
    'fumaric_acid_HAR' => ['MP', 'Fumaric acid (HAR)', 'Fumaric Acid Dry Weight', 'mg/kg'],
    'valeric_acid_HAR' => ['MP', 'Valeric acid (HAR)', 'Valeric Acid Dry Weight', 'mg/kg'],
    'benzoic_acid_HAR' => ['MP', 'Benzoic acid (HAR)', 'Benzoic Acid Dry Weight', 'mg/kg'],
    'oxalic_acid_HAR' => ['MP', 'Oxalic acid (HAR)', 'Oxalic Acid Dry Weight', 'mg/kg'],
    'salicylic_acid_HAR' => ['MP', 'Salicylic acid (HAR)', 'Salicylic Acid Dry Weight', 'mg/kg'],
    'citric_acid_HAR' => ['MP', 'Citric acid (HAR)', 'Citric Acid Dry Weight', 'mg/kg'],
    'aconitic_acid_HAR' => ['MP', 'Aconitic acid (HAR)', 'Aconitic Acid Dry Weight', 'mg/kg'],
    'pyruvic_acid_HAR' => ['MP', 'Pyruvic acid (HAR)', 'Pyruvic Acid Dry Weight', 'mg/kg'],
    'alpha_ketoglutaric_acid_HAR' => ['MP', 'alpha-Ketoglutaric acid (HAR)', 'alpha-Ketoglutaric Acid Dry Weight', 'mg/kg'],

    # Monoterpene Analysis
    'alpha_pinene_ww' => ['MT', 'apine ww', 'alpha-Pinene Wet Weight', 'ng/mg'],
    'camphene_ww' => ['MT', 'camphene ww', 'Camphene Wet Weight', 'ng/mg'],
    'beta_pinene_ww' => ['MT', 'bpine ww', 'beta-Pinene Wet Weight', 'ng/mg'],
    '3_carene_ww' => ['MT', 'tcarene ww', '3-Carene Wet Weight', 'ng/mg'],
    'myrcene_ww' => ['MT', 'myrc ww', 'Myrcene Wet Weight', 'ng/mg'],
    'alpha_terpinene_ww' => ['MT', 'aterpinene ww', 'alpha-Terpinene Wet Weight', 'ng/mg'],
    'limonene_ww' => ['MT', 'limo ww', 'Limonene Wet Weight', 'ng/mg'],
    'beta_phellandrene_ww' => ['MT', 'bphel ww', 'beta-Phellandrene Wet Weight', 'ng/mg'],
    'gamma_terpinene_ww' => ['MT', 'gterpinene ww', 'gamma-Terpinene Wet Weight', 'ng/mg'],
    'p_cymene_ww' => ['MT', 'pcyme', 'p-Cymene Wet Weight', 'ng/mg'],
    'terpineolene_ww' => ['MT', 'terpineolene ww', 'Terpinolene Wet Weight', 'ng/mg'],
    'bornyl_acetate_ww' => ['MT', 'bornylace ww', 'Bornyl Acetate Wet Weight', 'ng/mg'],
    '4_allylanisole_ww' => ['MT', 'ally ww', '4-Allylanisole Wet Weight', 'ng/mg'],
    'alpha_terpineol_ww' => ['MT', 'aterpineol ww', 'alpha-Terpineol Wet Weight', 'ng/mg'],
    'borneol_ww' => ['MT', 'borneol ww', 'Borneol Wet Weight', 'ng/mg'],
    'camphor_ww' => ['MT', 'camphor ww', 'Camphor Wet Weight', 'ng/mg'],
    'total_monoterpenes_ww' => ['MT', 'total_mono ww', 'Total Monoterpenes Wet Weight', 'ng/mg'],
    'alpha_pinene_dw' => ['MT', 'apine dw', 'alpha-Pinene Dry Weight', 'ng/mg'],
    'camphene_dw' => ['MT', 'camphene dw', 'Camphene Dry Weight', 'ng/mg'],
    'beta_pinene_dw' => ['MT', 'bpine dw', 'beta-Pinene Dry Weight', 'ng/mg'],
    '3_carene_dw' => ['MT', 'tcarene dw', '3-Carene Dry Weight', 'ng/mg'],
    'myrcene_dw' => ['MT', 'myrc dw', 'Myrcene Dry Weight', 'ng/mg'],
    'alpha_terpinene_dw' => ['MT', 'aterpinene dw', 'alpha-Terpinene Dry Weight', 'ng/mg'],
    'limonene_dw' => ['MT', 'limo dw', 'Limonene Dry Weight', 'ng/mg'],
    'beta_phellandrene_dw' => ['MT', 'bphel dw', 'beta-Phellandrene Dry Weight', 'ng/mg'],
    'gamma_terpinene_dw' => ['MT', 'gterpinene dw', 'gamma-Terpinene Dry Weight', 'ng/mg'],
    'p_cymene_dw' => ['MT', 'pcyme', 'p-Cymene Dry Weight', 'ng/mg'],
    'terpineolene_dw' => ['MT', 'terpineolene dw', 'Terpinolene Dry Weight', 'ng/mg'],
    'bornyl_acetate_dw' => ['MT', 'bornylace dw', 'Bornyl Acetate Dry Weight', 'ng/mg'],
    '4_allylanisole_dw' => ['MT', 'ally dw', '4-Allylanisole Dry Weight', 'ng/mg'],
    'alpha_terpineol_dw' => ['MT', 'aterpineol dw', 'alpha-Terpineol Dry Weight', 'ng/mg'],
    'borneol_dw' => ['MT', 'borneol dw', 'Borneol Dry Weight', 'ng/mg'],
    'camphor_dw' => ['MT', 'camphor dw', 'Camphor Dry Weight', 'ng/mg'],
    'total_monoterpenes_dw' => ['MT', 'total_mono dw', 'Total Monoterpenes Dry Weight', 'ng/mg'],

    # Polyphenolic Analysis
    'ferulic_acid_glucoside' => ['PP', 'ferulic_acid_glucoside', 'Ferulic Acid Glucoside', 'AU'],
    'ferulic_acid_hexoside' => ['PP', 'ferulic_acid_hexoside', 'Ferulic Acid Hexoside', 'AU'],
    'ferulic_acid_hexoside_like_compound' => ['PP', 'ferulic_acid_hexoside_like_compound', 'Ferulic Acid Hexoside-Like Compound', 'AU'],
    'coumaric_acid_hexoside' => ['PP', 'coumaric_acid_hexoside', 'Coumaric Acid Hexoside', 'AU'],
    'ellagic_acid' => ['PP', 'ellagic_acid', 'Ellagic Acid', 'AU'],
    'hydroxypropiovanillone_hexoside' => ['PP', 'hydroxypropiovanillone_hexoside', 'Hydroxypropiovanillone Hexoside', 'AU'],
    'lignan_deoxyhexoside' => ['PP', 'lignan_deoxyhexoside', 'Lignan Deoxyhexoside', 'AU'],
    'lignan_derivative' => ['PP', 'lignan_derivative', 'Lignan Derivative', 'AU'],
    'lignan_xyloside' => ['PP', 'lignan_xyloside', 'Lignan Xyloside', 'AU'],
    'phenolic_hexoside' => ['PP', 'phenolic_hexoside', 'Phenolic Hexoside', 'AU'],
    'quercetin_acetate' => ['PP', 'quercetin_acetate', 'Quercetin Acetate', 'AU'],
    'taxifolin_hexoside' => ['PP', 'taxifolin_hexoside', 'Taxifolin Hexoside', 'AU'],
    'unknown_compound' => ['PP', 'Unknown_C', 'Unknown Compound [m/z315 nm280]', 'AU'],
    'vanillic_acid_hexoside' => ['PP', 'vanillic_acid_hexoside', 'Vanillic Acid Hexoside', 'AU'],
    'coumaric_acid_esters' => ['PP', 'coumaric_acid_esters', 'Coumaric Acid Esters', 'AU'],
    'taxifolin' => ['PP', 'taxifolin', 'Taxifolin', 'AU'],
    'taxifolin_glucoside' => ['PP', 'taxifolin_glucoside', 'Taxifolin Glucoside', 'AU'],
    'total_polyphenolics' => ['PP', 'total_phenolics', 'Total Phenolics', 'AU'],

    # Resin Duct Analysis
    'ten_year_mean_count' => ['RD', 'count_RD_10yr', 'Mean count of resin ducts for last 10 years', nil],
    'ten_year_mean_area' => ['RD', 'ave_RD_mm2_10yr', 'Average area of resin duts for last 10 years', 'mm2'],
    'ten_year_mean_tree_radial_growth' => ['RD', 'increment_mm10yr', 'Average radial growth of tree for last 10 years', 'mm'],
    'ten_year_mean_density' => ['RD', 'RDD_yr_10yr', 'Average resin duct density for last 10 years', 'cm-2'],
    'ten_year_mean_percent_area' => ['RD', 'pRDA_yr_10yr', 'Average percent resin duct area for last 10 years', '%'],

    # 13C
    'carbon_ratio' => ['TC', 'd13C.ave.vs.VPDB', 'Carbon Isotope Discrimination Ratio', nil],
    'carbon_content' => ['TC', 'fractionC', 'Total Carbon Content', nil],

    # Volumetric Water Content (GE)
    'date_measured_ge_PRE' => ['VG', 'VG Date Measured (PRE)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_PRE' => ['VG', 'Measured by DL (PRE)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_PRE' => ['VG', 'Sensor (PRE)', 'Sensor', nil],
    'vwc_ge_1_PRE' => ['VG', 'VWC.gasex.1 (PRE)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_PRE' => ['VG', 'VWC.gasex.2 (PRE)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_PRE' => ['VG', 'VWC.gasex.3 (PRE)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_PRE' => ['VG', 'VWC.gasex.4 (PRE)', 'Volumetric Water Content 4', nil],

    'date_measured_ge_DD1' => ['VG', 'VG Date Measured (DD1)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_DD1' => ['VG', 'Measured by DL (DD1)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_DD1' => ['VG', 'Sensor (DD1)', 'Sensor', nil],
    'vwc_ge_1_DD1' => ['VG', 'VWC.gasex.1 (DD1)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_DD1' => ['VG', 'VWC.gasex.2 (DD1)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_DD1' => ['VG', 'VWC.gasex.3 (DD1)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_DD1' => ['VG', 'VWC.gasex.4 (DD1)', 'Volumetric Water Content 4', nil],

    'date_measured_ge_R1' => ['VG', 'VG Date Measured (R1)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_R1' => ['VG', 'Measured by DL (R1)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_R1' => ['VG', 'Sensor (R1)', 'Sensor', nil],
    'vwc_ge_1_R1' => ['VG', 'VWC.gasex.1 (R1)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_R1' => ['VG', 'VWC.gasex.2 (R1)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_R1' => ['VG', 'VWC.gasex.3 (R1)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_R1' => ['VG', 'VWC.gasex.4 (R1)', 'Volumetric Water Content 4', nil],

    'date_measured_ge_DD2' => ['VG', 'VG Date Measured (DD2)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_DD2' => ['VG', 'Measured by DL (DD2)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_DD2' => ['VG', 'Sensor (DD2)', 'Sensor', nil],
    'vwc_ge_1_DD2' => ['VG', 'VWC.gasex.1 (DD2)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_DD2' => ['VG', 'VWC.gasex.2 (DD2)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_DD2' => ['VG', 'VWC.gasex.3 (DD2)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_DD2' => ['VG', 'VWC.gasex.4 (DD2)', 'Volumetric Water Content 4', nil],

    'date_measured_ge_R2' => ['VG', 'VG Date Measured (R2)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_R2' => ['VG', 'Measured by DL (R2)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_R2' => ['VG', 'Sensor (R2)', 'Sensor', nil],
    'vwc_ge_1_R2' => ['VG', 'VWC.gasex.1 (R2)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_R2' => ['VG', 'VWC.gasex.2 (R2)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_R2' => ['VG', 'VWC.gasex.3 (R2)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_R2' => ['VG', 'VWC.gasex.4 (R2)', 'Volumetric Water Content 4', nil],

    'date_measured_ge_DD3' => ['VG', 'VG Date Measured (DD3)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_DD3' => ['VG', 'Measured by DL (DD3)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_DD3' => ['VG', 'Sensor (DD3)', 'Sensor', nil],
    'vwc_ge_1_DD3' => ['VG', 'VWC.gasex.1 (DD3)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_DD3' => ['VG', 'VWC.gasex.2 (DD3)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_DD3' => ['VG', 'VWC.gasex.3 (DD3)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_DD3' => ['VG', 'VWC.gasex.4 (DD3)', 'Volumetric Water Content 4', nil],

    'date_measured_ge_DD3T1' => ['VG', 'VG Date Measured (DD3T1)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_DD3T1' => ['VG', 'Measured by DL (DD3T1)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_DD3T1' => ['VG', 'Sensor (DD3T1)', 'Sensor', nil],
    'vwc_ge_1_DD3T1' => ['VG', 'VWC.gasex.1 (DD3T1)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_DD3T1' => ['VG', 'VWC.gasex.2 (DD3T1)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_DD3T1' => ['VG', 'VWC.gasex.3 (DD3T1)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_DD3T1' => ['VG', 'VWC.gasex.4 (DD3T1)', 'Volumetric Water Content 4', nil],

    'date_measured_ge_DD3T2' => ['VG', 'VG Date Measured (DD3T2)', 'Date Measured', 'YYYY-MM-DD'],
    'measured_by_david_love_DD3T2' => ['VG', 'Measured by DL (DD3T2)', 'Measured by David Love', 'Y/N'],
    'sensor_ge_DD3T2' => ['VG', 'Sensor (DD3T2)', 'Sensor', nil],
    'vwc_ge_1_DD3T2' => ['VG', 'VWC.gasex.1 (DD3T2)', 'Volumetric Water Content 1', nil],
    'vwc_ge_2_DD3T2' => ['VG', 'VWC.gasex.2 (DD3T2)', 'Volumetric Water Content 2', nil],
    'vwc_ge_3_DD3T2' => ['VG', 'VWC.gasex.3 (DD3T2)', 'Volumetric Water Content 3', nil],
    'vwc_ge_4_DD3T2' => ['VG', 'VWC.gasex.4 (DD3T2)', 'Volumetric Water Content 4', nil],

    # Wood Quality
    'earlywood_density' => ['WQ', 'Earlywood Density', 'Earlywood Density', 'kg m-3'],
    'latewood_density' => ['WQ', 'Latewood Density', 'Latewood Density', 'kg m-3'],
    'avg_density' => ['WQ', 'Average Density', 'Average Density', 'kg m-3'],
  }
  CODE = 0
  HEADER = 1 # database name may be different from header due to unsuitability of header
  TITLE = 2
  UNIT = 3

  belongs_to :measurement, touch: true
  validates :measurement_id, presence: true
  validates :name, presence: true, length: { maximum: 255 }
  validates :value, presence: true, length: { maximum: 255 }

  validate :check_value_type

  def check_value_type
    if !value.blank?
      if TIME_ATTRIBUTES.include? name
        if !value.match?(/\A(?:(?:([01]?\d|2[0-3]):)?([0-5]?\d):)?([0-5]?\d)\z/)
          errors.add(:base, "#{MeasurementValue.title(name)} must be in HH:MM:SS (disallowed input: #{value})")
        end
      elsif DATE_ATTRIBUTES.include? name
        if !value.match?(/\A((19|20)\d\d)-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])\z/)
          errors.add(:base, "#{MeasurementValue.title(name)} must be in YYYY-MM-DD (disallowed input: #{value})")
        end
      elsif STRING_ATTRIBUTES.include? name
        # accept all input
      else
        if !value.match?(/\A[-+]?[0-9]*\.?[0-9]+\Z/)
          errors.add(:base, "#{MeasurementValue.title(name)} must be a number (disallowed input: #{value})")
        end
      end
    end
  end

  # Grabs title of measurement value (without units) (e.g. for validation messages and version control)
  def self.title(db_name)
    MV_MASTER[db_name][TITLE]
  end

  # Grabs descriptive full title of measurement value (with units) (e.g. for views)
  def self.full_title(db_name)
    unit = MV_MASTER[db_name][UNIT]

    if unit == nil
      return "#{MV_MASTER[db_name][TITLE]}"
    else
      return "#{MV_MASTER[db_name][TITLE]} (#{MV_MASTER[db_name][UNIT]})"
    end
  end

  # Grabs header measurement value (with units) chosen by the researcher (e.g. for headers)
  def self.header(db_name)
    unit = MV_MASTER[db_name][UNIT]

    if unit == nil
      return "#{MV_MASTER[db_name][HEADER]}"
    else
      return "#{MV_MASTER[db_name][HEADER]} (#{MV_MASTER[db_name][UNIT]})"
    end
  end

  # Grabs header measurement value (without units) chosen by the researcher (e.g. for queries)
  def self.map_to_db(header)
    header = header.downcase

    MV_MASTER.each do |key, value|
      if value[HEADER].downcase == header
        return key
      end
    end

    return header
  end

  # Grabs attribute array of measurement value (for advanced search)
  def self.attribute_string(db_name)
    "Measurement-#{MV_MASTER[db_name][CODE]}, #{MV_MASTER[db_name][CODE]}_#{db_name}_value, #{header(db_name)}"
  end

  # Grabs symbol to retrieve measurement value (for show and form views)
  def self.symbol(db_name)
    "#{MV_MASTER[db_name][CODE]}_#{db_name}".to_sym
  end

  # Grabs function to retrieve value of measurement value (for show and form views)
  def self.value_function(db_name)
    "#{MV_MASTER[db_name][CODE]}_#{db_name}_value"
  end

  # For advanced search
  def self.ransackable_attributes(auth_object = nil)
    ['name', 'value']
  end
end
