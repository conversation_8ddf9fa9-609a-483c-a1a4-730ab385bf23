class CSVExport

  def self.generate(list, columns, ages)
    measurement_types_hash = get_measurement_types_hash
    empty_headers_hash = get_empty_headers_hash(columns, ages)
    models = get_models_list(columns)
    measurement_attributes_wanted = ['description', 'performed_on'] # specify list of attributes wanted from measurements table

    # Use enumerator to stream CSV (line by line)
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(get_headers(columns, ages, empty_headers_hash)) # headers

      # Each tree is one row in the CSV file
      list.select('trees.*').each do |tree|
        row = []

        # Store preloaded database queries in hashes
        characteristics_hash = get_characteristics_hash(tree)
        measurements_hash = get_measurements_hash(tree, measurement_types_hash)
        measurement_values_hash = get_measurement_values_hash(tree)

        # Preload
        if models.include? "Mother"
          mother_tree = tree.mother_parent_tree
        end

        # Deal with each column
        columns.each do |column|
          if get_model_name(column) == 'Characteristic'
            ages.each do |age|
              key_name = "#{get_attribute_name(column)}_#{age}"

              if empty_headers_hash[key_name.to_sym] == true
                row.push(get_characteristic_data(age, characteristics_hash, column))
              end
            end
          elsif get_model_name(column).include? 'Measurement'
            row.push(get_measurement_data(measurement_attributes_wanted, measurements_hash, measurement_values_hash, column))
          elsif get_model_name(column) == 'Mother'
            if mother_tree
              row.push(mother_tree.send(get_attribute_name(column)))
            else
              row.push(nil)
            end
          elsif get_display_name(column).include? 'Tree SUC Code'
            row.push(tree.send(get_attribute_name(column)))
          else 
            row.push(get_tree_data(column, tree))
          end
        end
        yielder << CSV.generate_line(row)
      end
    end
  end

  private

  def self.get_model_name(attribute_string)
    return attribute_string.split(/\s*,\s*/)[0]
  end

  def self.get_measurement_type(attribute_string)
    return attribute_string.split(/\s*,\s*/)[0].split('-')[1]
  end

  def self.get_attribute_name(attribute_string)
    return attribute_string.split(/\s*,\s*/)[1]
  end

  def self.get_display_name(attribute_string)
    return attribute_string.split(/\s*,\s*/)[2]
  end

  # Grab headers for CSV export given the desired columns and the included ages
  def self.get_headers(columns, ages, empty_headers_hash)
    headers = []

    columns.each do |column|
      if get_model_name(column) == 'Characteristic'
        ages.each do |age|
          key_name = "#{get_attribute_name(column)}_#{age}"

          if empty_headers_hash[key_name.to_sym] == true
            headers.push("#{get_display_name(column)} at Age #{age}")
          end
        end
      else
        headers.push(get_display_name(column).gsub('μ', 'u'))
      end
    end

    return headers
  end

  # Create a hash to determine which characteristic columns are empty
  def self.get_empty_headers_hash(columns, ages)
    empty_headers_hash = {}

    columns.each do |column|
      if get_model_name(column) == 'Characteristic'
        ages.each do |age|
          key_name = "#{get_attribute_name(column)}_#{age}"

          # Check if there is any data of an attribute for each age
          empty_headers_hash[key_name.to_sym] = Characteristic.where("age_recorded = #{age} AND #{get_attribute_name(column)} IS NOT NULL").exists?
        end
      end
    end

    return empty_headers_hash
  end

  # Get a list of models in the query for pre-loading
  def self.get_models_list(columns)
    models = []

    columns.each do |column|
      if !models.include? get_model_name(column)
        models.push(get_model_name(column))
      end
    end

    return models
  end

  # Store measurement_types as hash to reduce database queries
  def self.get_measurement_types_hash
    measurement_types_hash = {}

    # key:value (id:code)
    MeasurementType.find_each do |type|
      measurement_types_hash[type.id] = type.code
    end

    return measurement_types_hash
  end

  # Store characteristics as hash to reduce database queries for CSV export
  def self.get_characteristics_hash(tree)
    characteristics_hash = {}
    characteristics = tree.characteristics
    
    characteristics.each do |characteristic|
      characteristics_hash.store(characteristic.age_recorded, characteristic)
    end

    return characteristics_hash
  end

  # Store measurements as hash to reduce database queries for CSV export
  def self.get_measurements_hash(tree, measurement_types_hash)
    measurements_hash = {}
    measurements = tree.measurements
    
    measurements.each do |measurement|
      measurement_code = measurement_types_hash[measurement.measurement_type_id]
      measurements_hash.store(measurement_code.to_sym, measurement)
    end

    return measurements_hash
  end

  # Store measurement values as hash to reduce database queries for CSV export
  def self.get_measurement_values_hash(tree)
    measurement_values_hash = {}
    measurement_values = tree.measurement_values
    
    measurement_values.each do |measurement_value|
      measurement_values_hash.store("#{measurement_value.name}".to_sym, measurement_value.value)
    end

    return measurement_values_hash
  end

  # Grab characteristic data for CSV export
  def self.get_characteristic_data(age, characteristics_hash, column)
    characteristic = characteristics_hash[age]

    if characteristic.blank?
      return nil
    else
      if get_attribute_name(column) == 'condition_code'
        return Tree.format_code(characteristic.condition_code)
      else
        return characteristic.send(get_attribute_name(column))
      end
    end
  end

  # Grab measurement data for CSV export
  def self.get_measurement_data(measurement_attributes_wanted, measurements_hash, measurement_values_hash, column)
    measurement = measurements_hash[get_measurement_type(column).to_sym]
  
    if measurement == nil
      return nil
    elsif measurement_attributes_wanted.include? get_attribute_name(column)
      if get_attribute_name(column) == "performed_on"
        return measurement.performed_on_range
      else
        return measurement.send(get_attribute_name(column))
      end
    else
      attribute_name = get_attribute_name(column).gsub("#{get_measurement_type(column)}_",'').gsub('_value','')
      return measurement_values_hash[attribute_name.to_sym]
    end
  end

  # Grab tree data for CSV export
  def self.get_tree_data(column, tree)
    if get_attribute_name(column) == 'res_for_label'
      return tree.res_for_label
    elsif get_model_name(column) == 'Source'
      return tree.source.attributes[get_attribute_name(column)]
    elsif get_model_name(column) == 'Species'
      return tree.species.attributes[get_attribute_name(column)]
    else
      return tree.attributes[get_attribute_name(column)]
    end
  end

end
