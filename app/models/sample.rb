class Sample < ApplicationRecord
  SAMPLE_TYPES = ['increment core', 'needles', 'phloem', 'resin duct core', 'whole plant', 'soil'].freeze

  has_paper_trail on: [:update], ignore: [:tag_list]
  acts_as_taggable_on :tags
  
  scope :location_cont,
    ->(name) { where('building LIKE :name OR room LIKE :name', name: "%#{name}%") }

  scope :non_children, -> { where(sample_id: nil).order("suffix") }

  belongs_to :tree, inverse_of: :samples
  belongs_to :sample, optional: true, inverse_of: :samples
  belongs_to :site, inverse_of: :samples

  belongs_to :collected_by, class_name: 'User', optional: true, inverse_of: :samples
  belongs_to :last_user_updated, class_name: 'User', inverse_of: :samples
  belongs_to :protocol, optional: true, inverse_of: :samples

  has_many :samples, -> { order(suffix: :asc) }, dependent: :destroy
  has_many :measurements, dependent: :destroy

  has_many :grouping_assignments, as: :assignable, dependent: :destroy
  has_many :groupings, through: :grouping_assignments

  has_many :stored_files, as: :attachable
  accepts_nested_attributes_for :stored_files, allow_destroy: true

  before_validation :assign_parent_type, :set_tree_id

  validates :original_amount,
    numericality: { greater_than_or_equal_to: 0 }, allow_blank: true
  validates :actual_amount,
    numericality: { greater_than_or_equal_to: 0 }, allow_blank: true
  validates :sample_type, presence: true, inclusion: { in: SAMPLE_TYPES }
  validates :suffix, presence: true, uniqueness: { scope: :tree_id }
  validates_format_of :collected_on_before_type_cast, :collected_on_end_before_type_cast,
    with: /\A((19|20)\d\d)-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])\z/,
    message: "must be in YYYY-MM-DD format", allow_blank: true

  validate :same_parent?
  validate :has_tree_code?, :has_unique_sample_code?, on: :create

  before_save :construct_sample_code, :match_parent, :update_history

  attr_accessor :tree_code

  def self.sample_types
    SAMPLE_TYPES
  end

  def self.ransackable_scopes(auth_object = nil)
    %i(location_cont)
  end

  # Required so that Measurements, Samples, and Trees can be displayed in groupings
  def to_s
    #{}"#{self.code.blank? ? 'No code' : self.code} (#{sample_type.blank? ? 'No sample type' : sample_type.capitalize} - #{original_amount.blank? ? 'Unknown Amount' : "#{original_amount} #{original_unit}"})"
    "#{self.code} (#{sample_type})"
  end

  def to_label
    to_s
  end

  # sample takes precedence over tree
  def parent
    sample || tree
  end

  def aliquot?
    sample.present?
  end

  def root_sample
    aliquot? ? sample.root_sample : self
  end

  def root
    tree
  end

  # Returns true if this sample contains at least one sample location value
  def location_information?
    %i(site building room freezer shelf box box_position).detect { |l| send(l).present? }
  end

  # Calculates the theoretical amount of this sample
  # = original amount - sum(original amount of each child)
  # assumes that original_unit for this sample and all children are the same
  def theoretical_amount
    theoretical = original_amount.to_f
    samples.each do |child|
      theoretical -= child.original_amount.to_f
    end
    theoretical
  end

  # Calculated from remaining amount and child samples
  def calculate_original_amount
    total = actual_amount.to_f

    samples.each do |child|
      total += child.original_amount.to_f
    end

    self.original_amount = total
  end

  # updates the last user who made updates
  def update_history
    if self.changed?
      if User.current_user != nil
        self.last_user_updated_id = User.current_user.id
      end
    end
  end

  # appends the tree code and the user-entered code to make the sample code
  def construct_sample_code
    if @tree_code.present?
      self.code = Tree.find_by(id: @tree_code).code.to_s + '-' + suffix
    else
      self.code = tree.code.to_s + '-' + suffix
    end
  end

  # if sub-sample, grabs info from parent (if own info not already set)
  def match_parent
    if parent.is_a? Sample
      self.collected_by_id = parent.collected_by_id unless self.collected_by_id
      self.site_id = parent.site_id unless self.site_id
      self.collected_on = parent.collected_on unless self.collected_on
      self.collected_on_end = parent.collected_on_end unless self.collected_on_end
    end
  end

  # appends collected_on and collected_on_end to create a range (YYYY-MM-DD-DD)
  def collected_on_range
    if collected_on_end.present? && collected_on != collected_on_end
      return "#{collected_on}-#{collected_on_end.strftime("%d")}"
    else
      return collected_on
    end
  end

  # Allow LIKE search for dates
  ransacker :collected_on do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(samples.collected_on, "%Y-%m-%d %H:%M:%S")')
  end

  private

  def assign_parent_type
    self.sample_type = sample.sample_type if sample.present?
  end

  # If tree is nil, sets the tree to that of parent sample (if parent sample exists)
  # Else find tree id from given tree code
  def set_tree_id
    if tree_id.blank? && sample.present?
      self.tree_id = sample.tree_id
    elsif tree_id.blank? && @tree_code.present?
      self.tree_id = @tree_code
    end
  end

  def same_parent?
    if sample.present? && sample.tree_id != tree_id
      errors.add(:tree_id, "is not the same as that of parent sample")
    end
  end

  def has_tree_code?
    if !tree.present?
      if !tree_code.present?
        errors.add(:tree_code, "can't be blank")
      end
    end
  end

  # This check only necessary with user-entered trees (otherwise other validation kicks in)
  def has_unique_sample_code?
    if !tree.present? && suffix.present?
      if Sample.where(code: "#{Tree.find_by(id: @tree_id).code}-#{suffix}").exists?
        errors.add(:suffix, "has already been taken")
      end
    end
  end
end
