class Grouping < ApplicationRecord
  has_many :grouping_assignments, dependent: :destroy

  validates_presence_of :name, :type

  # Get a list of possible grouping subclasses
  def self.valid_types
    ['Tree', 'Sample', 'Measurement']
  end

  def self.factory(type, params = nil)
    "#{type}Grouping".constantize.new(params)
  end

  def assignable_type
    self.class.to_s.sub(/Grouping$/, '')
  end

  def to_s
    name
  end
end
