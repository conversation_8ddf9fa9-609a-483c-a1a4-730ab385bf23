class AnCoolGastonGe3lsFile < ApplicationRecord
  acts_as_taggable_on :tags

  belongs_to :user

  has_attached_file :content
  do_not_validate_attachment_file_type :content
  validates_with AttachmentPresenceValidator, attributes: :content

  # Allow LIKE search for dates
  ransacker :created_at do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(an_cool_gaston_ge3ls_files.created_at, "%Y-%m-%d %H:%M:%S")')
  end
end
