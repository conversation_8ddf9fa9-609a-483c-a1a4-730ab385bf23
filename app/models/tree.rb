class Tree < ApplicationRecord
  acts_as_taggable_on :tags

  belongs_to :species, inverse_of: :trees
  belongs_to :source, inverse_of: :trees

  belongs_to :mother_parent_tree, :class_name => 'ParentTree'
  belongs_to :father_parent_tree, :class_name => 'ParentTree'

  belongs_to :last_user_updated, class_name: 'User', optional: true, inverse_of: :trees

  has_many :samples, -> { order(suffix: :asc) }, dependent: :destroy
  accepts_nested_attributes_for :samples, allow_destroy: true
  has_many :measurements, through: :samples
  has_many :measurement_types, through: :measurements
  has_many :measurement_values, through: :measurements

  has_many :child_samples, -> { where('sample_id IS NULL') }, class_name: 'Sample'

  has_many :grouping_assignments, as: :assignable, dependent: :destroy
  has_many :groupings, through: :grouping_assignments

  has_many :characteristics, -> { order(age_recorded: :asc) }, dependent: :destroy

  has_many :cohorts_trees, dependent: :destroy
  has_many :cohorts, through: :cohorts_trees

  validates :code, presence: true, uniqueness: true
  validates :source, :species, :family, presence: true
  validate :species_matches_site
  # validates_numericality_of :code, greater_than: 0, only_integer: true
  validates_numericality_of :rep, :row, :tier, :set, :stake, :tree, greater_than: 0, allow_blank: true, only_integer: true
  validates_numericality_of :x_coordinate, :y_coordinate, only_integer: true, allow_blank: true
  validates :breeding_value_rank, numericality: {only_integer: true, greater_than_or_equal_to: 1, less_than_or_equal_to: 4}, allow_blank: true
  validates :label_present, inclusion: { in: [ 'Y', 'N' ] }, allow_blank: true
  validates :res_for_tree, inclusion: { in: [ true, false ] }

  before_save :update_history
  after_create :assign_tree_to_group

  scope :res_for, -> { where(res_for_tree: true) }
  scope :non_res_for, -> { where(res_for_tree: false) }

  def self.title
    Tree.model_name.human
  end

  def name
    "Plot #{code}"
  end

  def to_s
    code.to_s
  end

  def to_label
    code.to_s
  end

  # check that the species selected is grown at the site selected
  def species_matches_site
    if source != nil && species != nil
      source_code = self.source.code

      if source_code != "GH"
        if ['JUDY', 'SWAN', 'TIME', 'VIRG'].include? source_code
          if species_id != Species.find_by(code: "P").id
            errors.add(:species, 'is not found at selected site')
          end
        elsif ['CALL', 'CARS', 'REDE'].include? source_code
          if species_id != Species.find_by(code: "S").id
            errors.add(:species, 'is not found at selected site')
          end
        end
      end
    end
  end

  # check that forward_selected is not null for RES-FOR field trees
  def forward_selected_cannot_be_null
    if res_for_tree
      if self.source.code != "GH"
        if forward_selected == nil
          errors.add(:forward_selected, 'can\'t be blank')
        else
          if !(forward_selected == 'Y' || forward_selected == 'N')
            errors.add(:forward_selected, 'must be Y or N')
          end
        end
      end
    end
  end

  # assign tree to group
  def assign_tree_to_group
    grouping_assignment = GroupingAssignment.find_by(assignable_type: 'Tree', assignable_id: id)

    if grouping_assignment == nil
      if ['CALL', 'CARS', 'JUDY', 'REDE', 'SWAN', 'TIME', 'VIRG'].include? source.code
        if res_for_tree
          GroupingAssignment.create(grouping_id: Grouping.find_by(name: "Field").id, assignable_type: "Tree", assignable_id: id)
        else
          GroupingAssignment.create(grouping_id: Grouping.find_by(name: "Non-RES-FOR Trees").id, assignable_type: "Tree", assignable_id: id)
        end
      elsif ['GH'].include? source.code
        GroupingAssignment.create(grouping_id: Grouping.find_by(name: "Greenhouse").id, assignable_type: "Tree", assignable_id: id)
      end
    end
  end

  # generates the tree's RES-FOR label
  def res_for_label
    if res_for_tree # Field
      # Format family code
      if family[0] == '0'
        family_code = family[1..-1]
      else
        family_code = family
      end

      if source.code != 'GH'
        if forward_selected == 'Y'
          fs_code = 'FS'
        elsif forward_selected == 'N'
          fs_code = '-'
        else
          fs_code = nil
        end

        return "#{fs_code}-#{species.code}-#{source.code}-#{family_code}-#{code}"
      else # Greenhouse
        "#{species.code}-#{source.code}-#{treatment}-#{family_code}-#{code}-#{block}"
      end
    else
      return nil
    end
  end

  # displays SUC code for the mother tree
  def mother_tree
    if suc_code.present?
      if mother_parent_tree.present?
        return mother_parent_tree
      else
        return 0
      end
    end
  end

  # displays SUC code for mother tree
  def father_tree
    if suc_code.present?
      if father_parent_tree.present?
        return father_parent_tree
      else
        return 0
      end
    end
  end

  # modify condition code for view
  def self.format_code(code)
    formatted_code = ''
    
    code.each do |code|
      if code.length > 0
        formatted_code += code + ','
      end
    end

    return formatted_code.chomp(",")
  end

  private

  # for bulk importing trees
  def self.import(file_id)
    file = GeneralFile.find_by(id: file_id)
    current_tree = nil
    options = {
      headers_in_file: true, 
      user_provided_headers: %i(species_id source_id trial family code rep row tier set stake tree x_coordinate y_coordinate forward_selected breeding_value_rank label_present, res_for_tree),
      remove_zero_values: false,
      convert_values_to_numeric: false
    }

    begin
      SmarterCSV.process(file.content.path, options) do |array|
        tree_hash = array.first
        current_tree = tree_hash[:code]

        # Post-processing
        tree_hash[:species_id] = self.format_species(tree_hash[:species_id]) # replaces species code with species id
        tree_hash[:source_id] = self.format_source(tree_hash[:source_id]) # replace source code with id
        tree_hash[:family] = self.format_family(tree_hash[:family]) # appends zeros in front of 3-digit family codes
        tree_hash[:forward_selected] = self.format_boolean_word(tree_hash[:forward_selected])
        tree_hash[:label_present] = self.format_boolean_word(tree_hash[:label_present])
        tree_hash[:res_for_tree] = self.convert_to_boolean(tree_hash[:res_for_tree])

        # Create or update tree
        if !tree = Tree.find_by(:code => tree_hash[:code].to_s)
          Tree.create! tree_hash
        else
          tree.update! tree.attributes.merge(tree_hash.compact)
        end
      end

    rescue Exception => e
      if e.class.to_s == "SmarterCSV::HeaderSizeMismatch"
        return [false, "Headers in input file must match the headers in the template."]
      elsif e.class.to_s == 'ArgumentError'
        return [false, "Input file must be a CSV."]
      elsif e.class.to_s == 'ActiveRecord::RecordInvalid'
        return [false, "Error importing Tree #{current_tree}. #{e}. All subsequent trees were not imported."]
      else
        return [false, "#{e.class.to_s}"]
      end
    end

    return [true, 'Trees were successfully imported']
  end

  # for parsing CSV during bulk upload -> formats 3-digit family codes to 4-digit
  def self.format_family(family)
    if !family.blank?
      family.strip!
      if family.length == 3
        return '0' + family
      else
        return family
      end
    end
  end

  # for parsing CSV during bulk upload -> changes species codes
  def self.format_species(species)
    if !species.blank?
      species.strip!
      if species == 'P'
        return 1 # Lodgepole Pine code
      elsif species == 'S'
        return 2 # White Spruce code
      else
        return species
      end
    end
  end

  # for parsing CSV during bulk upload -> changes source code
  def self.format_source(source)
    if !source.blank?
      source.upcase.strip!
      if source == 'CALL'
        return 1
      elsif source == 'CARS'
        return 2
      elsif source == 'JUDY'
        return 3
      elsif source == 'REDE'
        return 4
      elsif source == 'SWAN'
        return 5
      elsif source == 'TIME'
        return 6
      elsif source == 'VIRG'
        return 7
      elsif source == 'GH'
        return 8
      else
        return source
      end
    end
  end

  # Allow LIKE search for integers
  ransacker :code do
    Arel.sql("CONVERT(trees.code, CHAR(8))")
  end

  # for advanced search
  def self.ransackable_attributes(auth_object = nil)
    ['trial', 'treatment', 'family', 'code', 'rep', 'row', 'tier', 'set', 'stake', 'tree', 'x_coordinate', 'y_coordinate', 'block', 'forward_selected', 'breeding_value_rank', 'label_present', 'res_for_tree']
  end

  # updates the last user who made updates
  def update_history
    if self.changed?
      if User.current_user != nil
        self.last_user_updated_id = User.current_user.id
      end
    end
  end
end
