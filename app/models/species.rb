class Species < ApplicationRecord
  has_many :trees, dependent: :restrict_with_error
  has_many :parent_trees, dependent: :restrict_with_error

  validates :name, presence: true, uniqueness: true

   # for advanced search
  def self.ransackable_attributes(auth_object = nil)
    ['short_code']
  end

  # necessary because of "Incorrect type definition in case of models with identical attributes names"
  ransacker :short_code do
    Arel.sql('species.code')
  end
end
