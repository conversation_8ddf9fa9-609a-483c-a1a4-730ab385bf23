class User < ApplicationRecord
  devise :invitable, :database_authenticatable, :recoverable,
    :rememberable, :trackable, :validatable, :lockable

  enum status: %i(inactive active).freeze
  enum role: %i(researcher administrator).freeze

  belongs_to :site

  has_one :photo_file, class_name: 'StoredFile::Photo', as: :attachable
  accepts_nested_attributes_for :photo_file, allow_destroy: true

  has_many :tasks, foreign_key: :assigned_to_id
  has_many :samples, foreign_key: :collected_by_id
  has_many :samples, foreign_key: :last_user_updated_id
  has_many :trees, foreign_key: :last_user_updated_id
  has_many :characteristics, foreign_key: :last_user_updated_id
  has_many :measurements, foreign_key: :last_user_updated_id

  validates :name, presence: true

  cattr_accessor :current_user # in order to access current_user within models

  def skip_password_validation!
    @skip_password_validation = true
  end

  def to_s
    self.email
  end

  def active_for_authentication?
    super && active?
  end

  protected

  # Overloading the devise method here to allow us to create users that
  # have no password, and have been invited by an admin.
  def password_required?
    return false if @skip_password_validation
    super
  end
end
