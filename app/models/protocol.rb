class Protocol < ApplicationRecord
  has_attached_file :storage,
    url: "/system/:attachment/:id/:filename",
    path: ":rails_root/public/system/:attachment/:id/:filename"
  do_not_validate_attachment_file_type :storage

  has_many :measurements
  validates_presence_of :name
  validates_presence_of :storage

  def to_label
    "#{self.name} (version #{self.version})"
  end

  # Allow LIKE search for dates
  ransacker :created_at do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(protocols.created_at, "%Y-%m-%d %H:%M:%S")')
  end
end
