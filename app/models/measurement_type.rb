class MeasurementType < ApplicationRecord
  has_many :measurements

  validates_presence_of :name
  validates_presence_of :code

  # for advanced search
  def self.ransackable_attributes(auth_object = nil)
    ['name', 'short_code']
  end

   # necessary because of "Incorrect type definition in case of models with identical attributes names"
  ransacker :short_code do
    Arel.sql('measurement_types.code')
  end
end
