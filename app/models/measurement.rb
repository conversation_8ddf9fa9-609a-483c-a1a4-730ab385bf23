class Measurement < ApplicationRecord
  has_paper_trail on: [:update], ignore: [:tag_list]

  MEASUREMENT_VALUE_TYPES = {
    BM: %w(needle_ww stem_ww root_ww above_ground_ww total_biomass_ww needle_dw stem_dw root_dw above_ground_dw total_biomass_dw),
    GE: %w(phase_ge time_recorded instrument_num photosynthetic_rate stomatal_conductance transpiration_rate intercellular_co2_conc co2_ratio intrinsic_water_use_efficiency vapour_pressure_defecit relative_humidity leaf_temperature),
    GG: %w(height_pre1 height_pre2 height_r1 height_r2 height_f rcd_pre1 rcd_pre2 rcd_r1 rcd_r2 rcd_f),
    IR: %w(limonene_percentage mpb_resistance_rank),
    LL: %w(date_inoculated length_at_2 length_at_7 length_at_12 avg_length),
    MA: %w(microfibril_angle),
    MB: %w(lithium_FIELD boron_FIELD sodium_FIELD magnesium_FIELD aluminum_FIELD phosphorus_FIELD potassium_FIELD calcium_FIELD vanadium_FIELD manganese_FIELD iron_FIELD nickel_FIELD cobalt_FIELD copper_FIELD zinc_FIELD arsenic_FIELD selenium_FIELD rubidium_FIELD strontium_FIELD cadmium_FIELD tellurium_FIELD cesium_FIELD barium_FIELD lanthanum_FIELD thallium_FIELD gallic_acid_FIELD gallocatchin_FIELD 3_4_dihydroxybenzoic_acid_FIELD catechin_FIELD protocatecuic_aldehyde_FIELD pungenol_FIELD caffeic_acid_FIELD vanillic_acid_FIELD vanillin_FIELD piceol_FIELD taxifolin_FIELD p_coumaric_acid_FIELD ferulic_acid_FIELD myricetin_FIELD quercetin_FIELD naringenin_FIELD kampferol_FIELD apigenin_FIELD isorhamnetin_FIELD lithium_PRE boron_PRE sodium_PRE magnesium_PRE aluminum_PRE phosphorus_PRE potassium_PRE calcium_PRE vanadium_PRE manganese_PRE iron_PRE nickel_PRE cobalt_PRE copper_PRE zinc_PRE arsenic_PRE selenium_PRE rubidium_PRE strontium_PRE cadmium_PRE tellurium_PRE cesium_PRE barium_PRE lanthanum_PRE thallium_PRE gallic_acid_PRE gallocatchin_PRE 3_4_dihydroxybenzoic_acid_PRE catechin_PRE protocatecuic_aldehyde_PRE pungenol_PRE caffeic_acid_PRE vanillin_PRE piceol_PRE taxifolin_PRE p_coumaric_acid_PRE ferulic_acid_PRE myricetin_PRE quercetin_PRE naringenin_PRE kampferol_PRE apigenin_PRE isorhamnetin_PRE lithium_DD2 boron_DD2 sodium_DD2 magnesium_DD2 aluminum_DD2 phosphorus_DD2 potassium_DD2 calcium_DD2 vanadium_DD2 manganese_DD2 iron_DD2 nickel_DD2 cobalt_DD2 copper_DD2 zinc_DD2 arsenic_DD2 selenium_DD2 rubidium_DD2 strontium_DD2 cadmium_DD2 tellurium_DD2 cesium_DD2 barium_DD2 lanthanum_DD2 thallium_DD2 gallic_acid_DD2 gallocatchin_DD2 3_4_dihydroxybenzoic_acid_DD2 catechin_DD2 protocatecuic_aldehyde_DD2 pungenol_DD2 caffeic_acid_DD2 vanillin_DD2 piceol_DD2 taxifolin_DD2 p_coumaric_acid_DD2 ferulic_acid_DD2 myricetin_DD2 quercetin_DD2 naringenin_DD2 kampferol_DD2 apigenin_DD2 isorhamnetin_DD2 lithium_HAR boron_HAR sodium_HAR magnesium_HAR aluminum_HAR phosphorus_HAR potassium_HAR calcium_HAR vanadium_HAR manganese_HAR iron_HAR nickel_HAR cobalt_HAR copper_HAR zinc_HAR arsenic_HAR selenium_HAR rubidium_HAR strontium_HAR cadmium_HAR tellurium_HAR cesium_HAR barium_HAR lanthanum_HAR thallium_HAR gallic_acid_HAR gallocatchin_HAR 3_4_dihydroxybenzoic_acid_HAR catechin_HAR protocatecuic_aldehyde_HAR pungenol_HAR caffeic_acid_HAR vanillin_HAR piceol_HAR taxifolin_HAR p_coumaric_acid_HAR ferulic_acid_HAR myricetin_HAR quercetin_HAR naringenin_HAR kampferol_HAR apigenin_HAR isorhamnetin_HAR),
    MP: %w(choline_FIELD betaine_FIELD creatinine_FIELD tmao_FIELD creatine_FIELD proline_betaine_FIELD zeatin_FIELD proline_FIELD carnosine_FIELD histidine_FIELD histamine_FIELD methylhistidine_FIELD arginine_FIELD taurine_FIELD total_dma_FIELD trans_hydroxyproline_FIELD glutamine_FIELD asparagine_FIELD citrulline_FIELD methionine_sulfoxide_FIELD serine_FIELD acetylornithine_FIELD glycine_FIELD glutamic_acid_FIELD aspartic_acid_FIELD sarcosine_FIELD threonine_FIELD alpha_aaa_FIELD dopa_FIELD alanine_FIELD tyrosine_FIELD dopamine_FIELD serotonin_FIELD methonine_FIELD valine_FIELD tyramine_FIELD ornithine_FIELD kynurenine_FIELD lysine_FIELD nitro_tyr_FIELD tryptophan_FIELD xleu_FIELD phenylalanine_FIELD putrescine_FIELD pea_FIELD spermidine_FIELD spermine_FIELD glucose_FIELD carnitine_FIELD lysoc14_0_FIELD lysoc16_0_FIELD lysoc18_2_FIELD lysoc18_1_FIELD lysoc18_0_FIELD pc_aa_c36_6_FIELD pc_aa_c36_0_FIELD pc_aa_c38_6_FIELD pc_aa_c38_0_FIELD pc_aa_c40_6_FIELD pc_aa_c32_0_FIELD pc_ae_c34_2_FIELD pc_ae_c34_1_FIELD pc_aa_c34_4_FIELD pc_aa_c34_3_FIELD pc_aa_c34_2_FIELD pc_aa_c34_1_FIELD pc_ae_c36_3_FIELD pc_ae_c36_2_FIELD pc_ae_c36_1_FIELD pc_aa_c36_5_FIELD pc_aa_c36_4_FIELD pc_aa_c36_3_FIELD pc_aa_c36_2_FIELD pc_aa_c36_1_FIELD pc_ae_c38_4_FIELD pc_ae_c38_3_FIELD pc_ae_c38_2_FIELD pc_ae_c38_0_FIELD pc_aa_c38_5_FIELD pc_aa_c38_4_FIELD pc_aa_c38_3_FIELD pc_aa_c38_1_FIELD pc_ae_c40_4_FIELD pc_ae_c40_3_FIELD pc_aa_c40_5_FIELD pc_ae_c42_5_FIELD shikimic_acid_FIELD glyceric_acid_FIELD beta_hydroxybutyric_acid_FIELD lactic_acid_FIELD propionic_acid_FIELD malic_acid_FIELD butyric_acid_FIELD hippuric_acid_FIELD succinic_acid_FIELD glutaric_acid_FIELD fumaric_acid_FIELD valeric_acid_FIELD benzoic_acid_FIELD oxalic_acid_FIELD salicylic_acid_FIELD citric_acid_FIELD aconitic_acid_FIELD pyruvic_acid_FIELD alpha_ketoglutaric_acid_FIELD choline_PRE betaine_PRE creatinine_PRE tmao_PRE creatine_PRE proline_betaine_PRE zeatin_PRE proline_PRE carnosine_PRE histidine_PRE histamine_PRE methylhistidine_PRE arginine_PRE taurine_PRE total_dma_PRE trans_hydroxyproline_PRE glutamine_PRE asparagine_PRE citrulline_PRE methionine_sulfoxide_PRE serine_PRE acetylornithine_PRE glycine_PRE glutamic_acid_PRE aspartic_acid_PRE sarcosine_PRE threonine_PRE alpha_aaa_PRE dopa_PRE alanine_PRE tyrosine_PRE dopamine_PRE serotonin_PRE methonine_PRE valine_PRE tyramine_PRE ornithine_PRE kynurenine_PRE lysine_PRE nitro_tyr_PRE tryptophan_PRE xleu_PRE phenylalanine_PRE putrescine_PRE pea_PRE spermidine_PRE spermine_PRE glucose_PRE carnitine_PRE lysoc14_0_PRE lysoc16_0_PRE lysoc18_2_PRE lysoc18_1_PRE lysoc18_0_PRE pc_aa_c36_6_PRE pc_aa_c36_0_PRE pc_aa_c38_6_PRE pc_aa_c38_0_PRE pc_aa_c40_6_PRE pc_aa_c32_0_PRE pc_ae_c34_2_PRE pc_ae_c34_1_PRE pc_aa_c34_4_PRE pc_aa_c34_3_PRE pc_aa_c34_2_PRE pc_aa_c34_1_PRE pc_ae_c36_3_PRE pc_ae_c36_2_PRE pc_ae_c36_1_PRE pc_aa_c36_5_PRE pc_aa_c36_4_PRE pc_aa_c36_3_PRE pc_aa_c36_2_PRE pc_aa_c36_1_PRE pc_ae_c38_4_PRE pc_ae_c38_3_PRE pc_ae_c38_2_PRE pc_ae_c38_0_PRE pc_aa_c38_5_PRE pc_aa_c38_4_PRE pc_aa_c38_3_PRE pc_aa_c38_1_PRE pc_ae_c40_4_PRE pc_ae_c40_3_PRE pc_aa_c40_5_PRE pc_ae_c42_5_PRE shikimic_acid_PRE glyceric_acid_PRE beta_hydroxybutyric_acid_PRE lactic_acid_PRE propionic_acid_PRE malic_acid_PRE butyric_acid_PRE hippuric_acid_PRE succinic_acid_PRE glutaric_acid_PRE fumaric_acid_PRE valeric_acid_PRE benzoic_acid_PRE oxalic_acid_PRE salicylic_acid_PRE citric_acid_PRE aconitic_acid_PRE pyruvic_acid_PRE alpha_ketoglutaric_acid_PRE choline_DD2 betaine_DD2 creatinine_DD2 tmao_DD2 creatine_DD2 proline_betaine_DD2 zeatin_DD2 proline_DD2 carnosine_DD2 histidine_DD2 histamine_DD2 methylhistidine_DD2 arginine_DD2 taurine_DD2 total_dma_DD2 trans_hydroxyproline_DD2 glutamine_DD2 asparagine_DD2 citrulline_DD2 methionine_sulfoxide_DD2 serine_DD2 acetylornithine_DD2 glycine_DD2 glutamic_acid_DD2 aspartic_acid_DD2 sarcosine_DD2 threonine_DD2 alpha_aaa_DD2 dopa_DD2 alanine_DD2 tyrosine_DD2 dopamine_DD2 serotonin_DD2 methonine_DD2 valine_DD2 tyramine_DD2 ornithine_DD2 kynurenine_DD2 lysine_DD2 nitro_tyr_DD2 tryptophan_DD2 xleu_DD2 phenylalanine_DD2 putrescine_DD2 pea_DD2 spermidine_DD2 spermine_DD2 glucose_DD2 carnitine_DD2 lysoc14_0_DD2 lysoc16_0_DD2 lysoc18_2_DD2 lysoc18_1_DD2 lysoc18_0_DD2 pc_aa_c36_6_DD2 pc_aa_c36_0_DD2 pc_aa_c38_6_DD2 pc_aa_c38_0_DD2 pc_aa_c40_6_DD2 pc_aa_c32_0_DD2 pc_ae_c34_2_DD2 pc_ae_c34_1_DD2 pc_aa_c34_4_DD2 pc_aa_c34_3_DD2 pc_aa_c34_2_DD2 pc_aa_c34_1_DD2 pc_ae_c36_3_DD2 pc_ae_c36_2_DD2 pc_ae_c36_1_DD2 pc_aa_c36_5_DD2 pc_aa_c36_4_DD2 pc_aa_c36_3_DD2 pc_aa_c36_2_DD2 pc_aa_c36_1_DD2 pc_ae_c38_4_DD2 pc_ae_c38_3_DD2 pc_ae_c38_2_DD2 pc_ae_c38_0_DD2 pc_aa_c38_5_DD2 pc_aa_c38_4_DD2 pc_aa_c38_3_DD2 pc_aa_c38_1_DD2 pc_ae_c40_4_DD2 pc_ae_c40_3_DD2 pc_aa_c40_5_DD2 pc_ae_c42_5_DD2 shikimic_acid_DD2 glyceric_acid_DD2 beta_hydroxybutyric_acid_DD2 lactic_acid_DD2 propionic_acid_DD2 malic_acid_DD2 butyric_acid_DD2 hippuric_acid_DD2 succinic_acid_DD2 glutaric_acid_DD2 fumaric_acid_DD2 valeric_acid_DD2 benzoic_acid_DD2 oxalic_acid_DD2 salicylic_acid_DD2 citric_acid_DD2 aconitic_acid_DD2 pyruvic_acid_DD2 alpha_ketoglutaric_acid_DD2 choline_HAR betaine_HAR creatinine_HAR tmao_HAR creatine_HAR proline_betaine_HAR zeatin_HAR proline_HAR carnosine_HAR histidine_HAR histamine_HAR methylhistidine_HAR arginine_HAR taurine_HAR total_dma_HAR trans_hydroxyproline_HAR glutamine_HAR asparagine_HAR citrulline_HAR methionine_sulfoxide_HAR serine_HAR acetylornithine_HAR glycine_HAR glutamic_acid_HAR aspartic_acid_HAR sarcosine_HAR threonine_HAR alpha_aaa_HAR dopa_HAR alanine_HAR tyrosine_HAR dopamine_HAR serotonin_HAR methonine_HAR valine_HAR tyramine_HAR ornithine_HAR kynurenine_HAR lysine_HAR nitro_tyr_HAR tryptophan_HAR xleu_HAR phenylalanine_HAR putrescine_HAR pea_HAR spermidine_HAR spermine_HAR glucose_HAR carnitine_HAR lysoc14_0_HAR lysoc16_0_HAR lysoc18_2_HAR lysoc18_1_HAR lysoc18_0_HAR pc_aa_c36_6_HAR pc_aa_c36_0_HAR pc_aa_c38_6_HAR pc_aa_c38_0_HAR pc_aa_c40_6_HAR pc_aa_c32_0_HAR pc_ae_c34_2_HAR pc_ae_c34_1_HAR pc_aa_c34_4_HAR pc_aa_c34_3_HAR pc_aa_c34_2_HAR pc_aa_c34_1_HAR pc_ae_c36_3_HAR pc_ae_c36_2_HAR pc_ae_c36_1_HAR pc_aa_c36_5_HAR pc_aa_c36_4_HAR pc_aa_c36_3_HAR pc_aa_c36_2_HAR pc_aa_c36_1_HAR pc_ae_c38_4_HAR pc_ae_c38_3_HAR pc_ae_c38_2_HAR pc_ae_c38_0_HAR pc_aa_c38_5_HAR pc_aa_c38_4_HAR pc_aa_c38_3_HAR pc_aa_c38_1_HAR pc_ae_c40_4_HAR pc_ae_c40_3_HAR pc_aa_c40_5_HAR pc_ae_c42_5_HAR shikimic_acid_HAR glyceric_acid_HAR beta_hydroxybutyric_acid_HAR lactic_acid_HAR propionic_acid_HAR malic_acid_HAR butyric_acid_HAR hippuric_acid_HAR succinic_acid_HAR glutaric_acid_HAR fumaric_acid_HAR valeric_acid_HAR benzoic_acid_HAR oxalic_acid_HAR salicylic_acid_HAR citric_acid_HAR aconitic_acid_HAR pyruvic_acid_HAR alpha_ketoglutaric_acid_HAR),
    MT: %w(alpha_pinene_ww camphene_ww beta_pinene_ww 3_carene_ww myrcene_ww alpha_terpinene_ww limonene_ww beta_phellandrene_ww gamma_terpinene_ww p_cymene_ww terpineolene_ww bornyl_acetate_ww 4_allylanisole_ww alpha_terpineol_ww borneol_ww camphor_ww total_monoterpenes_ww alpha_pinene_dw camphene_dw beta_pinene_dw 3_carene_dw myrcene_dw alpha_terpinene_dw limonene_dw beta_phellandrene_dw gamma_terpinene_dw p_cymene_dw terpineolene_dw bornyl_acetate_dw 4_allylanisole_dw alpha_terpineol_dw borneol_dw camphor_dw total_monoterpenes_dw),
    PP: %w(ferulic_acid_glucoside ferulic_acid_hexoside ferulic_acid_hexoside_like_compound coumaric_acid_hexoside ellagic_acid hydroxypropiovanillone_hexoside lignan_deoxyhexoside lignan_derivative lignan_xyloside phenolic_hexoside quercetin_acetate taxifolin_hexoside unknown_compound vanillic_acid_hexoside coumaric_acid_esters taxifolin taxifolin_glucoside total_polyphenolics),
    RD: %w(ten_year_mean_count ten_year_mean_area ten_year_mean_tree_radial_growth ten_year_mean_density ten_year_mean_percent_area),
    TC: %w(carbon_ratio carbon_content),
    VG: %w(date_measured_ge_PRE measured_by_david_love_PRE sensor_ge_PRE vwc_ge_1_PRE vwc_ge_2_PRE vwc_ge_3_PRE vwc_ge_4_PRE date_measured_ge_DD1 measured_by_david_love_DD1 sensor_ge_DD1 vwc_ge_1_DD1 vwc_ge_2_DD1 vwc_ge_3_DD1 vwc_ge_4_DD1 date_measured_ge_R1 measured_by_david_love_R1 sensor_ge_R1 vwc_ge_1_R1 vwc_ge_2_R1 vwc_ge_3_R1 vwc_ge_4_R1 date_measured_ge_DD2 measured_by_david_love_DD2 sensor_ge_DD2 vwc_ge_1_DD2 vwc_ge_2_DD2 vwc_ge_3_DD2 vwc_ge_4_DD2 date_measured_ge_R2 measured_by_david_love_R2 sensor_ge_R2 vwc_ge_1_R2 vwc_ge_2_R2 vwc_ge_3_R2 vwc_ge_4_R2 date_measured_ge_DD3 measured_by_david_love_DD3 sensor_ge_DD3 vwc_ge_1_DD3 vwc_ge_2_DD3 vwc_ge_3_DD3 vwc_ge_4_DD3 date_measured_ge_DD3T1 measured_by_david_love_DD3T1 sensor_ge_DD3T1 vwc_ge_1_DD3T1 vwc_ge_2_DD3T1 vwc_ge_3_DD3T1 vwc_ge_4_DD3T1 date_measured_ge_DD3T2 measured_by_david_love_DD3T2 sensor_ge_DD3T2 vwc_ge_1_DD3T2 vwc_ge_2_DD3T2 vwc_ge_3_DD3T2 vwc_ge_4_DD3T2),
    WQ: %w(earlywood_density latewood_density avg_density)
  }

  has_many :measurement_values, dependent: :destroy
  attr_accessor :measurement_value_errors

  # Deal with measurement values
  MEASUREMENT_VALUE_TYPES.each do |type, values|
    has_many "#{type}_measurement_values".to_sym,
      -> { where(name: MEASUREMENT_VALUE_TYPES[type]) },
      class_name: 'MeasurementValue'
    values.each do |v|
      # attribute accessor
      has_one "#{type}_#{v}".to_sym,
        -> { where(name: v) },
        class_name: 'MeasurementValue'      
      
      # getter
      define_method "#{type}_#{v}_value" do
        send("#{type}_#{v}")&.value
      end
      
      # setter
      define_method "#{type}_#{v}=" do | arg |
        if !arg.blank?
          begin  
            measurement_value = MeasurementValue.find_or_initialize_by(measurement_id: self.id, name: v)
            measurement_value.value = arg
            # Bypass validation when measurement has not been created yet
            if self.id != nil
              measurement_value.save!
            end
          rescue
            errors.add(:base, measurement_value.errors.full_messages)
            self.save_measurement_value_errors
          end
        else
          measurement_value = MeasurementValue.find_by(measurement_id: self.id, name: v)
          if measurement_value != nil
            measurement_value.destroy!
          end
        end
      end
    end
  end

  attr_accessor :tree_code
  attr_accessor :sample_placeholder

  acts_as_taggable_on :tags

  belongs_to :last_user_updated, class_name: 'User', optional: true, inverse_of: :measurements
  belongs_to :assigned_to, class_name: 'User'
  belongs_to :performed_by, class_name: 'User'

  belongs_to :sample
  has_one :tree, through: :sample

  belongs_to :protocol
  belongs_to :measurement_type

  has_many :data_files, dependent: :destroy
  accepts_nested_attributes_for :data_files,
    allow_destroy: true,
    reject_if: proc { |attributes| attributes[:data].blank? }

  has_many :grouping_assignments, as: :assignable, dependent: :destroy
  has_many :groupings, through: :grouping_assignments

  before_validation :assign_sample_and_measurement_type, on: :create

  validates :measurement_type_id, presence: true, numericality: {only_integer: true}
  validates :sample_id, presence: true, numericality: {only_integer: true}, uniqueness: true
  validates_numericality_of :amount_used, greater_than_or_equal: 0, allow_blank: true
  validate :sample_suffix_equals_measurement_type
  validates_format_of :performed_on_before_type_cast, :performed_on_end_before_type_cast,
    with: /\A((19|20)\d\d)-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])\z/,
    message: "must be in YYYY-MM-DD format", allow_blank: true

  before_save :update_history, :save_measurement_value_errors

  # Required so that Measurements, Samples, and Trees can be displayed in groupings
  def to_s
    "#{tree.code} #{self.measurement_type.name}"
  end

  def root
    sample.root if sample
  end

  # Assigns sample_id and measurement_type_id on create -> cannot be changed once created
  def assign_sample_and_measurement_type
    if !@sample_placeholder.blank?
      sample_code = @sample_placeholder.split(' ')[0]
      suffix = sample_code.split('-')[1]

      self.sample_id = Sample.find_by(code: sample_code).id
      self.measurement_type_id = MeasurementType.find_by(code: suffix).id
    end
  end

  # Persist measurement value errors for display
  # Needed because these errors get deleted upon save of measurement (because not in validation)
  def save_measurement_value_errors
    if !measurement_value_errors.blank?
      previous_errors = measurement_value_errors.split(", ")
    else
      previous_errors = []
    end

    self.errors.full_messages.each do |message|
      previous_errors.push(message)
    end

    self.measurement_value_errors = previous_errors.uniq.join(", ")
  end

  # appends performed_on and performed_on_end to create a range (YYYY-MM-DD-DD)
  def performed_on_range
    if performed_on_end.present? && performed_on != performed_on_end
      return "#{performed_on}-#{performed_on_end.strftime("%d")}"
    else
      return performed_on
    end
  end

  # Array of all measurement value names given a measurement type code
  def self.measurement_value_types_array(code)
    MEASUREMENT_VALUE_TYPES[code.to_sym]
  end

  # Get versions of measurement values in order to list them in the version history for the measurement
  def measurement_value_versions
    measurement_value_ids = self.measurement_values.pluck('id')
    return PaperTrail::Version.where(:item_id => measurement_value_ids, item_type: 'MeasurementValue')
  end

  # Allow LIKE search for dates
  ransacker :perform_on do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(measurements.perform_on, "%Y-%m-%d %H:%M:%S")')
  end

  ransacker :performed_on do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(measurements.performed_on, "%Y-%m-%d %H:%M:%S")')
  end

  private

  # updates the last user who made updates
  def update_history
    if self.changed?
      if User.current_user != nil
        self.last_user_updated_id = User.current_user.id
      end
    end
  end

  # makes sure that the measurement type corresponds to the sample code
  # i.e. Gas Exchange measurements are only carried out on samples marked 'GE'
  def sample_suffix_equals_measurement_type
    if @sample_placeholder.blank?
      if self.sample_id != nil
        suffix = Sample.find_by(id: self.sample_id).suffix
        measurement_type_code = MeasurementType.find_by(id: self.measurement_type_id).code

        if suffix != measurement_type_code
          errors.add(:measurement_type, 'does not correspond to this sample code')
        end
      end
    end
  end

  # for advanced search
  #def self.ransackable_attributes(auth_object = nil)
    #['performed_on']
  #end
end
