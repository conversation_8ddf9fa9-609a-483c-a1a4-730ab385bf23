class CalendarEvent < ApplicationRecord
  validates :name, presence: true
  validates :start_time, format: { with: /\A((19|20)\d\d)-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])\z/ }
  validates :end_time, format: { with: /\A((19|20)\d\d)-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])\z/ }, allow_blank: true

  # Allow LIKE search for dates
  ransacker :start_time do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(calendar_events.start_time, "%Y-%m-%d %H:%M:%S")')
  end

  # Allow LIKE search for dates
  ransacker :end_time do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(calendar_events.end_time, "%Y-%m-%d %H:%M:%S")')
  end
end
