class MeasurementBulkImport

  def self.import(file_id, protocol_id, sample_type, measurement_type_id)
    file = GeneralFile.find_by(id: file_id)
    child_codes = {'MT' => 'CD', 'PP' => 'CD'}
    phases = {
      'MB' => ['FIELD', 'PRE', 'DD2', 'HAR'],
      'MP' => ['FIELD', 'PRE', 'DD2', 'HAR'],
      'VG' => ['PRE', 'DD1', 'R1', 'DD2', 'R2', 'DD3', 'DD3T1', 'DD3T2']
    }
    measurement_type_code = MeasurementType.find_by(id: measurement_type_id).code
    current_tree = nil
    current_user_id = User.current_user.id
    options = {
      headers_in_file: true, 
      user_provided_headers: get_headers(measurement_type_code),
      remove_zero_values: false,
      convert_values_to_numeric: false
    }
    begin
      SmarterCSV.process(file.content.path, options) do |array|
        measurement_hash = array.first
        parent_sample_id = nil

        # Unique Tree Code is required
        if !current_tree = measurement_hash[:code]
          return [false, "Missing Unique Tree Code"]
        elsif !tree = Tree.find_by(code: measurement_hash[:code].to_s)
          return [false, "Tree #{current_tree} does not exist."]
        end

        # Deal with phases
        if phases.keys.include? measurement_type_code
          measurement_hash = process_measurement_hash_for_phases(measurement_hash, measurement_type_code)
          if !measurement_hash[:phase]
            return [false, "Missing phase"]
          elsif !phases[measurement_type_code].include? measurement_hash[:phase]
            return [false, "Phase #{measurement_hash[:phase]} does not exist"]
          end
        end

        # Deal with parent samples (i.e. measurements attach to child samples)
        if child_codes.keys.include? measurement_type_code
          parent_sample_hash = get_sample_hash(tree.id, nil, child_codes[measurement_type_code], sample_type, measurement_hash)
          
          if !parent_sample = Sample.find_by(tree_id: tree.id, suffix: child_codes[measurement_type_code])
            parent_sample = Sample.create! parent_sample_hash
          else
            parent_sample.update! parent_sample_hash
            fill_nil_whodunnit('Sample', parent_sample.id, current_user_id)
          end

          parent_sample_id = parent_sample.id
        end

        # Grab sample hash and measurement hash
        sample_hash = get_sample_hash(tree.id, parent_sample_id, measurement_type_code, sample_type, measurement_hash, current_user_id)
        measurement_hash = get_measurement_hash(measurement_hash, measurement_type_id, measurement_type_code, protocol_id)

        # Create or update sample
        if !sample = Sample.find_by(tree_id: tree.id, suffix: measurement_type_code)
          sample = Sample.create! sample_hash
        else
          sample.update! sample_hash
          fill_nil_whodunnit('Sample', sample.id, current_user_id)
        end

        # Calculate original amount for parent sample
        if child_codes.keys.include? measurement_type_code
          parent_sample.calculate_original_amount
          parent_sample.save!
          fill_nil_whodunnit('Sample', parent_sample.id, current_user_id)
        end

        # Create or update measurement
        measurement_hash[:sample_id] = sample.id
        if !measurement = Measurement.find_by(sample_id: sample.id)
          Measurement.create! measurement_hash
          measurement = Measurement.find_by(sample_id: sample.id) 
        end

        measurement.update! measurement_hash
        fill_nil_whodunnit_measurement(measurement, current_user_id)
      
        # If any errors in measurement values, stop upload
        if !measurement.measurement_value_errors.blank?
          return [false, "Error importing measurement values at Tree #{current_tree}. #{measurement.measurement_value_errors}. All subsequent measurements were not imported."]
        end
      end

    rescue Exception => e
      if e.class.to_s == "SmarterCSV::HeaderSizeMismatch"
        return [false, "Headers in input file must match the headers in the template."]
      elsif e.class.to_s == 'ArgumentError'
        return [false, "Input file must be a CSV."]
      elsif e.class.to_s == 'ActiveRecord::RecordInvalid'
        return [false, "Error importing measurement at Tree #{current_tree}. #{e}. All subsequent measurements were not imported."]
      else
        return [false, "#{e.class.to_s}"]
      end
    end
    return [true, 'Measurements were successfully imported']
  end

  # Set whodunnit for versions that are empty
  def self.fill_nil_whodunnit(item_type, item_id, current_user_id)
    null_versions = PaperTrail::Version.where(whodunnit: nil, item_type: item_type, item_id: item_id)
    null_versions.each do |null_version|
      null_version.whodunnit = current_user_id
      null_version.save!
    end
  end

  # Set whodunnit for versions that are empty (specifically for measurements)
  def self.fill_nil_whodunnit_measurement(measurement, current_user_id)
    fill_nil_whodunnit('Measurement', measurement.id, current_user_id)

    measurement_value_ids = measurement.measurement_values.pluck('id')
    measurement_value_ids.each do |id|
      fill_nil_whodunnit('MeasurementValue', id, current_user_id)
    end 
  end

  # Returns headers based on measurement type code as symbols
  def self.get_headers(measurement_type_code)
    if ['BM', 'GE', 'LL', 'MT', 'PP', 'RD'].include? measurement_type_code
      array = send("#{measurement_type_code}_headers")
    elsif ['MB', 'MP', 'VG'].include? measurement_type_code # custom headers
      return send("#{measurement_type_code}_headers")
    else
      # Basic header format (just tree code needed)
      array = ['code']
    end

    Measurement.measurement_value_types_array(measurement_type_code).each do |measurement_value|
      array.push("#{measurement_type_code}_#{measurement_value}")
    end

    return array.map(&:to_sym)
  end

  # Returns gas exchange specific headers
  def self.GE_headers
    ['code', 'performed_on']
  end

  # Returns monoterpene specific headers
  def self.MT_headers
    ['code', 'sample_type', 'date_range_sampled', 'EX', 'EX2', 'description', 'amount_used_wet', 'amount_used_dry', 'performed_on']
  end

  # Returns polyphenolic specific headers
  def self.PP_headers
    ['code', 'sample_type', 'collected_on', 'EX', 'EX2', 'description', 'amount_used', 'performed_on']
  end

  # Returns resin duct specific headers
  def self.RD_headers
    ['code', 'collected_on']
  end

  # Returns lesion length specific headers
  def self.LL_headers
    ['code', 'performed_on', 'experiment_description']
  end

  # Returns biomass specific headers
  def self.BM_headers
    ['code', 'experiment_description']
  end

  # Returns metabolomics specific headers
  def self.MB_headers
    ['code', 'collected_on', 'phase', 'MB_lithium', 'MB_boron', 'MB_sodium', 'MB_magnesium', 'MB_aluminum', 'MB_phosphorus', 'MB_potassium', 'MB_calcium', 'MB_vanadium', 'MB_manganese', 'MB_iron', 'MB_nickel', 'MB_cobalt', 'MB_copper', 'MB_zinc', 'MB_arsenic', 'MB_selenium', 'MB_rubidium', 'MB_strontium', 'MB_cadmium', 'MB_tellurium', 'MB_cesium', 'MB_barium', 'MB_lanthanum', 'MB_thallium', 'MB_gallic_acid', 'MB_gallocatchin', 'MB_3_4_dihydroxybenzoic_acid', 'MB_catechin', 'MB_protocatecuic_aldehyde', 'MB_pungenol', 'MB_caffeic_acid', 'MB_vanillic_acid', 'MB_vanillin', 'MB_piceol', 'MB_taxifolin', 'MB_p_coumaric_acid', 'MB_ferulic_acid', 'MB_myricetin', 'MB_quercetin', 'MB_naringenin', 'MB_kampferol', 'MB_apigenin', 'MB_isorhamnetin']
  end

  # Returns metabolomics (primary) specific headers
  def self.MP_headers
    ['code', 'collected_on', 'phase', 'MP_choline', 'MP_betaine', 'MP_creatinine', 'MP_tmao', 'MP_creatine', 'MP_proline_betaine', 'MP_zeatin', 'MP_proline', 'MP_carnosine', 'MP_histidine', 'MP_histamine', 'MP_methylhistidine', 'MP_arginine', 'MP_taurine', 'MP_total_dma', 'MP_trans_hydroxyproline', 'MP_glutamine', 'MP_asparagine', 'MP_citrulline', 'MP_methionine_sulfoxide', 'MP_serine', 'MP_acetylornithine', 'MP_glycine', 'MP_glutamic_acid', 'MP_aspartic_acid', 'MP_sarcosine', 'MP_threonine', 'MP_alpha_aaa', 'MP_dopa', 'MP_alanine', 'MP_tyrosine', 'MP_dopamine', 'MP_serotonin', 'MP_methonine', 'MP_valine', 'MP_tyramine', 'MP_ornithine', 'MP_kynurenine', 'MP_lysine', 'MP_nitro_tyr', 'MP_tryptophan', 'MP_xleu', 'MP_phenylalanine', 'MP_putrescine', 'MP_pea', 'MP_spermidine', 'MP_spermine', 'MP_glucose', 'MP_carnitine', 'MP_lysoc14_0', 'MP_lysoc16_0', 'MP_lysoc18_2', 'MP_lysoc18_1', 'MP_lysoc18_0', 'MP_pc_aa_c36_6', 'MP_pc_aa_c36_0', 'MP_pc_aa_c38_6', 'MP_pc_aa_c38_0', 'MP_pc_aa_c40_6', 'MP_pc_aa_c32_0', 'MP_pc_ae_c34_2', 'MP_pc_ae_c34_1', 'MP_pc_aa_c34_4', 'MP_pc_aa_c34_3', 'MP_pc_aa_c34_2', 'MP_pc_aa_c34_1', 'MP_pc_ae_c36_3', 'MP_pc_ae_c36_2', 'MP_pc_ae_c36_1', 'MP_pc_aa_c36_5', 'MP_pc_aa_c36_4', 'MP_pc_aa_c36_3', 'MP_pc_aa_c36_2', 'MP_pc_aa_c36_1', 'MP_pc_ae_c38_4', 'MP_pc_ae_c38_3', 'MP_pc_ae_c38_2', 'MP_pc_ae_c38_0', 'MP_pc_aa_c38_5', 'MP_pc_aa_c38_4', 'MP_pc_aa_c38_3', 'MP_pc_aa_c38_1', 'MP_pc_ae_c40_4', 'MP_pc_ae_c40_3', 'MP_pc_aa_c40_5', 'MP_pc_ae_c42_5', 'MP_shikimic_acid', 'MP_glyceric_acid', 'MP_beta_hydroxybutyric_acid', 'MP_lactic_acid', 'MP_propionic_acid', 'MP_malic_acid', 'MP_butyric_acid', 'MP_hippuric_acid', 'MP_succinic_acid', 'MP_glutaric_acid', 'MP_fumaric_acid', 'MP_valeric_acid', 'MP_benzoic_acid', 'MP_oxalic_acid', 'MP_salicylic_acid', 'MP_citric_acid', 'MP_aconitic_acid', 'MP_pyruvic_acid', 'MP_alpha_ketoglutaric_acid']
  end

  # Returns volumetric water content (GE) specific headers
  def self.VG_headers
    ['code', 'phase', 'VG_date_measured_ge', 'VG_measured_by_david_love', 'VG_sensor_ge', 'VG_vwc_ge_1', 'VG_vwc_ge_2', 'VG_vwc_ge_3', 'VG_vwc_ge_4']
  end

  # Special processing of measurement hash for different phases
  def self.process_measurement_hash_for_phases(measurement_hash, measurement_type_code)
    # MB-specific processing
    if ['MB', 'MP'].include? measurement_type_code
      if !measurement_hash[:phase] # no phase = FIELD
        measurement_hash[:phase] = 'FIELD'
      else
        # remove only FIELD-specific attributes
        field_specifc_attributes = [:MB_vanillic_acid]
        field_specifc_attributes.each { |k| measurement_hash.delete k }
      end
    end

    # Modify measurement_hash only if phase exists
    non_measurement_keys = [:code, :collected_on, :phase]
    if measurement_hash[:phase]
      modified_measurement_hash = {}

      measurement_hash.each do |k, v|
        if non_measurement_keys.include? k
          modified_measurement_hash[k] = v
        else
          modified_measurement_hash["#{k.to_s}_#{measurement_hash[:phase]}".to_sym] = v
        end
      end

      return modified_measurement_hash
    end

    return measurement_hash
  end

  # Directs to correct sample hash function based on measurement type code
  def self.get_sample_hash(tree_id, parent_sample_id, measurement_type_code, sample_type, measurement_hash, current_user_id)
    sample_hash = {}

    if ['CD'].include? measurement_type_code # parent samples
      sample_hash = defences_parent_sample_hash(tree_id, measurement_type_code, measurement_hash)
    elsif ['MT', 'PP'].include? measurement_type_code
      sample_hash = defences_child_sample_hash(tree_id, parent_sample_id, measurement_type_code, measurement_hash)
    elsif ['RD'].include? measurement_type_code
      sample_hash = dated_sample_hash(tree_id, measurement_type_code, sample_type, measurement_hash[:collected_on])
    elsif ['MB', 'MP'].include? measurement_type_code
      if measurement_hash[:phase] != "FIELD"
        # For greenhouse samples, if the parent sample doesn't exist, create it
        if !parent_sample = Sample.find_by(tree_id: tree_id, suffix: measurement_type_code)
          parent_sample = Sample.create! dated_sample_hash(tree_id, measurement_type_code, sample_type, measurement_hash[:collected_on])
        end

        child_measurement_code = "#{measurement_type_code}-#{measurement_hash[:phase]}"
        child_sample_hash = dated_child_sample_hash(tree_id, parent_sample.id, child_measurement_code, sample_type, measurement_hash[:collected_on])

        # Create or update child sample
        if !child_sample = Sample.find_by(tree_id: tree_id, suffix: child_measurement_code)
          child_sample = Sample.create! child_sample_hash
        else
          child_sample.update! child_sample_hash
          fill_nil_whodunnit('Sample', child_sample.id, current_user_id)
        end
      end

      # Return parent (measurements attach to parent sample)
      sample_hash = dated_sample_hash(tree_id, measurement_type_code, sample_type, measurement_hash[:collected_on])
    elsif ['VG'].include? measurement_type_code
      # For greenhouse samples, if the parent sample doesn't exist, create it
      if !parent_sample = Sample.find_by(tree_id: tree_id, suffix: measurement_type_code)
        parent_sample = Sample.create! basic_sample_hash(tree_id, measurement_type_code, sample_type)
      end

      child_measurement_code = "#{measurement_type_code}-#{measurement_hash[:phase]}"
      child_sample_hash = basic_child_sample_hash(tree_id, parent_sample.id, child_measurement_code, sample_type)

      # Create or update child sample
      if !child_sample = Sample.find_by(tree_id: tree_id, suffix: child_measurement_code)
        child_sample = Sample.create! child_sample_hash
      else
        child_sample.update! child_sample_hash
        fill_nil_whodunnit('Sample', child_sample.id, current_user_id)
      end

      # Return parent (measurements attach to parent sample)
      sample_hash = basic_sample_hash(tree_id, measurement_type_code, sample_type)
    else
      sample_hash = basic_sample_hash(tree_id, measurement_type_code, sample_type)
    end

    return sample_hash
  end

  # Creates bare minimum sample (good for researchers that don't track samples)
  def self.basic_sample_hash(tree_id, measurement_type_code, sample_type)
    {:tree_id => tree_id, :suffix => measurement_type_code, :sample_type => sample_type}
  end

  # Creates sample with date collected on
  def self.dated_sample_hash(tree_id, measurement_type_code, sample_type, collected_on)
    {:tree_id => tree_id, :suffix => measurement_type_code, :sample_type => sample_type, :collected_on => collected_on}
  end

  # Creates basic child sample
  def self.basic_child_sample_hash(tree_id, parent_sample_id, measurement_type_code, sample_type)
    {:tree_id => tree_id, :sample_id => parent_sample_id, :suffix => measurement_type_code, :sample_type => sample_type}
  end

  # Creates child sample with date collected on
  def self.dated_child_sample_hash(tree_id, parent_sample_id, measurement_type_code, sample_type, collected_on)
    {:tree_id => tree_id, :sample_id => parent_sample_id, :suffix => measurement_type_code, :sample_type => sample_type, :collected_on => collected_on}
  end

  # Parent sample hash for CD samples
  def self.defences_parent_sample_hash(tree_id, measurement_type_code, measurement_hash)
    remaining_amount = measurement_hash[:EX].to_f + measurement_hash[:EX2].to_f
    start_date, end_date = process_date_range(measurement_hash[:date_range_sampled])

    sample_hash = {
      :tree_id => tree_id, 
      :suffix => measurement_type_code, 
      :sample_type => measurement_hash[:sample_type].downcase,
      :collected_on => start_date,
      :collected_on_end => end_date,
      :original_unit => 'mg',
      :actual_amount => remaining_amount,
      :actual_unit => 'mg',
      :description => measurement_hash[:description]
    }

    return sample_hash
  end

  # Child sample hash for MT and PP samples
  def self.defences_child_sample_hash(tree_id, parent_sample_id, measurement_type_code, measurement_hash)
    sample_hash = {
      :tree_id => tree_id,
      :sample_id => parent_sample_id,
      :suffix => measurement_type_code, 
      :sample_type => measurement_hash[:sample_type].downcase,
      :collected_on => measurement_hash[:collected_on],
      :original_amount => measurement_hash[:amount_used_wet],
      :original_unit => 'mg',
      :actual_amount => measurement_hash[:amount_used_dry],
      :actual_unit => 'mg',
      :description => measurement_hash[:description]
    }

    return sample_hash
  end

  # Directs to correct measurement hash function based on measurement type code
  def self.get_measurement_hash(measurement_hash, measurement_type_id, measurement_type_code, protocol_id)
    measurement_hash[:measurement_type_id] = measurement_type_id # Add measurement type ID
    
    if (protocol_id != nil) && (!protocol_id.blank?) # Only set protocol if a new one is provided
      measurement_hash[:protocol_id] = protocol_id
    end

    # Process measurement hash (split performed_on into start and end)
    if measurement_hash[:performed_on]
      start_date, end_date = process_date_range(measurement_hash[:performed_on])
      measurement_hash[:performed_on] = start_date
      measurement_hash[:performed_on_end] = end_date
    end
   
    # Delete from measurement_hash
    delete_from_measurement_hash.each do |item|
      measurement_hash.delete(item.to_sym)
    end

    # Rename experiment_description to description
    # Both sample and experiment have a description field -> the sample description was already deleted
    if measurement_hash.key? :experiment_description
      measurement_hash[:description] = measurement_hash.delete :experiment_description
    end

    return measurement_hash
  end

  # Headers to delete from measurement hash
  def self.delete_from_measurement_hash
    ['code', 'phase', 'sample_type', 'collected_on', 'date_range_sampled', 'EX', 'EX2', 'description', 'amount_used', 'amount_used_wet', 'amount_used_dry']
  end

  # Separates a date range into its start and end date
  def self.process_date_range(date_range)
    date_range_array = date_range.split('-')

    if date_range_array.length > 3 # YYYY-MM-DD-DD
      start_date = date_range_array.first(3).join('-')
      end_date = date_range_array.values_at(0, 1, 3).join('-')
      return start_date, end_date
    else # YYYY-MM-DD
      return date_range, nil
    end
  end
end
