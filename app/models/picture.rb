class Picture < ApplicationRecord
  acts_as_taggable_on :tags

  belongs_to :user

  has_attached_file :content, styles: { thumb: ["100x100>", :png] }
  validates_attachment_content_type :content,
    content_type: ['image/png', 'image/jpeg'],
    message: 'must be an image in png or jpeg format'

  validates_with AttachmentPresenceValidator, attributes: :content

  # Allow LIKE search for dates
  ransacker :created_at do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(pictures.created_at, "%Y-%m-%d %H:%M:%S")')
  end
end
