class CowProduction < ApplicationRecord
  def self.import(file_id)
    file = GeneralFile.find_by(id: file_id)
    current_tree = nil
    options = {
      headers_in_file: true, 
      user_provided_headers: %i(CW_TAG	CW_GENO_ID	CW_BRDT	CW_YR_LETTER	CW_AGE_JUL2025_YR	CW_PGWT_KG	CW_Pred_PGWT_KG	CW_PGPREG	CW_PGDT	CF_BRDT	RFI_gEPD	CW_REMARKS	RANCH),
      remove_zero_values: false,
      convert_values_to_numeric: false
    }

    begin
      SmarterCSV.process(file.content.path, options) do |array|
        cow_production_hash = array.first.transform_keys { |key| key.to_s.downcase.to_sym }
        current_cow = cow_production_hash[:cw_tag]
        # Post-processing
        # tree_hash[:species_id] = self.format_species(tree_hash[:species_id]) # replaces species code with species id
        # tree_hash[:source_id] = self.format_source(tree_hash[:source_id]) # replace source code with id
        # tree_hash[:family] = self.format_family(tree_hash[:family]) # appends zeros in front of 3-digit family codes
        # tree_hash[:forward_selected] = self.format_boolean_word(tree_hash[:forward_selected])
        # tree_hash[:label_present] = self.format_boolean_word(tree_hash[:label_present])
        # tree_hash[:res_for_tree] = self.convert_to_boolean(tree_hash[:res_for_tree])

        # Create or update tree
        if !cowproduction = CowProduction.find_by(:cw_tag => cow_production_hash[:cw_tag].to_s)
          Rails.logger.info("Creating new cow production record")
          Rails.logger.info(cow_production_hash)
          cow_production_hash[:cw_brdt] = Date.strptime(cow_production_hash[:cw_brdt], '%m/%d/%Y') if cow_production_hash[:cw_brdt].present?
          cow_production_hash[:cw_pgdt] = Date.strptime(cow_production_hash[:cw_pgdt], '%m/%d/%Y') if cow_production_hash[:cw_pgdt].present?
          cow_production_hash[:cf_brdt] = Date.strptime(cow_production_hash[:cf_brdt], '%m/%d/%Y') if cow_production_hash[:cf_brdt].present?
          CowProduction.create! cow_production_hash
        else
          puts "Already exists"
          #tree.update! tree.attributes.merge(tree_hash.compact)
        end
      end

    rescue Exception => e
      if e.class.to_s == "SmarterCSV::HeaderSizeMismatch"
        return [false, "Headers in input file must match the headers in the template."]
      elsif e.class.to_s == 'ArgumentError'
        return [false, "Input file must be a CSV."]
      elsif e.class.to_s == 'ActiveRecord::RecordInvalid'
        return [false, "Error importing Tree #{current_tree}. #{e}. All subsequent trees were not imported."]
      else
        return [false, "#{e.class.to_s}"]
      end
    end

    return [true, 'Cow production data was successfully imported']
  end

  # Allow LIKE search for dates
  ransacker :cw_brdt do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(cow_productions.cw_brdt, "%Y-%m-%d %H:%M:%S")')
  end  
  ransacker :cf_brdt do
    Arel::Nodes::SqlLiteral.new('DATE_FORMAT(cow_productions.cf_brdt, "%Y-%m-%d %H:%M:%S")')
  end   
end
