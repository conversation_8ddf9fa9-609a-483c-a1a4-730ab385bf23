class ApplicationRecord < ActiveRecord::Base
  self.abstract_class = true

  # for parsing CSV during bulk upload => turn everything into Y/N
  def self.format_boolean_word(boolean_word)
    if boolean_word != nil
      boolean_word = boolean_word.downcase
      if (boolean_word == 'y') || (boolean_word == 'yes')
        return 'Y'
      elsif (boolean_word == 'n') || (boolean_word == 'no')
        return 'N'
      else
        return boolean_word
      end
    else
      return boolean_word
    end
  end

  # for parsing CSV during bulk upload => turn Y/N to boolean
  def self.convert_to_boolean(boolean_word)
    word = self.format_boolean_word(boolean_word)

    if word == 'N'
      return 0
    else
      return 1
    end
  end
end
