class Characteristic < ApplicationRecord
  belongs_to :tree, inverse_of: :characteristics
  belongs_to :last_user_updated, class_name: 'User', inverse_of: :characteristics

  serialize :condition_code, Array
  
  validates :age_recorded, presence: true
  validates_numericality_of :age_recorded, greater_than: 0, only_integer: true
  validates :age_recorded, uniqueness: { scope: :tree_id }
  validates_numericality_of :height, :dbh, greater_than: 0, allow_blank: true
  validates_numericality_of :height_breeding_value, :height_breeding_value_gxe, :dbh_breeding_value, :dbh_breeding_value_gxe, allow_blank: true
  validates :dbh_position, inclusion: { in: [ 'DBH', 'Above', 'Below' ] }, allow_blank: true
  validates :status_code, numericality: {only_integer: true, greater_than_or_equal_to: 1}, allow_blank: true
  validate :status_code_matches_species
  validates :western_gall_rust_two_code, numericality: {only_integer: true, greater_than_or_equal_to: 0, less_than_or_equal_to: 2}, allow_blank: true
  validates :western_gall_rust_six_code, numericality: {only_integer: true, greater_than_or_equal_to: 0, less_than_or_equal_to: 6}, allow_blank: true
  validates :mountain_pine_beetle_presence, inclusion: { in: [ 'Y', 'N' ] }, allow_blank: true 
  validates :white_pine_weevil_score_sum, numericality: {only_integer: true, greater_than_or_equal_to: 0}, allow_blank: true
  validates :white_pine_weevil_presence, numericality: {only_integer: true, greater_than_or_equal_to: 0, less_than_or_equal_to: 1}, allow_blank: true
  before_save :check_disease_codes, :update_history

  def self.title
    model_name.human
  end

  # validates that the status code is one that that is allowable for the species
  def status_code_matches_species
    tree = Tree.find_by(id: tree_id)

    if status_code != nil
      if tree.species_id == 1 # lodgepole pine
        if status_code > 5
          errors.add(:status_code, 'is not allowable for lodgepole pines')
        end
      elsif tree.species_id == 2 # white spruce
        if status_code > 7
          errors.add(:status_code, 'is not allowable for white spruce')
        end
      end
    end
  end

  # makes sure that not corresponding diseases to a tree's species are null
  def check_disease_codes
    if tree.species_id == 1
      self.white_pine_weevil_score_sum = nil
      self.white_pine_weevil_presence = nil
    elsif tree.species_id == 2
      self.western_gall_rust_six_code = nil
      self.western_gall_rust_two_code = nil
      self.mountain_pine_beetle_presence = nil
    end
  end

  # format condition code for view
  def format_condition_code
    formatted_condition_code = ''
    self.condition_code.each do |code|
      if code.length > 0
        formatted_condition_code += code + ', '
      end
    end

    return formatted_condition_code.chomp(", ")
  end

  # format condition code to store in database
  def self.format_condition_code_to_array(codes)
    if codes != nil
      if codes.include? ','
        code_array = codes.split(',') # turn comma-delimited string to an array
      else
        code_array = codes.split('') # no commas (split every single character)
      end
      code_array.unshift('') # push empty item in first position (rails format)
      return code_array.map(&:upcase).sort! # uppercase all letters and sort alphabetically
    else
      return codes
    end
  end

  private

  # for bulk importing characteristics
  def self.import(file_id)
    file = GeneralFile.find_by(id: file_id)
    current_characteristic = []
    options = {
      headers_in_file: true,
      user_provided_headers: %i(tree_id age_recorded height dbh dbh_position height_breeding_value dbh_breeding_value height_breeding_value_gxe dbh_breeding_value_gxe white_pine_weevil_presence white_pine_weevil_score_sum western_gall_rust_six_code western_gall_rust_two_code mountain_pine_beetle_presence status_code condition_code notes),
      remove_zero_values: false,
      convert_values_to_numeric: false
    }

    begin
      SmarterCSV.process(file.content.path, options) do |array|
        characteristic_hash = array.first
        current_characteristic = [characteristic_hash[:tree_id].to_s, characteristic_hash[:age_recorded].to_s] # used to identify problem characteristics

        characteristic_hash[:tree_id] = Tree.find_by(code: characteristic_hash[:tree_id]).id # replace tree code with id
        characteristic_hash[:dbh_position] = self.format_dbh_position(characteristic_hash[:dbh_position]) # format DBH position for database
        characteristic_hash[:condition_code] = self.format_condition_code_to_array(characteristic_hash[:condition_code]) # format condition code for database
        characteristic_hash[:mountain_pine_beetle_presence] = self.format_boolean_word(characteristic_hash[:mountain_pine_beetle_presence]) # format mountain pine beetle presence for database
        
        # Create or update characteristic
        if !characteristic = Characteristic.find_by(:tree_id => characteristic_hash[:tree_id], age_recorded: characteristic_hash[:age_recorded])
          Characteristic.create! characteristic_hash
        else
          characteristic.update! characteristic_hash
        end
      end
    rescue Exception => e
      if e.class.to_s == "SmarterCSV::HeaderSizeMismatch"
        return [false, "Headers in input file must match the headers in the template."]
      elsif e.class.to_s == 'ArgumentError'
        return [false, "Input file must be a CSV."]
      elsif e.class.to_s == 'ActiveRecord::RecordInvalid'
        return [false, "Error importing Characteristic for Tree #{current_characteristic[0]} at Age #{current_characteristic[1]}. #{e}. All subsequent characteristics were not imported."]
      else
        return [false, "#{e.class.to_s}"]
      end
    end

    return [true, 'Characteristics were successfully imported']
  end

  # for parsing CSV during bulk upload -> formats dbh position
  def self.format_dbh_position(position)
    if position == nil
      return nil
    else
      position.strip!
    
      if position.downcase == 'above'
        return 'Above'
      elsif position.downcase == 'below'
        return 'Below'
      elsif position.downcase == 'dbh'
        return 'DBH'
      else
        return position
      end
    end
  end

  # for advanced search
  def self.ransackable_attributes(auth_object = nil)
    ['age_recorded', 'height', 'dbh', 'dbh_position', 'height_breeding_value', 'dbh_breeding_value', 'height_breeding_value_gxe', 'dbh_breeding_value_gxe', 'white_pine_weevil_presence', 'white_pine_weevil_score_sum', 'western_gall_rust_six_code', 'western_gall_rust_two_code', 'mountain_pine_beetle_presence', 'status_code', 'condition_code', 'notes']
  end

  # updates the last user who made updates
  def update_history
    if self.changed?
      if User.current_user != nil
        self.last_user_updated_id = User.current_user.id
      end
    end
  end
end
