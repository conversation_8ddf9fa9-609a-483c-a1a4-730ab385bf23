require 'test_helper'

class TreesControllerTest < ActionController::TestCase
  def test_should_get_index_administrator
    login_as :admin
    get :index
    assert_response :success
    assert_equal Tree.find(:all).size, assigns(:trees).size
  end
  
  def test_should_get_index_superuser
    login_as :superuser
    get :index
    assert_response :success
    assert_equal Tree.find(:all).size, assigns(:trees).size
  end
  
  def test_should_get_index_user
    login_as :user
    get :index
    assert_response :success
    assert_equal Tree.find(:all, :conditions => ['site_id=?', sites(:one)]).size, assigns(:trees).size
  end

  def test_should_get_new
    login_as :user
    get :new
    assert_response :success
  end

  def test_should_create_tree
    login_as :user
    assert_difference('Tree.count') do
      post :create, :tree => { :code => 'TESTCODE', :site_id => sites(:one).id }
    end

    assert_redirected_to tree_path(assigns(:tree))
  end

  def test_should_show_tree_administrator
    login_as :admin
    get :show, :id => trees(:one).id
    assert_response :success
  end
  
  def test_should_show_tree_superuser
    login_as :superuser
    get :show, :id => trees(:one).id
    assert_response :success
  end
  
  def test_should_show_tree_user
    login_as :user
    get :show, :id => trees(:one).id
    assert_response :success
  end
  
  def test_should_not_get_tree
    login_as :user
    
    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :show, :id => trees(:two).id
    end
  end

  def test_should_get_edit_administrator
    login_as :admin
    get :edit, :id => trees(:one).id
    assert_response :success
  end
  
  def test_should_get_edit_superuser
    login_as :superuser
    get :edit, :id => trees(:one).id
    assert_response :success
  end
  
  def test_should_get_edit_user
    login_as :user
    get :edit, :id => trees(:one).id
    assert_response :success
  end
  
  def test_should_not_get_edit
    login_as :user
    
    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :edit, :id => trees(:two).id
    end
  end

  def test_should_update_tree_administrator
    login_as :admin
    put :update, :id => trees(:one).id, :tree => { }
    assert_redirected_to tree_path(assigns(:tree))
  end

  def test_should_update_tree_superuser
    login_as :superuser
    put :update, :id => trees(:one).id, :tree => { }
    assert_redirected_to tree_path(assigns(:tree))
  end
  
  def test_should_update_tree_user
    login_as :user
    put :update, :id => trees(:one).id, :tree => { }
    assert_redirected_to tree_path(assigns(:tree))
  end
  
  def test_should_not_update_tree
    login_as :user
    
    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      put :update, :id => trees(:two).id, :trees => { }
    end
  end
  
  def test_should_destroy_tree_administrator
    login_as :admin
    assert_difference('Tree.count', -1) do
      delete :destroy, :id => trees(:one).id
    end
    
    assert_redirected_to trees_path
  end
  
  def test_should_destroy_tree_superuser
    login_as :superuser
    assert_difference('Tree.count', -1) do
      delete :destroy, :id => trees(:one).id
    end
    
    assert_redirected_to trees_path
  end
  
  def test_should_destroy_tree_user
    login_as :user
    assert_difference('Tree.count', -1) do
      delete :destroy, :id => trees(:one).id
    end

    assert_redirected_to trees_path
  end
  
  def test_should_not_destroy_tree
    login_as :user
    
    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      delete :destroy, :id => trees(:two).id
    end
  end
end
