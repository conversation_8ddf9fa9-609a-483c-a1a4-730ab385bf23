require 'test_helper'

class MeasurementsControllerTest < ActionController::TestCase
  def test_should_get_index_administrator
    login_as :admin
    get :index, :sample_id => samples(:one)
    assert_response :success
    assert_equal 2, assigns(:measurements).size
  end
  
  def test_should_get_index_superuser
    login_as :superuser
    get :index, :sample_id => samples(:one)
    assert_response :success
    assert_equal 2, assigns(:measurements).size
  end
  
  def test_should_get_index_user
    login_as :user
    get :index, :sample_id => samples(:one)
    assert_response :success
    assert_equal 2, assigns(:measurements).size
    
    #No sample -> Should list all measurements
    get :index
    assert_response :success
    #assert_equal 2, assigns(:measurements).size
  end
  
  def test_should_get_new_administrator
    login_as :admin
    get :new, :sample_id => samples(:one)
    assert_response :success
  end
  
  def test_should_get_new_superuser
    login_as :superuser
    get :new, :sample_id => samples(:one)
    assert_response :success
  end
  
  def test_should_get_new_user
    login_as :user
    get :new, :sample_id => samples(:one)
    assert_response :success
  end
  
  def test_should_not_get_new
    login_as :user
    
    # no sample_id
    assert_raise ActiveRecord::RecordNotFound do
      get :new
    end
    
    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :new, :sample_id => samples(:two)
    end
  end

  def test_should_create_measurement_administrator
    login_as :admin
    assert_difference('Measurement.count') do
      post :create, :sample_id => samples(:one), :measurement => {:name => 'test', :measurement_type_id => measurement_types(:one).id}
    end

    assert_redirected_to sample_measurement_path(samples(:one), assigns(:measurement))
  end
  
  def test_should_create_measurement_superuser
    login_as :superuser
    assert_difference('Measurement.count') do
      post :create, :sample_id => samples(:one), :measurement => {:name => 'test', :measurement_type_id => measurement_types(:one).id}
    end

    assert_redirected_to sample_measurement_path(samples(:one), assigns(:measurement))
  end
  
  def test_should_create_measurement_user
    login_as :user
    assert_difference('Measurement.count') do
      post :create, :sample_id => samples(:one), :measurement => {:name => 'test', :measurement_type_id => measurement_types(:one).id}
    end

    assert_redirected_to sample_measurement_path(samples(:one), assigns(:measurement))
  end
  
  def test_should_not_create_measurement
    login_as :user
    
    # no sample_id
    assert_raise ActiveRecord::RecordNotFound do
      post :create
    end
    
    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      post :create, :sample_id => samples(:two)
    end
  end

  def test_should_show_measurement_administrator
    login_as :admin
    get :show, :sample_id => samples(:one), :id => measurements(:one).id
    assert_response :success
  end
  
  def test_should_show_measurement_superuser
    login_as :superuser
    get :show, :sample_id => samples(:one), :id => measurements(:one).id
    assert_response :success
  end
  
  def test_should_show_measurement_user
    login_as :user
    get :show, :sample_id => samples(:one), :id => measurements(:one).id
    assert_response :success
  end
  
  def test_should_not_show_measurement
    login_as :user
    
    # no sample_id
    assert_raise ActiveRecord::RecordNotFound do
      get :show, :id => measurements(:one).id
    end
    
    # no sample_id
    assert_raise ActiveRecord::RecordNotFound do
      get :show, :sample_id => samples(:two), :id => measurements(:two).id
    end
  end

  def test_should_get_edit_administrator
    login_as :admin
    get :edit, :sample_id => samples(:one), :id => measurements(:one).id
    assert_response :success
  end
  
  def test_should_get_edit_superuser
    login_as :superuser
    get :edit, :sample_id => samples(:one), :id => measurements(:one).id
    assert_response :success
  end
  
  def test_should_get_edit_user
    login_as :user
    get :edit, :sample_id => samples(:one), :id => measurements(:one).id
    assert_response :success
  end
  
  def test_should_not_get_edit
    login_as :user
    
    # no sample_id
    assert_raise ActiveRecord::RecordNotFound do
      get :edit, :id => measurements(:one).id
    end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :edit, :sample_id => samples(:two), :id => measurements(:two).id
    end
  end

  def test_should_update_measurement_administrator
    login_as :admin
    put :update, :sample_id => samples(:one), :id => measurements(:one).id, :measurement => { }
    assert_redirected_to sample_measurement_path(samples(:one), assigns(:measurement))
  end
  
  def test_should_update_measurement_superuser
    login_as :superuser
    put :update, :sample_id => samples(:one), :id => measurements(:one).id, :measurement => { }
    assert_redirected_to sample_measurement_path(samples(:one), assigns(:measurement))
  end
  
  def test_should_update_measurement_user
    login_as :user
    put :update, :sample_id => samples(:one), :id => measurements(:one).id, :measurement => { }
    assert_redirected_to sample_measurement_path(samples(:one), assigns(:measurement))
  end
  
  def test_should_not_update_measurement
    login_as :user
    
    # no sample id
    assert_raise ActiveRecord::RecordNotFound do
      put :update, :id => measurements(:one).id, :measurement => { }
    end
    
    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      put :update, :sample_id => samples(:two), :id => measurements(:two).id, :measurement => { }
    end
  end

  def test_should_destroy_measurement_administrator
    login_as :admin
    assert_difference('Measurement.count', -1) do
      delete :destroy, :sample_id => samples(:one), :id => measurements(:one).id
    end

    assert_redirected_to measurements_path
  end
  
  def test_should_destroy_measurement_superuser
    login_as :superuser
    assert_difference('Measurement.count', -1) do
      delete :destroy, :sample_id => samples(:one), :id => measurements(:one).id
    end

    assert_redirected_to measurements_path
  end
  
  def test_should_destroy_measurement_user
    login_as :user
    assert_difference('Measurement.count', -1) do
      delete :destroy, :sample_id => samples(:one), :id => measurements(:one).id
    end

    assert_redirected_to measurements_path
  end
  
  def test_should_not_destroy_measurement
    login_as :user
    
    # no sample_id
    assert_raise ActiveRecord::RecordNotFound do
      delete :destroy, :id => measurements(:one).id
    end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      delete :destroy, :sample_id => samples(:two).id, :id => measurements(:two).id
    end
  end
end
