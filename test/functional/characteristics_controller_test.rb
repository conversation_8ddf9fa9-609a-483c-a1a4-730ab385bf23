require 'test_helper'

class CharacteristicsControllerTest < ActionController::TestCase
  def test_should_get_index_administrator
    login_as :admin
    get :index, :tree_id => trees(:one)
    assert_response :success
    assert_equal 2, assigns(:characteristics).size
  end

  def test_should_get_index_superuser
    login_as :superuser
    get :index, :tree_id => trees(:one)
    assert_response :success
    assert_equal 2, assigns(:characteristics).size
  end

  def test_should_get_index_user
    login_as :user

    get :index, :tree_id => trees(:one)
    assert_response :success
    assert_equal 2, assigns(:characteristics).size
  end

  def test_should_not_get_index
    login_as :user

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :index, :tree_id => trees(:two)
    end
  end

  def test_should_get_new_administrator
    login_as :admin
    get :new, :tree_id => trees(:one)
    assert_response :success
  end

  def test_should_get_new_superuser
    login_as :superuser
    get :new, :tree_id => trees(:one)
    assert_response :success
  end

  def test_should_get_new_user
    login_as :user
    get :new, :tree_id => trees(:one)
    assert_response :success
  end

  def test_should_not_get_new_user
    login_as :user

    # # no tree_id
    # assert_raise ActiveRecord::RecordNotFound do
    #   get :new
    # end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :new, :tree_id => trees(:two)
    end
  end

  def characteristic_administrator
    login_as :admin
    assert_difference('characteristic.count') do
      post :create, :tree_id => trees(:one)
    end

    assert_redirected_to characteristic_path(assigns(:tree), assigns(:characteristic))
  end

  def characteristic_superuser
    login_as :superuser
    assert_difference('characteristic.count') do
      post :create, :tree_id => trees(:one)
    end

    assert_redirected_to characteristic_path(assigns(:tree), assigns(:characteristic))
  end

  def characteristic_user
    login_as :user
    assert_difference('characteristic.count') do
      post :create, :tree_id => trees(:one)
    end

    assert_redirected_to characteristic_path(assigns(:tree), assigns(:characteristic))
  end

  def characteristic
    login_as :user

    # # no tree_id
    # assert_raise ActiveRecord::RecordNotFound do
    #   post :create
    # end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      post :create, :tree_id => trees(:two)
    end
  end

  def characteristic_administrator
    login_as :admin
    get :show, :tree_id => trees(:one), :id => characteristics(:one).id
    assert_response :success
  end

  def characteristic_superuser
    login_as :superuser
    get :show, :tree_id => trees(:one), :id => characteristics(:one).id
    assert_response :success
  end

  def characteristic_user
    login_as :user
    get :show, :tree_id => trees(:one), :id => characteristics(:one).id
    assert_response :success
  end

  def characteristic
    login_as :user

    # # no tree id
    # assert_raise ActiveRecord::RecordNotFound do
    #   get :show, :id => characteristics(:one).id
    # end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :show, :tree_id => trees(:two), :id => characteristics(:two).id
    end
  end

  def test_should_get_edit_admin
    login_as :admin
    get :edit, :tree_id => trees(:one), :id => characteristics(:one).id
    assert_response :success
  end

  def test_should_get_edit_superuser
    login_as :superuser
    get :edit, :tree_id => trees(:one), :id => characteristics(:one).id
    assert_response :success
  end

  def test_should_get_edit_user
    login_as :user
    get :edit, :tree_id => trees(:one), :id => characteristics(:one).id
    assert_response :success
  end

  def test_should_not_get_edit
    login_as :user

    # # no tree id
    # assert_raise ActiveRecord::RecordNotFound do
    #   get :edit, :id => characteristics(:one).id
    # end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      get :edit, :tree_id => trees(:two), :id => characteristics(:two).id
    end
  end

  def characteristic_administrator
    login_as :admin
    put :update, :tree_id => trees(:one), :id => characteristics(:one).id, :characteristic => { }
    assert_redirected_to characteristic_path(assigns(:tree), assigns(:characteristic))
  end

  def characteristic_superuser
    login_as :superuser
    put :update, :tree_id => trees(:one), :id => characteristics(:one).id, :characteristic => { }
    assert_redirected_to characteristic_path(assigns(:tree), assigns(:characteristic))
  end

  def characteristic_user
    login_as :user
    put :update, :tree_id => trees(:one), :id => characteristics(:one).id, :characteristic => { }
    assert_redirected_to characteristic_path(assigns(:tree), assigns(:characteristic))
  end

  def characteristic_user
    login_as :user

    # # no tree_id
    # assert_raise ActiveRecord::RecordNotFound do
    #   put :update, :id => characteristics(:one).id, :characteristic => { }
    # end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      put :update, :tree_id => trees(:two), :id => characteristics(:two).id, :characteristic => { }
    end
  end

  def characteristic_administrator
    login_as :admin
    assert_difference('characteristic.count', -1) do
      delete :destroy, :tree_id => trees(:one), :id => characteristics(:one).id
    end

    assert_redirected_to characteristics_path(assigns(:tree))
  end

  def characteristic_superuser
    login_as :superuser
    assert_difference('characteristic.count', -1) do
      delete :destroy, :tree_id => trees(:one), :id => characteristics(:one).id
    end

    assert_redirected_to characteristics_path(assigns(:tree))
  end

  def characteristic_user
    login_as :user
    assert_difference('characteristic.count', -1) do
      delete :destroy, :tree_id => trees(:one), :id => characteristics(:one).id
    end

    assert_redirected_to characteristics_path(assigns(:tree))
  end

  def characteristic
    login_as :user

    # # no tree_id
    # assert_raise ActiveRecord::RecordNotFound do
    #   delete :destroy, :id => characteristics(:one).id
    # end

    # wrong site
    assert_raise ActiveRecord::RecordNotFound do
      delete :destroy, :tree_id => trees(:two), :id => characteristics(:two).id
    end
  end
end
