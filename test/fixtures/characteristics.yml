# Read about fixtures at http://ar.rubyonrails.org/classes/Fixtures.html

one:
  test_subject: one
  diagnosis: TestSubject is surly
  current_smoker: false
  past_smoker: 1.5
  drinking_weekly: 1.5
  exercise_weekly: 2
  hours_sleep: 5.5
  enough_sleep: false
  past_medical: ['Cancer']
  symptoms: []
  father_age: 1
  father_health: MyString
  father_death_age: 63
  father_death_cause: Heart Attack
  mother_age: 65
  mother_health: Diabetic
  mother_death_age: -1
  mother_death_cause: MyString
  num_children: 1
  relative_condition: MyString
  relative_relationship: MyString
  evaluated_on: 2008-09-25
  height: 72
  weight: 190

two:
  test_subject: two
  diagnosis: TestSubject is improving
  current_smoker: false
  past_smoker: 1.5
  drinking_weekly: 1
  exercise_weekly: 3
  hours_sleep: 6.5
  enough_sleep: true
  past_medical: ['Cancer']
  symptoms: []
  father_age: 1
  father_health: MyString
  father_death_age: 63
  father_death_cause: Heart Attack
  mother_age: 65
  mother_health: Diabetic
  mother_death_age: -1
  mother_death_cause: MyString
  num_children: 1
  relative_condition: MyString
  relative_relationship: MyString
  evaluated_on: 2008-09-30
  height: 60
  weight: 110
  
three:
  test_subject: one
  diagnosis: TestSub<PERSON> is perfectly healthy...except he smokes 3 packs a day
  current_smoker: true
  past_smoker: -1
  drinking_weekly: 1.5
  exercise_weekly: 1.5
  hours_sleep: 8
  enough_sleep: true
  past_medical: ['Stroke', 'Bad headaches', 'Pneumonia', 'Glaucoma']
  symptoms: []
  father_age: 55
  father_health: Good
  father_death_age: -1
  father_death_cause: MyString
  mother_age: 56
  mother_health: Good
  mother_death_age: -1
  mother_death_cause: MyString
  num_children: 4
  relative_condition: MyString
  relative_relationship: MyString
  evaluated_on: 2008-04-22
  height: 72
  weight: 160
