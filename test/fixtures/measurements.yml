# Read about fixtures at http://ar.rubyonrails.org/classes/Fixtures.html

one:
  name: Metabolite Concentrations
  perform_on: <%= 5.days.ago.to_s :db %>
  assigned_to: user
  performed_on: <%= 4.days.ago.to_s :db %>
  performed_by: user2
  amount_used: 1.5
  amount_used_unit: ml
  sample: one
  protocol: one
  experiment_type: one

two:
  name: Random Experiment
  perform_on: <%= 5.days.ago.to_s :db %>
  assigned_to: user2
  performed_on: <%= 6.days.ago.to_s :db %>
  performed_by: user
  amount_used: 1.5
  amount_used_unit: ml
  sample: two
  protocol: one
  experiment_type: two

three:
  name: Random Experiment 2
  perform_on: <%= 2.days.from_now.to_s :db %>
  assigned_to: user
  performed_on: 
  performed_by: 
  amount_used: 1.5
  amount_used_unit: ml
  sample: one
  protocol: two
  experiment_type: two
