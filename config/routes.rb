Rails.application.routes.draw do
  resources :calendar_events
  devise_for :users, controllers: {
    sessions: 'users/sessions',
    invitations: 'users/invitations',
  }

  resources :users, except: [:destroy] do
    resources :user_pictures
    resources :tasks
  end

  # Planning
  resources :task_categories
  resources :task_priorities
  resources :tasks do
    collection do
      get :gantt
      get :calendar
    end
    member do
      put :complete
    end
  end

  resources :announcements
  resources :protocols
  resources :sites
  resources :data_file_types
  resources :data_files
  resources :general_files do
    collection do
      get 'all'
    end
  end
  resources :davidson_blue_ge3ls_files
  resources :an_cool_gaston_ge3ls_files
  resources :pictures
  resources :studies
  resources :measurements do
    resources :data_files
    resources :grouping_assignments
  end

  # Sample/tree tracking
  resources :samples do
    member do
      get 'history'
      patch 'rollback'
    end
    resources :samples
    resources :measurements do
      member do
        get 'history'
        patch 'rollback'
      end
    end
    resources :grouping_assignments
  end

  resources :parent_trees
  resources :cow_productions do
    get :cow_production, on: :member
  end
  resources :trees do
    get :tree, on: :member
    resources :samples
    resources :grouping_assignments
    resources :characteristics
    resources :measurements
  end

  resources :characteristics, :only => [:import] do
    collection do
      get :import
      post :import
    end
  end

  # Grouping/cohorts
  resources :groupings do
    resources :grouping_assignments
    resources :study_grouping_assignments
  end

  # Add routes to direct the grouping types to the correct place
  Grouping.valid_types.each do |grouping_type|
    resources "#{grouping_type.tableize.singularize}_groupings",
      controller: 'groupings', defaults: { type: grouping_type }
  end

  # Add routes to direct the bulk import types to the correct place
  BulkImport.categories.each do |category|
    category = category.pluralize.downcase
    get "bulk_import/#{category}", to: "bulk_imports#import", defaults: { category: category }
    post "bulk_import/#{category}", to: "bulk_imports#import", defaults: { category: category }
    #get "bulk_import/#{category}", to: "bulk_imports#demo_import", defaults: { category: category }
    #post "bulk_import/#{category}", to: "bulk_imports#demo_import", defaults: { category: category }
  end

  # Download routes
  BulkImport.templates.each do |template|
    filename = template.gsub('/', '_')
    get "download_#{filename}_template", to: "bulk_imports#download_template", defaults: {name: template}
  end

  ## Public routes
  get 'contact' => 'public/pages#contact'

  # Other
  get 'advanced_search' => 'simple#advanced_search'
  get 'advanced_search/result' => 'simple#result'
  get 'references' => 'simple#references'
  get 'data_files_all' => 'data_files#master'
 
  root 'simple#home'
end
