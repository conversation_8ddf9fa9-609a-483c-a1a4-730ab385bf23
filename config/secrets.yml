# Be sure to restart your server when you modify this file.

# Your secret key is used for verifying the integrity of signed cookies.
# If you change this key, all old signed cookies will become invalid!

# Make sure the secret is at least 30 characters and all random,
# no regular words or you'll be exposed to dictionary attacks.
# You can use `rails secret` to generate a secure secret key.

# Make sure the secrets in this file are kept private
# if you're sharing your code publicly.

development:
  secret_key_base: 446038dcdbcf1b8dcaf98ec18c096a14c103b10afebb15cf41579b4111a06a0145445509ef6211bcbc8e96c0a4b0bb632c01188672bb924a04e2bf1d4ec35316
  davidson_blue_username: <PERSON><PERSON><PERSON>
  davidson_blue_password: <%= ENV["DAVIDSON_BLUE_PASSWORD"] %>
  an_cool_gaston_username: An<PERSON><PERSON><PERSON><PERSON><PERSON>
  an_cool_gaston_password: <%= ENV["AN_COOL_GASTON_PASSWORD"] %>

test:
  secret_key_base: 32cca102e3474c3f1163c40c51e8e64df1e523fa3cd336448f9705ddb3380fbe43096802aa1acb285c2b3db54f371814c8bb15069e8b74b7887495992954fe65

# Do not keep production secrets in the repository,
# instead read values from the environment.
production:
  secret_key_base: 7a3a7e5f6a1df461ed255b09784417df5c4c941e64f35fb3c518c85ac0146bd38acf77860689cb91c2d293628f8cf0a2fdf7cca8d8b366488bf684df25128337
  davidson_blue_username: <PERSON><PERSON><PERSON>
  davidson_blue_password: <%= ENV["DAVIDSON_BLUE_PASSWORD"] %>
  an_cool_gaston_username: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  an_cool_gaston_password: <%= ENV["AN_COOL_GASTON_PASSWORD"] %>
