development:
  adapter: mysql2
  encoding: utf8
  reconnect: false
  database: rf_lims_development
  pool: 5
  username: root
  password:
  socket: /tmp/mysql.sock

test:
  adapter: mysql2
  encoding: utf8
  reconnect: false
  database: rf_lims_test
  pool: 5
  username: root
  password:
  socket: /tmp/mysql.sock

production:
  adapter: mysql2
  encoding: utf8
  reconnect: false
  database: rf_lims_development
  pool: 5
  username: root
  password:
  socket: /tmp/mysql.sock
