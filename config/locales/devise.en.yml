en:
  devise:
    sessions:
      link: 'Login'
      signed_in: 'Logged in successfully.'
      signed_out: 'Logged out successfully. Please close your web browser window.'
      unauthenticated: 'You need to login before continuing.'
      unconfirmed: 'You have to confirm your account before continuing.'
      locked: 'Your account is locked.'
      invalid: 'Invalid email or password.'
      invalid_token: 'Invalid authentication token.'
      timeout: 'Your session expired, please login again to continue.'
      inactive: 'Your account was not activated yet.'
    passwords:
      link: 'Forgot password?'
      send_instructions: 'You will receive an email with instructions about how to reset your password in a few minutes.'
      updated: 'Your password was changed successfully. You are now logged in.'
    confirmations:
      link: "Didn't receive confirmation instructions?"
      send_instructions: 'You will receive an email with instructions about how to confirm your account in a few minutes.'
      confirmed: 'Your account was successfully confirmed. You are now logged in.'
    registrations:
      signed_up_but_unconfirmed: 'A message with a confirmation link has been sent to your email address. Please open the link to activate your account.'
      signed_up_but_inactive: 'You have signed up successfully. However, we could not sign you in because your account is not yet activated.'
      signed_up_but_locked: 'You have signed up successfully. However, we could not sign you in because your account is locked.'
    unlocks:
      link: "Didn't receive unlock instructions?"
      send_instructions: 'You will receive an email with instructions about how to unlock your account in a few minutes.'
      unlocked: 'Your account was successfully unlocked. You are now logged in.'
    mailer:
      confirmation_instructions: 'Confirmation instructions'
      reset_password_instructions: 'Reset password instructions'
      unlock_instructions: 'Unlock Instructions'
