# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more, please read the Rails Internationalization guide
# available at http://guides.rubyonrails.org/i18n.html.

en:
  activerecord:
    models:
      characteristic: Characteristic
      an_cool_gaston_ge3ls_file: File
      davidson_blue_ge3ls_file: File
      general_file: File
      measurement_type: Measurement Type
      parent_tree: Mother Tree
      site: Lab
      source: Site
      tree: Tree
      measurement_value: Measurement Value
    attributes:
      tree:
        code: Unique Tree Code
        suc_code: SUC Code
        source: Site
        characteristics: Measurements
        x_coordinate: x
        y_coordinate: y
        treatment: GH Treatment
      parent_tree:
        suc_code: SUC Code
        accession_number: Pedigree Corrected Family
        cone_collected: Parent Cone Collection Date
        location: Parent Location
        latitude: Parent Latitude
        longitude: Parent Longitude
        elevation: Parent Elevation
      data_file:
        data: File to upload
        has_concentrations: This file contains concentration data in CSV format that should be imported into the LIMS
        has_concentration_units: Units
      measurement:
        performed_on: Date Measured
        performed_on_end: Date Measured (End Date)
        amount_used: Amount Used
        amount_used_unit: Amount Used Unit
        assigned_to_id: To Be Analyzed By
        name: Measurement Title
        performed_by_id: Actually Analyzed By
        protocol_id: Measurement Protocol
        performed_on_before_type_cast: Date Measured
        performed_on_end_before_type_cast: Date Measured (End Date)
      sample:
        collected_on: Collected On
        collected_on_end: Collected On (End Date)
        actual_amount: Remaining Amount
        original_amount: Original Amount
        site: Lab
        box: Box/Container
        collected_on_before_type_cast: Collected On
        collected_on_end_before_type_cast: Collected On (End Date)
      user:
        site: Lab
      characteristic:
        age_recorded: Age Recorded
        height_breeding_value: Height Breeding Value
        height_breeding_value_gxe: Height Breeding Value GxE (%height)
        dbh_breeding_value: DBH Breeding Value
        dbh_breeding_value_gxe: DBH Breeding Value GxE (%DBH)
        dbh_position: DBH Position
        status_code: Status Code
        condition_code: Condition Code
        western_gall_rust_six_code: WGR (Code 0-6)
        western_gall_rust_two_code: WGR (Code 0-2)
        white_pine_weevil_score_sum: WPW Sum
        white_pine_weevil_presence: WPW Presence (1) or Absence (0)
        mountain_pine_beetle_presence: MPB
  ransack:
    asc: ascending
    desc: descending
    predicates:
      cont: contains
      not_cont: not contains
      start: starts with
      end: ends with
      gt: greater than
      lt: less than
    models:
      parent_tree: Mother Tree
      source: Site
    attributes:
      tree:
        code: Unique Tree Code
        forward_selected: Forward Selected
        breeding_value_rank: Breeding Value Rank
        label_present: RES-FOR Label on Tree (Y/N)
        res_for_tree: RES-FOR Tree (true/false)
        x_coordinate: x
        y_coordinate: y
        treatment: GH Treatment
      measurement_type:
        short_code: Measurement Type Code
        name: Measurement Type Name
      source: 
        short_code: Site Code
        name: Site Name
      species:
        short_code: Species Code (P/S)
        name: Species Name
      characteristic:
        age_recorded: Age Recorded (yr)
        condition_code: Condition Code
        dbh: DBH (cm)
        dbh_breeding_value: DBH Breeding Value (%DBH)
        dbh_breeding_value_gxe: DBH Breeding Value GxE (%DBH)
        dbh_position: DBH Position
        height: Height (cm)
        height_breeding_value: Height Breeding Value (%height)
        height_breeding_value_gxe: Height Breeding Value GxE (%height)
        status_code: Status Code
        western_gall_rust_six_code: WGR (Code 0-6)
        western_gall_rust_two_code: WGR (Code 0-2)
        white_pine_weevil_score_sum: WPW Sum
        white_pine_weevil_presence: WPW Presence (1) or Absence (0)
        mountain_pine_beetle_presence: MPB
      measurement:
        performed_on: Date Measured
        performed_on_before_type_cast: Date Measured
      measurement_value:
        name: Measurement Name
        value: Measurement Value
