source 'https://rubygems.org'
ruby '2.4.1'

gem 'mysql2'#, '0.4.9'
gem 'bundler', '>= 1.8.4'
gem 'rails', '~> 5.0.0', '>= *******'
gem 'sass-rails', '~> 5.0'
gem 'uglifier', '>= 1.3.0'
gem 'coffee-rails', '~> 4.2'
gem 'jbuilder', '~> 2.5'
gem 'jquery-rails'
gem 'devise'
gem 'devise_invitable'
gem 'paperclip'
gem 'ransack'
gem 'will_paginate'
gem 'simple_form'
gem 'cocoon'
gem 'slim-rails'
gem 'font-awesome-sass'
gem 'bootstrap'
gem 'select2-rails'
gem 'acts-as-taggable-on'
gem 'acts_as_csv' # for advanced search CSV export
gem 'smarter_csv' # for bulk import CSV processing
gem 'simple_calendar', '~> 2.0' 
gem 'paper_trail' # for version control
gem 'active_record_union' # for union queries
gem 'gon' # pass Rails variables into javascript

source 'https://rails-assets.org' do
  gem 'rails-assets-tether', '>= 1.1.0'
  gem 'rails-assets-jstree'
end

group :production do
  gem 'execjs'
#  gem 'therubyracer', require: 'v8'
end

group :development do
  # Access an IRB console on exception pages or by using <%= console %> anywhere in the code.
  gem 'listen', '~> 3.0.5'
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.0.0'
  gem 'capistrano'
  gem 'capistrano-rails', require: false
  gem 'capistrano-bundler', require: false
  gem 'capistrano3-puma', require: false
  gem 'capistrano-rbenv'
  gem 'better_errors'
  gem 'binding_of_caller'
  gem 'rb-readline' # fix readline errors (rails console)
end

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and
  # get a debugger console
  gem 'byebug'
  # Access an IRB console on exception pages or by using
  # <%= console %> in views
  gem 'web-console'
end

group :development, :production do
  gem 'puma'
end
