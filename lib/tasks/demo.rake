require 'active_record/fixtures'

namespace :demo do
  ALL_MODELS = [:sites, :users, :protocols, :trees,
                :groupings, :grouping_assignments, :lab_tests,
                :tree_characteristics, :samples, :measurement_types,
                :measurements, :data_file_types, :data_files,
                :task_statuses, :task_categories,
                :task_priorities, :tasks].freeze

  desc 'Import demo data into database.'
  task :load => [:environment] do |t|
    ALL_MODELS.each do |models|
      puts "Loading #{models}"
      load_fixture models
    end
    load_data_files
  end

  desc 'Create demo data from data in database.'
  task :dump => [:environment] do |t|
    ALL_MODELS.each do |models|
      dump_models models
    end
  end
end

def load_fixture(models)
   ActiveRecord::Fixtures.create_fixtures(File.join(File.dirname(__FILE__), 'demo'), models.to_sym)
end

def load_data_files
  counter = 0
  Tree.all.each do |ts|
    counter += 1
    user = User.find(:first)
    exptype = MeasurementType.first
    s = ts.samples.first || ts.samples.create!(:sample_type => 'urine', :collected_on => Date.today, :barcode => "673#{counter}",
                                               :original_amount => 100.0, :original_unit => 'mL', :building => 'Bio Sci', :room => '235',
                                               :collected_by_id => user.id)
    e = s.measurements.first || s.measurements.create!(:name => "Measurement for #{ts.id}", :measurement_type => exptype,
                                                     :description => "#{exptype.name} Measurement -- Batch upload of Cachexia data.",
                                                     :assigned_to => user, :perform_on => Date.today, :performed_on => Date.today,
                                                     :performed_by => user)

    csv = "#{ts.id}.csv"
    path = File.join(File.dirname(__FILE__), "demo", "data_files", csv)

    f = File.new(path, "r")

    data_file = e.data_files.create!(:data => f, :data_file_type => DataFileType.find_by_name('CSV'), :has_concentrations => true, :has_concentration_units => 'uM')
  end
end

def dump_models(models)
  instances = {}
  models.to_s.singularize.camelize.constantize.find(:all).each do |instance|
    instances.store(instance.to_param, instance.attributes)
  end
  File.open(File.join(File.dirname(__FILE__), "demo/#{models}.yml"), 'w') { |file| file.write(instances.to_yaml) }
end
