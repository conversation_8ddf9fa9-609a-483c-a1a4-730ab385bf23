namespace :import do

  desc "Import greenhouse death dates from TSV file"
  task :greenhouse_deaths => :environment do
    import_file = "data/greenhouse_tree_deaths.txt" # Unique Tree Code | Death Date

    # Parse CSV to extract data
    CSV.foreach(import_file, :headers => true, :col_sep => "\t") do |row|
      tree = Tree.find_by(code: row["Unique Tree Code"])
      tree.tag_list.add("dead")
      tree.notes = "Death date: #{row["Death Date"]}"
      tree.save!
    end
    puts("IMPORT COMPLETE")
  end

  desc "Import greenhouse trees from TSV file"
  task :greenhouse_trees => :environment do
    import_file = "data/greenhouse_trees.txt" # Species | Family | Unique Tree Code | GH Treatment | Block

    # Get values
    pine_species_id = Species.find_by(code: "P").id
    spruce_species_id = Species.find_by(code: "S").id
    greenhouse_source_id = Source.find_or_create_by(name: "Greenhouse", code: "GH").id
    site_admin_id = User.find_by(name: "SiteAdmin").id

    # Parse CSV to extract data
    CSV.foreach(import_file, :headers => true, :col_sep => "\t") do |row|
      greenhouse_tree = Tree.new

      # Grab species ID
      if row["Species"] == "P"
        greenhouse_tree.species_id = pine_species_id
      elsif row["Species"] == "S"
        greenhouse_tree.species_id = spruce_species_id
      end

      # Format family (add zero to the front of 3-digit codes)
      if row["Family"].size < 4
        family = "0#{row["Family"]}"
      else
        family = row["Family"]
      end

      greenhouse_tree.family = family
      greenhouse_tree.code = row["Unique Tree Code"]
      greenhouse_tree.treatment = row["GH Treatment"]
      greenhouse_tree.block = row["Block"]

      greenhouse_tree.source_id = greenhouse_source_id
      greenhouse_tree.res_for_tree = 1
      greenhouse_tree.last_user_updated_id = site_admin_id
      greenhouse_tree.save!
    end
    puts("IMPORT COMPLETE")
  end
  
  desc "Import parent trees from TSV file"
  task :parent_trees => :environment do
    import_file = "data/parent_trees.txt" # Access No | Cone Collected Date | Location | Latitude 1 | Longitude 1 | Elev (m)

    # Parse CSV to extract data
    CSV.foreach(import_file, :headers => true, :col_sep => "\t") do |row|
      parent_tree = ParentTree.new
      parent_tree.code = row["Access No"]
      parent_tree.cone_collected = row["Cone Collected Date"]
      parent_tree.location = row["Location"]
      parent_tree.latitude = row["Latitude 1"]
      parent_tree.longitude = row["Longitude 1"]
      parent_tree.elevation = row["Elev (m)"]
      parent_tree.save!
    end
    puts("IMPORT COMPLETE")
  end

  desc "Import corrected pedigrees from TSV files"
  task :corrected_pedigrees => :environment do
    parent_tree_suc_codes_import_file = "data/mothers_mapping.txt" # Access Number | SUC Code | Species (P/S)
    tree_suc_codes_import_file = "data/tree_suc_codes.txt" # Unique Tree Code | SUC Code | Father Parent Tree | Mother Parent Tree | Species (P/S)
    species_hash = Species.all.map { |item| [item.code, item.id] }.to_h

    # Import SUC Codes for parent trees
    CSV.foreach(parent_tree_suc_codes_import_file, :headers => true, :col_sep => "\t") do |row|
      if parent_tree = ParentTree.find_by(accession_number: row["Access Number"], suc_code: row["SUC Code"])
        # Already exists (do nothing)
      elsif parent_tree = ParentTree.find_by(accession_number: row["Access Number"], suc_code: nil)
        parent_tree.suc_code = row["SUC Code"]
        parent_tree.save!
      else # Access numbers (i.e. families) 1845 and 1785 were split
        parent_tree = ParentTree.new
        parent_tree.suc_code = row["SUC Code"]
        parent_tree.accession_number = row["Access Number"]
        parent_tree.species_id = species_hash[row["Species (P/S)"]]
        parent_tree.save!
      end
    end
    puts("IMPORT COMPLETE FOR PARENT TREES")
    
    # Import SUC Codes and mappings to mother and father trees
    CSV.foreach(tree_suc_codes_import_file, :headers => true, :col_sep => "\t") do |row|
      # Save SUC Code
      if tree = Tree.find_by(code: row["Unique Tree Code"], suc_code: nil)
        tree = Tree.find_by(code: row["Unique Tree Code"])
        tree.suc_code = row["SUC Code"]

        # Save parent trees (create if doesn't exist already)
        if row["Father Parent Tree SUC Code"].to_i != 0
          if !parent_tree = ParentTree.find_by(suc_code: row["Father Parent Tree SUC Code"].to_i)
            parent_tree = ParentTree.new
            parent_tree.suc_code = row["Father Parent Tree SUC Code"]
            parent_tree.species_id = species_hash[row["Species (P/S)"]]
            parent_tree.save!
          end
          tree.father_parent_tree_id = parent_tree.id
        end

        if row["Mother Parent Tree SUC Code"].to_i != 0
          if !parent_tree = ParentTree.find_by(suc_code: row["Mother Parent Tree SUC Code"].to_i)
            parent_tree = ParentTree.new
            parent_tree.suc_code = row["Mother Parent Tree SUC Code"]
            parent_tree.species_id = species_hash[row["Species (P/S)"]]
            parent_tree.save!
          end
          tree.mother_parent_tree_id = parent_tree.id
        end

        tree.save!
      end
    end
    puts("IMPORT COMPLETE FOR SUC Codes")
  end
end
