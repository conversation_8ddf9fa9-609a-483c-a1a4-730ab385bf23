namespace :change do
  task :renumber => :environment do
    files = DataFileType.all
    files.each_with_index do |file, index|
      file.id = index.next
      file.save!
    end
  end

  task :add_new_measurement_type => :environment do
    measurement_type = MeasurementType.find_by(code: "TC")
    measurement_type.description = "Carbon Isotope Discrimination Ratio, Carbon Content"
    measurement_type.save!

    measurement_type = MeasurementType.new
    measurement_type.name = "Wood Quality"
    measurement_type.description = "Density, Microfibril Angle"
    measurement_type.code = "WQ"
    measurement_type.save!

    bulk_import = BulkImport.new
    bulk_import.name = 'measurements/wood_quality'
    bulk_import.description = 'wood quality measurements recorded by <PERSON>itia Da <PERSON>'
    bulk_import.category = 'Measurement'
    bulk_import.save!
  end

  task :change_last_user_updated => :environment do
    Tree.update_all(last_user_updated_id: 8)
  end
end
