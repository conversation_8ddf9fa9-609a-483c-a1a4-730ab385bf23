GEM
  remote: https://rubygems.org/
  remote: https://rails-assets.org/
  specs:
    actioncable (5.0.2)
      actionpack (= 5.0.2)
      nio4r (>= 1.2, < 3.0)
      websocket-driver (~> 0.6.1)
    actionmailer (5.0.2)
      actionpack (= 5.0.2)
      actionview (= 5.0.2)
      activejob (= 5.0.2)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (5.0.2)
      actionview (= 5.0.2)
      activesupport (= 5.0.2)
      rack (~> 2.0)
      rack-test (~> 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (5.0.2)
      activesupport (= 5.0.2)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    active_record_union (1.3.0)
      activerecord (>= 4.0)
    activejob (5.0.2)
      activesupport (= 5.0.2)
      globalid (>= 0.3.6)
    activemodel (5.0.2)
      activesupport (= 5.0.2)
    activerecord (5.0.2)
      activemodel (= 5.0.2)
      activesupport (= 5.0.2)
      arel (~> 7.0)
    activesupport (5.0.2)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (~> 0.7)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    acts-as-taggable-on (5.0.0)
      activerecord (>= 4.2.8)
    acts_as_csv (1.0.0)
    airbrussh (1.1.2)
      sshkit (>= 1.6.1, != 1.7.0)
    arel (7.1.4)
    autoprefixer-rails (6.7.7)
      execjs
    bcrypt (3.1.11)
    better_errors (2.1.1)
      coderay (>= 1.0.0)
      erubis (>= 2.6.6)
      rack (>= 0.9.0)
    bigdecimal (3.0.2)
    binding_of_caller (0.7.2)
      debug_inspector (>= 0.0.1)
    bootstrap (4.0.0.alpha6)
      autoprefixer-rails (>= 6.0.3)
      sass (>= 3.4.19)
    builder (3.2.3)
    byebug (9.0.6)
    capistrano (3.8.0)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (1.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.2)
    capistrano-rails (1.2.3)
      capistrano (~> 3.1)
      capistrano-bundler (~> 1.1)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    capistrano3-puma (3.1.1)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (~> 3.4)
    climate_control (0.1.0)
    cocaine (0.5.8)
      climate_control (>= 0.0.3, < 1.0)
    cocoon (1.2.9)
    coderay (1.1.1)
    coffee-rails (4.2.1)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0, < 5.2.x)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.0.5)
    debug_inspector (0.0.2)
    devise (4.2.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0, < 5.1)
      responders
      warden (~> 1.2.3)
    devise_invitable (1.7.1)
      actionmailer (>= 4.1.0)
      devise (>= 4.0.0)
    erubis (2.7.0)
    execjs (2.7.0)
    ffi (1.15.5)
    font-awesome-sass (4.7.0)
      sass (>= 3.2)
    globalid (0.3.7)
      activesupport (>= 4.1.0)
    gon (6.3.2)
      actionpack (>= 3.0.20)
      i18n (>= 0.7)
      multi_json
      request_store (>= 1.0)
    i18n (0.8.1)
    jbuilder (2.6.3)
      activesupport (>= 3.0.0, < 5.2)
      multi_json (~> 1.2)
    jquery-rails (4.2.2)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    listen (3.0.8)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    loofah (2.0.3)
      nokogiri (>= 1.5.9)
    mail (2.6.4)
      mime-types (>= 1.16, < 4)
    method_source (0.8.2)
    mime-types (3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2016.0521)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_portile2 (2.1.0)
    minitest (5.10.1)
    multi_json (1.12.1)
    mysql2 (0.5.7)
      bigdecimal
    net-scp (1.2.1)
      net-ssh (>= 2.6.5)
    net-ssh (4.1.0)
    nio4r (2.0.0)
    nokogiri (1.7.1)
      mini_portile2 (~> 2.1.0)
    orm_adapter (0.5.0)
    paper_trail (8.1.2)
      activerecord (>= 4.2, < 5.2)
      request_store (~> 1.1)
    paperclip (5.1.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      cocaine (~> 0.5.5)
      mime-types
      mimemagic (~> 0.3.0)
    polyamorous (1.3.1)
      activerecord (>= 3.0)
    puma (3.9.1)
    rack (2.0.1)
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (5.0.2)
      actioncable (= 5.0.2)
      actionmailer (= 5.0.2)
      actionpack (= 5.0.2)
      actionview (= 5.0.2)
      activejob (= 5.0.2)
      activemodel (= 5.0.2)
      activerecord (= 5.0.2)
      activesupport (= 5.0.2)
      bundler (>= 1.3.0, < 2.0)
      railties (= 5.0.2)
      sprockets-rails (>= 2.0.0)
    rails-assets-jquery (3.2.0)
    rails-assets-jstree (3.3.3)
      rails-assets-jquery (>= 1.9.1)
    rails-assets-tether (1.4.0)
    rails-dom-testing (2.0.2)
      activesupport (>= 4.2.0, < 6.0)
      nokogiri (~> 1.6)
    rails-html-sanitizer (1.0.3)
      loofah (~> 2.0)
    railties (5.0.2)
      actionpack (= 5.0.2)
      activesupport (= 5.0.2)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rake (12.0.0)
    ransack (1.8.2)
      actionpack (>= 3.0)
      activerecord (>= 3.0)
      activesupport (>= 3.0)
      i18n
      polyamorous (~> 1.3)
    rb-fsevent (0.9.8)
    rb-inotify (0.9.8)
      ffi (>= 0.5.0)
    rb-readline (0.5.5)
    request_store (1.4.1)
      rack (>= 1.4)
    responders (2.3.0)
      railties (>= 4.2.0, < 5.1)
    sass (3.4.23)
    sass-rails (5.0.6)
      railties (>= 4.0.0, < 6)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    select2-rails (4.0.3)
      thor (~> 0.14)
    simple_calendar (2.2.7)
      rails (>= 3.0)
    simple_form (3.4.0)
      actionpack (> 4, < 5.1)
      activemodel (> 4, < 5.1)
    slim (3.0.7)
      temple (~> 0.7.6)
      tilt (>= 1.3.3, < 2.1)
    slim-rails (3.1.2)
      actionpack (>= 3.1)
      railties (>= 3.1)
      slim (~> 3.0)
    smarter_csv (1.2.3)
    spring (2.0.1)
      activesupport (>= 4.2)
    spring-watcher-listen (2.0.1)
      listen (>= 2.7, < 4.0)
      spring (>= 1.2, < 3.0)
    sprockets (3.7.1)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.0)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    sshkit (1.12.0)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    temple (0.7.7)
    thor (0.19.4)
    thread_safe (0.3.6)
    tilt (2.0.7)
    tzinfo (1.2.2)
      thread_safe (~> 0.1)
    uglifier (3.1.9)
      execjs (>= 0.3.0, < 3)
    warden (1.2.7)
      rack (>= 1.0)
    web-console (2.3.0)
      activemodel (>= 4.0)
      binding_of_caller (>= 0.7.2)
      railties (>= 4.0)
      sprockets-rails (>= 2.0, < 4.0)
    websocket-driver (0.6.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.2)
    will_paginate (3.1.5)

PLATFORMS
  ruby
  x86_64-darwin-16
  x86_64-darwin-17
  x86_64-darwin-18

DEPENDENCIES
  active_record_union
  acts-as-taggable-on
  acts_as_csv
  better_errors
  binding_of_caller
  bootstrap
  bundler (>= 1.8.4)
  byebug
  capistrano
  capistrano-bundler
  capistrano-rails
  capistrano-rbenv
  capistrano3-puma
  cocoon
  coffee-rails (~> 4.2)
  devise
  devise_invitable
  execjs
  font-awesome-sass
  gon
  jbuilder (~> 2.5)
  jquery-rails
  listen (~> 3.0.5)
  mysql2 (~> 0.5.0)
  paper_trail
  paperclip
  puma
  rails (~> 5.0.0, >= *******)
  rails-assets-jstree!
  rails-assets-tether (>= 1.1.0)!
  ransack
  rb-readline
  sass-rails (~> 5.0)
  select2-rails
  simple_calendar (~> 2.0)
  simple_form
  slim-rails
  smarter_csv
  spring
  spring-watcher-listen (~> 2.0.0)
  uglifier (>= 1.3.0)
  web-console
  will_paginate

RUBY VERSION
   ruby 2.4.1p111

BUNDLED WITH
   1.17.3
